- 类名使用 UpperCamelCase 风格（以下情形例外:DO / BO / DTO / VO / AO / PO）
- 方法名、参数名、成员变量、局部变量都统一使用 lowerCamelCase 风格，必须遵从 驼峰形式
- 抽象类命名使用 Abstract(首选) 或 Base 开头;异常类命名使用 Exception 结尾;测试类命名以 Test 结尾，以它要测试的类名结尾，例如“SettingServiceTest”
- POJO 类中布尔类型的变量，都不要加 is 前缀，否则部分框架解析会引起序列化错误。 反例:定义为基本数据类型 Boolean isDeleted;的属性，它的方法也是 isDeleted()，RPC 框架在反向解析的时候，“误以为”对应的属性名称是 deleted，导致属性获取不到，进而抛 出异常
- 类名的命名通常遵循名词在前的原则，因为类代表的是对象或实体，而名词更适合描述这些对象或实体。例如：IndicatorCalcJob
- 方法名的命名通常遵循动词在前的原则，因为方法代表行为或操作。例如：calcIndicator()
- 常量命名全部大写，单词间用下划线隔开，力求语义表达完整清楚，不要嫌名字长
- 不允许任何魔法值(即未经预先定义的常量)直接出现在代码中
- 方法命名：[动作][资源对象名]；例如：submitInvest(...)。常用动作如下表所示：get，返回单个对象. Map对象属于单个对象；find，返回集合类对象。 例如List, Collection对象；search，返回分页对象，比如 IMetaDBQuery，IPage对象；count，用于统计一个总数；remove，逻辑删除；delete，物理删除；match，用于搜索框控件的模糊匹配。传入搜索是搜索文本，返回IPage对象
- 包名统一使用小写。包名统一使用单数形式，格式："com.sinitek.产品名称.模块名称(+).功能"。例如：com.sinitek.sirm.common，或者 com.sinitek.aim.product 包名中的“功能”(“功能”是不可以再有下级 package)，约定如下：controller，控制器，类名以 Controller 为结尾，模块名作为前缀，例如 InvestController；entity，实体。按数据库表命名，大驼峰，例如数据库表 DEMO_INVEST，对应的实体是 DemoInvest。实体对象只出现在Service实现类中，不能作为传入参数或返回对象；service，服务接口类，以 I 开头，以 Service 为结尾；mapper，DB 接口类，以 Mapper 为结尾。资源名作为前缀，一个 Mapper 尽量只操作一个资源，例如 InvestMapper；dto，数据传输对象，类名以 DTO 为结尾；support，模型类。特征是需要对外提供的是对象方法，而不是静态方法；constant，常量。命名规范：[模块][分类]Constant，例如 DemoInvestConstant；当某一类常量较多时可以在模块名后增加分类，例如 DemoInvestErrorCodeConstant；exception，异常，类名以 Exception 为结尾；