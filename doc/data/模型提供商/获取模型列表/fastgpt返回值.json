{"code": 200, "statusText": "", "message": "", "data": [{"type": "stt", "provider": "AliCloud", "model": "SenseVoiceSmall", "name": "SenseVoiceSmall", "isActive": false, "isCustom": false}, {"type": "embedding", "provider": "Other", "model": "bge-m3", "name": "bge-m3", "charsPointsPrice": 0, "inputPrice": "", "outputPrice": "", "isActive": true, "isCustom": false, "contextToken": 8096}, {"type": "rerank", "provider": "BAAI", "model": "bge-reranker-v2-m3", "name": "bge-reranker-v2-m3", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "ChatGLM", "model": "glm-4-air", "name": "glm-4-air", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "ChatGLM", "model": "glm-4-flash", "name": "glm-4-flash", "isActive": true, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "ChatGLM", "model": "glm-4-long", "name": "glm-4-long", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "ChatGLM", "model": "glm-4-plus", "name": "GLM-4-plus", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "ChatGLM", "model": "glm-4v-flash", "name": "glm-4v-flash", "isActive": true, "isCustom": false, "contextToken": 8000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "ChatGLM", "model": "glm-4v-plus", "name": "GLM-4v-plus", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": true, "toolChoice": false}, {"type": "embedding", "provider": "ChatGLM", "model": "embedding-3", "name": "embedding-3", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "llm", "provider": "<PERSON>", "model": "claude-sonnet-4-20250514", "name": "claude-sonnet-4-20250514", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "<PERSON>", "model": "claude-opus-4-20250514", "name": "claude-opus-4-20250514", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "<PERSON>", "model": "claude-3-7-sonnet-20250219", "name": "claude-3-7-sonnet-20250219", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "<PERSON>", "model": "claude-3-5-haiku-20241022", "name": "claude-3-5-haiku-20241022", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "<PERSON>", "model": "claude-3-5-sonnet-20240620", "name": "Claude-3-5-sonnet-20240620", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "<PERSON>", "model": "claude-3-5-sonnet-20241022", "name": "Claude-3-5-sonnet-20241022", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "<PERSON>", "model": "claude-3-opus-20240229", "name": "claude-3-opus-20240229", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "DeepSeek", "model": "deepseek-chat", "name": "Deepseek-chat", "isActive": false, "isCustom": false, "contextToken": 64000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "DeepSeek", "model": "deepseek-reasoner", "name": "<PERSON><PERSON><PERSON>-reasoner", "isActive": false, "isCustom": false, "contextToken": 64000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-Seed-1.6", "name": "Doubao-Seed-1.6", "isActive": false, "isCustom": false, "contextToken": 220000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-Seed-1.6-thinking", "name": "Doubao-Seed-1.6-thinking", "isActive": false, "isCustom": false, "contextToken": 220000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-Seed-1.6-flash", "name": "Doubao-Seed-1.6-flash", "isActive": false, "isCustom": false, "contextToken": 220000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-1.5-lite-32k", "name": "Doubao-1.5-lite-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-1.5-pro-32k", "name": "Doubao-1.5-pro-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-1.5-pro-256k", "name": "Doubao-1.5-pro-256k", "isActive": false, "isCustom": false, "contextToken": 256000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-1.5-vision-pro-32k", "name": "Doubao-1.5-vision-pro-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-lite-4k", "name": "Doubao-lite-4k", "isActive": false, "isCustom": false, "contextToken": 4000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-lite-32k", "name": "Doubao-lite-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-lite-128k", "name": "Doubao-lite-128k", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-vision-lite-32k", "name": "Doubao-vision-lite-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-pro-4k", "name": "Doubao-pro-4k", "isActive": false, "isCustom": false, "contextToken": 4000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-pro-32k", "name": "Doubao-pro-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-pro-128k", "name": "Doubao-pro-128k", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Do<PERSON><PERSON>", "model": "Doubao-vision-pro-32k", "name": "Doubao-vision-pro-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": false}, {"type": "embedding", "provider": "Do<PERSON><PERSON>", "model": "Doubao-embedding-large", "name": "Doubao-embedding-large", "isActive": false, "isCustom": false, "contextToken": 4096}, {"type": "embedding", "provider": "Do<PERSON><PERSON>", "model": "Doubao-embedding", "name": "Doubao-embedding", "isActive": false, "isCustom": false, "contextToken": 4096}, {"type": "llm", "provider": "<PERSON>", "model": "ERNIE-4.0-8K", "name": "ERNIE-4.0-8K", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON>", "model": "ERNIE-4.0-Turbo-8K", "name": "ERNIE-4.0-Turbo-8K", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON>", "model": "ERNIE-Lite-8K", "name": "ERNIE-lite-8k", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON>", "model": "ERNIE-Speed-128K", "name": "ERNIE-Speed-128K", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": false}, {"type": "embedding", "provider": "<PERSON>", "model": "Embedding-V1", "name": "Embedding-V1", "isActive": false, "isCustom": false, "contextToken": 1000}, {"type": "embedding", "provider": "<PERSON>", "model": "tao-8k", "name": "tao-8k", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.5-pro-exp-03-25", "name": "gemini-2.5-pro-exp-03-25", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.5-flash-preview-04-17", "name": "gemini-2.5-flash-preview-04-17", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.0-flash", "name": "gemini-2.0-flash", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.0-pro-exp", "name": "gemini-2.0-pro-exp", "isActive": false, "isCustom": false, "contextToken": 2000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-1.5-flash", "name": "gemini-1.5-flash", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-1.5-pro", "name": "gemini-1.5-pro", "isActive": false, "isCustom": false, "contextToken": 2000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.0-flash-exp", "name": "gemini-2.0-flash-exp", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.0-flash-thinking-exp-1219", "name": "gemini-2.0-flash-thinking-exp-1219", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-2.0-flash-thinking-exp-01-21", "name": "gemini-2.0-flash-thinking-exp-01-21", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Gemini", "model": "gemini-exp-1206", "name": "gemini-exp-1206", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "embedding", "provider": "Gemini", "model": "text-embedding-004", "name": "text-embedding-004", "isActive": false, "isCustom": false, "contextToken": 2000}, {"type": "llm", "provider": "Grok", "model": "grok-3-mini", "name": "grok-3-mini", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Grok", "model": "grok-3-mini-fast", "name": "grok-3-mini-fast", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Grok", "model": "grok-3", "name": "grok-3", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Grok", "model": "grok-3-fast", "name": "grok-3-fast", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Groq", "model": "llama-3.1-8b-instant", "name": "Groq-llama-3.1-8b-instant", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Groq", "model": "llama-3.3-70b-versatile", "name": "Groq-llama-3.3-70b-versatile", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-large", "name": "hunyuan-large", "isActive": false, "isCustom": false, "contextToken": 28000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-lite", "name": "hunyuan-lite", "isActive": false, "isCustom": false, "contextToken": 250000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-pro", "name": "hunyuan-pro", "isActive": false, "isCustom": false, "contextToken": 28000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-standard", "name": "hunyuan-standard", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-turbo-vision", "name": "hunyuan-turbo-vision", "isActive": false, "isCustom": false, "contextToken": 6000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-turbo", "name": "hunyuan-turbo", "isActive": false, "isCustom": false, "contextToken": 28000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "Hunyuan", "model": "hunyuan-vision", "name": "hunyuan-vision", "isActive": false, "isCustom": false, "contextToken": 6000, "vision": true, "toolChoice": false}, {"type": "embedding", "provider": "Hunyuan", "model": "hunyuan-embedding", "name": "hunyuan-embedding", "isActive": false, "isCustom": false, "contextToken": 1024}, {"type": "llm", "provider": "Intern", "model": "internlm2-pro-chat", "name": "internlm2-pro-chat", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Intern", "model": "internlm3-8b-instruct", "name": "internlm3-8b-instruct", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "MiniMax", "model": "MiniMax-Text-01", "name": "MiniMax-Text-01", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "MiniMax", "model": "abab6.5s-chat", "name": "MiniMax-abab6.5s", "isActive": false, "isCustom": false, "contextToken": 245000, "vision": false, "toolChoice": false}, {"type": "tts", "provider": "MiniMax", "model": "speech-01-turbo", "name": "speech-01-turbo", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "MistralAI", "model": "ministral-3b-latest", "name": "Ministral-3b-latest", "isActive": false, "isCustom": false, "contextToken": 130000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "MistralAI", "model": "ministral-8b-latest", "name": "Ministral-8b-latest", "isActive": false, "isCustom": false, "contextToken": 130000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "MistralAI", "model": "mistral-large-latest", "name": "Mistral-large-latest", "isActive": false, "isCustom": false, "contextToken": 130000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "MistralAI", "model": "mistral-small-latest", "name": "Mistral-small-latest", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Moonshot", "model": "moonshot-v1-8k", "name": "moonshot-v1-8k", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Moonshot", "model": "moonshot-v1-32k", "name": "moonshot-v1-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Moonshot", "model": "moonshot-v1-128k", "name": "moonshot-v1-128k", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Moonshot", "model": "moonshot-v1-8k-vision-preview", "name": "moonshot-v1-8k-vision-preview", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Moonshot", "model": "moonshot-v1-32k-vision-preview", "name": "moonshot-v1-32k-vision-preview", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "Moonshot", "model": "moonshot-v1-128k-vision-preview", "name": "moonshot-v1-128k-vision-preview", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "gpt-4.1", "name": "gpt-4.1", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "gpt-4.1-mini", "name": "gpt-4.1-mini", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "gpt-4.1-nano", "name": "gpt-4.1-nano", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "gpt-4o-mini", "name": "GPT-4o-mini", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "gpt-4o", "name": "GPT-4o", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "o4-mini", "name": "o4-mini", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "o3", "name": "o3", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": true, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "o3-mini", "name": "o3-mini", "isActive": false, "isCustom": false, "contextToken": 200000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "o1", "name": "o1", "isActive": false, "isCustom": false, "contextToken": 195000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "OpenAI", "model": "o1-mini", "name": "o1-mini", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "OpenAI", "model": "o1-preview", "name": "o1-preview", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "OpenAI", "model": "gpt-3.5-turbo", "name": "gpt-3.5-turbo", "isActive": false, "isCustom": false, "contextToken": 16000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "OpenAI", "model": "gpt-4-turbo", "name": "gpt-4-turbo", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": true}, {"type": "embedding", "provider": "OpenAI", "model": "text-embedding-3-large", "name": "text-embedding-3-large", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "embedding", "provider": "OpenAI", "model": "text-embedding-3-small", "name": "text-embedding-3-small", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "embedding", "provider": "OpenAI", "model": "text-embedding-ada-002", "name": "text-embedding-ada-002", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "tts", "provider": "OpenAI", "model": "tts-1", "name": "TTS1", "isActive": false, "isCustom": false}, {"type": "stt", "provider": "OpenAI", "model": "whisper-1", "name": "whisper-1", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-max", "name": "<PERSON><PERSON>-max", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-vl-max", "name": "qwen-vl-max", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-plus", "name": "<PERSON>wen-plus", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-vl-plus", "name": "qwen-vl-plus", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-turbo", "name": "Qwen-turbo", "isActive": false, "isCustom": false, "contextToken": 1000000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-235b-a22b", "name": "qwen3-235b-a22b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-32b", "name": "qwen3-32b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-30b-a3b", "name": "qwen3-30b-a3b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-14b", "name": "qwen3-14b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-8b", "name": "qwen3-8b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-4b", "name": "qwen3-4b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-1.7b", "name": "qwen3-1.7b", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen3-0.6b", "name": "qwen3-0.6b", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwq-plus", "name": "qwq-plus", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwq-32b", "name": "qwq-32b", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-coder-turbo", "name": "qwen-coder-turbo", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen2.5-7b-instruct", "name": "qwen2.5-7b-instruct", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen2.5-14b-instruct", "name": "qwen2.5-14b-instruct", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen2.5-32b-instruct", "name": "qwen2.5-32b-instruct", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen2.5-72b-instruct", "name": "Qwen2.5-72B-instruct", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "<PERSON><PERSON>", "model": "qwen-long", "name": "qwen-long", "isActive": false, "isCustom": false, "contextToken": 10000000, "vision": false, "toolChoice": false}, {"type": "embedding", "provider": "<PERSON><PERSON>", "model": "text-embedding-v4", "name": "text-embedding-v4", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "embedding", "provider": "<PERSON><PERSON>", "model": "text-embedding-v3", "name": "text-embedding-v3", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "rerank", "provider": "<PERSON><PERSON>", "model": "gte-rerank-v2", "name": "gte-rerank-v2", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "Siliconflow", "model": "Qwen/Qwen2.5-72B-Instruct", "name": "Qwen/Qwen2.5-72B-Instruct", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": true}, {"type": "llm", "provider": "Siliconflow", "model": "Qwen/Qwen2-VL-72B-Instruct", "name": "Qwen/Qwen2-VL-72B-Instruct", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "Siliconflow", "model": "deepseek-ai/DeepSeek-V2.5", "name": "deepseek-ai/DeepSeek-V2.5", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": true}, {"type": "embedding", "provider": "Siliconflow", "model": "BAAI/bge-m3", "name": "BAAI/bge-m3", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "tts", "provider": "Siliconflow", "model": "FunAudioLLM/CosyVoice2-0.5B", "name": "FunAudioLLM/CosyVoice2-0.5B", "isActive": false, "isCustom": false}, {"type": "tts", "provider": "Siliconflow", "model": "RVC-Boss/GPT-SoVITS", "name": "RVC-Boss/GPT-SoVITS", "isActive": false, "isCustom": false}, {"type": "tts", "provider": "Siliconflow", "model": "fishaudio/fish-speech-1.5", "name": "fish-speech-1.5", "isActive": false, "isCustom": false}, {"type": "stt", "provider": "Siliconflow", "model": "FunAudioLLM/SenseVoiceSmall", "name": "FunAudioLLM/SenseVoiceSmall", "isActive": false, "isCustom": false}, {"type": "rerank", "provider": "Siliconflow", "model": "BAAI/bge-reranker-v2-m3", "name": "BAAI/bge-reranker-v2-m3", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "SparkDesk", "model": "lite", "name": "SparkDesk-lite", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "SparkDesk", "model": "generalv3", "name": "SparkDesk-Pro", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "SparkDesk", "model": "pro-128k", "name": "SparkDesk-Pro-128k", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "SparkDesk", "model": "generalv3.5", "name": "SparkDesk-max", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "SparkDesk", "model": "max-32k", "name": "SparkDesk-max-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "SparkDesk", "model": "4.0Ultra", "name": "SparkDesk-v4.0 Ultra", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1-flash", "name": "step-1-flash", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1-8k", "name": "step-1-8k", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1-32k", "name": "step-1-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1-128k", "name": "step-1-128k", "isActive": false, "isCustom": false, "contextToken": 128000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1-256k", "name": "step-1-256k", "isActive": false, "isCustom": false, "contextToken": 256000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1o-vision-32k", "name": "step-1o-vision-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1v-8k", "name": "step-1v-8k", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-1v-32k", "name": "step-1v-32k", "isActive": false, "isCustom": false, "contextToken": 32000, "vision": true, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-2-mini", "name": "step-2-mini", "isActive": false, "isCustom": false, "contextToken": 8000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-2-16k", "name": "step-2-16k", "isActive": false, "isCustom": false, "contextToken": 16000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON><PERSON><PERSON>", "model": "step-2-16k-exp", "name": "step-2-16k-exp", "isActive": false, "isCustom": false, "contextToken": 16000, "vision": false, "toolChoice": false}, {"type": "tts", "provider": "<PERSON><PERSON><PERSON>", "model": "step-tts-mini", "name": "step-tts-mini", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "<PERSON>", "model": "yi-lightning", "name": "yi-lightning", "isActive": false, "isCustom": false, "contextToken": 16000, "vision": false, "toolChoice": false}, {"type": "llm", "provider": "<PERSON>", "model": "yi-vision-v2", "name": "yi-vision-v2", "isActive": false, "isCustom": false, "contextToken": 16000, "vision": true, "toolChoice": false}, {"type": "embedding", "provider": "<PERSON><PERSON>", "model": "jina-embeddings-v3", "name": "jina-embeddings-v3", "isActive": false, "isCustom": false, "contextToken": 8000}, {"type": "rerank", "provider": "<PERSON><PERSON>", "model": "jina-reranker-v2-base-multilingual", "name": "jina-reranker-v2-base-multilingual", "isActive": false, "isCustom": false}, {"type": "rerank", "provider": "<PERSON><PERSON>", "model": "jina-reranker-m0", "name": "jina-reranker-m0", "isActive": false, "isCustom": false}, {"type": "llm", "provider": "Other", "model": "deepseek-v3", "name": "deepseek-v3", "charsPointsPrice": 0, "inputPrice": "", "outputPrice": "", "isActive": true, "isCustom": true, "contextToken": 65536, "vision": false, "toolChoice": true}]}