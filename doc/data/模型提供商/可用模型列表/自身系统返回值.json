{"code": 200, "statusText": "", "message": "", "data": {"bufferId": null, "feConfigs": {"lafEnv": "https://laf.dev", "mcpServerProxyEndpoint": "", "show_emptyChat": true, "show_git": true, "docUrl": "https://doc.tryfastgpt.ai", "openAPIDocUrl": "https://doc.tryfastgpt.ai/docs/development/openapi", "systemPluginCourseUrl": "https://fael3z0zfze.feishu.cn/wiki/ERZnw9R26iRRG0kXZRec6WL9nwh", "appTemplateCourse": "https://fael3z0zfze.feishu.cn/wiki/CX9wwMGyEi5TL6koiLYcg7U0nWb?fromScene=spaceOverview", "systemTitle": "智搭", "concatMd": "项目开源地址: [FastGPT GitHub](https://github.com/labring/FastGPT)\n交流群: ![](https://oss.laf.run/otnvvf-imgs/fastgpt-feishu1.png)", "limit": null, "scripts": null, "favicon": "/favicon.ico", "uploadFileMaxSize": 500, "isPlus": false, "show_aiproxy": false, "show_coupon": false, "showCustomPdfParse": false, "customPdfParsePrice": 0}, "subPlans": null, "systemVersion": "1.0.0", "activeModelList": [{"provider": "Other", "model": "deepseek-v3", "name": "deepseek-v3", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": null, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "requestAuth": "sk-davb7onvufkrgzlx", "maxContext": 65536, "maxResponse": null, "quoteMaxToken": 8096, "maxTemperature": null, "showTopP": false, "responseFormatList": null, "showStopSign": false, "censor": null, "vision": false, "reasoning": false, "datasetProcess": true, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": true, "functionCall": null, "toolChoice": true, "defaultSystemChatPrompt": "", "defaultConfig": null, "fieldMap": null, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": 0, "inputPrice": null, "outputPrice": null}, {"provider": "Other", "model": "bge-m3", "name": "bge-m3", "type": "embedding", "avatar": null, "isActive": true, "isCustom": true, "isDefault": true, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "requestAuth": "sk-davb7onvufkrgzlx", "maxContext": null, "maxResponse": null, "quoteMaxToken": null, "maxTemperature": null, "showTopP": null, "responseFormatList": null, "showStopSign": null, "censor": null, "vision": null, "reasoning": null, "datasetProcess": null, "usedInClassify": null, "usedInExtractFields": null, "usedInToolCall": null, "functionCall": null, "toolChoice": null, "defaultSystemChatPrompt": null, "defaultConfig": null, "fieldMap": null, "defaultToken": 8096, "maxToken": 8096, "weight": null, "hidden": null, "normalization": false, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": 0, "inputPrice": null, "outputPrice": null}, {"provider": "ChatGLM", "model": "glm-4-flash", "name": "glm-4-flash", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": null, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": null, "requestAuth": null, "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": ["text", "json_object"], "showStopSign": true, "censor": null, "vision": false, "reasoning": null, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "functionCall": false, "toolChoice": true, "defaultSystemChatPrompt": "", "defaultConfig": {}, "fieldMap": {}, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": null, "inputPrice": null, "outputPrice": null}, {"provider": "ChatGLM", "model": "glm-4v-flash", "name": "glm-4v-flash", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": null, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": null, "requestAuth": null, "maxContext": 8000, "maxResponse": 1000, "quoteMaxToken": 6000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": null, "showStopSign": true, "censor": null, "vision": true, "reasoning": null, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "functionCall": false, "toolChoice": false, "defaultSystemChatPrompt": "", "defaultConfig": {}, "fieldMap": {}, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": null, "inputPrice": null, "outputPrice": null}, {"provider": "Other", "model": "czy", "name": "czy", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": false, "isDefaultDatasetTextModel": false, "isDefaultDatasetImageModel": false, "requestUrl": "", "requestAuth": "", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": ["text", "json_object"], "showStopSign": true, "censor": null, "vision": false, "reasoning": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "functionCall": false, "toolChoice": true, "defaultSystemChatPrompt": "", "defaultConfig": {}, "fieldMap": {}, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": 0, "inputPrice": null, "outputPrice": null}], "defaultModels": {"embedding": {"provider": "Other", "model": "bge-m3", "name": "bge-m3", "type": "embedding", "avatar": null, "isActive": true, "isCustom": true, "isDefault": true, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "requestAuth": "sk-davb7onvufkrgzlx", "maxContext": null, "maxResponse": null, "quoteMaxToken": null, "maxTemperature": null, "showTopP": null, "responseFormatList": null, "showStopSign": null, "censor": null, "vision": null, "reasoning": null, "datasetProcess": null, "usedInClassify": null, "usedInExtractFields": null, "usedInToolCall": null, "functionCall": null, "toolChoice": null, "defaultSystemChatPrompt": null, "defaultConfig": null, "fieldMap": null, "defaultToken": 8096, "maxToken": 8096, "weight": null, "hidden": null, "normalization": false, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": 0, "inputPrice": null, "outputPrice": null}, "llm": {"provider": "ChatGLM", "model": "glm-4v-flash", "name": "glm-4v-flash", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": null, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": null, "requestAuth": null, "maxContext": 8000, "maxResponse": 1000, "quoteMaxToken": 6000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": null, "showStopSign": true, "censor": null, "vision": true, "reasoning": null, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "functionCall": false, "toolChoice": false, "defaultSystemChatPrompt": "", "defaultConfig": {}, "fieldMap": {}, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": null, "inputPrice": null, "outputPrice": null}, "datasetTextLLM": {"provider": "ChatGLM", "model": "glm-4v-flash", "name": "glm-4v-flash", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": null, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": null, "requestAuth": null, "maxContext": 8000, "maxResponse": 1000, "quoteMaxToken": 6000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": null, "showStopSign": true, "censor": null, "vision": true, "reasoning": null, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "functionCall": false, "toolChoice": false, "defaultSystemChatPrompt": "", "defaultConfig": {}, "fieldMap": {}, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": null, "inputPrice": null, "outputPrice": null}, "datasetImageLLM": {"provider": "ChatGLM", "model": "glm-4v-flash", "name": "glm-4v-flash", "type": "llm", "avatar": null, "isActive": true, "isCustom": true, "isDefault": null, "isDefaultDatasetTextModel": null, "isDefaultDatasetImageModel": null, "requestUrl": null, "requestAuth": null, "maxContext": 8000, "maxResponse": 1000, "quoteMaxToken": 6000, "maxTemperature": 0.99, "showTopP": true, "responseFormatList": null, "showStopSign": true, "censor": null, "vision": true, "reasoning": null, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "functionCall": false, "toolChoice": false, "defaultSystemChatPrompt": "", "defaultConfig": {}, "fieldMap": {}, "defaultToken": null, "maxToken": null, "weight": null, "hidden": null, "normalization": null, "dbConfig": null, "queryConfig": null, "voices": null, "charsPointsPrice": null, "inputPrice": null, "outputPrice": null}}}}