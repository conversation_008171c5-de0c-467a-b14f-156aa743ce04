{"code": 200, "statusText": "", "message": "", "data": [{"_id": "6874bdcff892b2654b7c0cfd", "avatar": "core/dataset/commonDatasetColor", "name": "索引测试", "intro": "", "type": "dataset", "vectorModel": {"maxTemperature": "", "maxToken": 8096, "outputPrice": "", "charsPointsPrice": 0, "hidden": "", "reasoning": "", "usedInToolCall": "", "type": "embedding", "isActive": true, "responseFormatList": [], "inputPrice": "", "showStopSign": "", "requestAuth": "", "provider": "Other", "normalization": false, "queryConfig": "", "requestUrl": "http://192.168.22.246:9997/v1/embeddings", "quoteMaxToken": "", "usedInExtractFields": "", "defaultConfig": {}, "defaultToken": 8096, "model": "bge-base-zh", "maxContext": "", "maxResponse": "", "dbConfig": "", "toolChoice": "", "fieldMap": "", "defaultSystemChatPrompt": "", "voices": "", "weight": "", "datasetProcess": "", "isDefaultDatasetImageModel": false, "vision": "", "isDefault": false, "isDefaultDatasetTextModel": false, "showTopP": "", "functionCall": "", "name": "bge-base-zh", "censor": "", "usedInClassify": "", "isCustom": true}, "inheritPermission": true, "tmbId": "685e0b4574813aa1df06dbde", "updateTime": "2025-07-14T08:20:31.661Z", "permission": {"isOwner": true, "hasManagePer": true, "hasWritePer": true, "hasReadPer": true, "value": **********, "_permissionList": {"read": {"name": "common:permission.read", "description": "", "value": 4, "checkBoxType": "single"}, "write": {"name": "common:permission.write", "description": "", "value": 6, "checkBoxType": "single"}, "manage": {"name": "common:permission.manager", "description": "", "value": 7, "checkBoxType": "single"}}}, "private": true, "sourceMember": {"name": "Owner", "avatar": "/api/system/img/686ca73df892b2654b7b16ab.jpeg", "status": "active"}}, {"_id": "6864e57474813aa1df07b4e3", "avatar": "core/dataset/commonDatasetColor", "name": "test", "intro": "", "type": "dataset", "vectorModel": {"model": "bge-m3", "name": "bge-m3", "defaultToken": 8096, "maxToken": 8096, "type": "embedding", "outputPrice": "", "charsPointsPrice": 0, "isActive": true, "responseFormatList": [], "inputPrice": "", "requestAuth": "sk-davb7onvufkrgzlx", "provider": "Other", "normalization": false, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "defaultConfig": {}, "isDefault": true, "isCustom": false}, "inheritPermission": true, "tmbId": "685e0b4574813aa1df06dbde", "updateTime": "2025-07-02T07:53:24.023Z", "permission": {"isOwner": true, "hasManagePer": true, "hasWritePer": true, "hasReadPer": true, "value": **********, "_permissionList": {"read": {"name": "common:permission.read", "description": "", "value": 4, "checkBoxType": "single"}, "write": {"name": "common:permission.write", "description": "", "value": 6, "checkBoxType": "single"}, "manage": {"name": "common:permission.manager", "description": "", "value": 7, "checkBoxType": "single"}}}, "private": true, "sourceMember": {"name": "Owner", "avatar": "/api/system/img/686ca73df892b2654b7b16ab.jpeg", "status": "active"}}, {"_id": "685e94d374813aa1df0700e5", "avatar": "core/dataset/commonDatasetColor", "name": "通用知识库", "intro": "", "type": "dataset", "vectorModel": {"model": "bge-m3", "name": "bge-m3", "defaultToken": 8096, "maxToken": 8096, "type": "embedding", "outputPrice": "", "charsPointsPrice": 0, "isActive": true, "responseFormatList": [], "inputPrice": "", "requestAuth": "sk-davb7onvufkrgzlx", "provider": "Other", "normalization": false, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "defaultConfig": {}, "isDefault": true, "isCustom": false}, "inheritPermission": true, "tmbId": "685e0b4574813aa1df06dbde", "updateTime": "2025-06-27T12:55:47.340Z", "permission": {"isOwner": true, "hasManagePer": true, "hasWritePer": true, "hasReadPer": true, "value": **********, "_permissionList": {"read": {"name": "common:permission.read", "description": "", "value": 4, "checkBoxType": "single"}, "write": {"name": "common:permission.write", "description": "", "value": 6, "checkBoxType": "single"}, "manage": {"name": "common:permission.manager", "description": "", "value": 7, "checkBoxType": "single"}}}, "private": true, "sourceMember": {"name": "Owner", "avatar": "/api/system/img/686ca73df892b2654b7b16ab.jpeg", "status": "active"}}, {"_id": "685e94ae74813aa1df0700c1", "avatar": "/imgs/files/folder.svg", "name": "这是一个文件夹", "intro": "", "type": "folder", "vectorModel": {"model": "bge-m3", "name": "bge-m3", "defaultToken": 8096, "maxToken": 8096, "type": "embedding", "outputPrice": "", "charsPointsPrice": 0, "isActive": true, "responseFormatList": [], "inputPrice": "", "requestAuth": "sk-davb7onvufkrgzlx", "provider": "Other", "normalization": false, "requestUrl": "https://cloud.infini-ai.com/maas/v1", "defaultConfig": {}, "isDefault": true, "isCustom": false}, "inheritPermission": true, "tmbId": "685e0b4574813aa1df06dbde", "updateTime": "2025-06-27T12:55:10.157Z", "permission": {"isOwner": true, "hasManagePer": true, "hasWritePer": true, "hasReadPer": true, "value": **********, "_permissionList": {"read": {"name": "common:permission.read", "description": "", "value": 4, "checkBoxType": "single"}, "write": {"name": "common:permission.write", "description": "", "value": 6, "checkBoxType": "single"}, "manage": {"name": "common:permission.manager", "description": "", "value": 7, "checkBoxType": "single"}}}, "private": true, "sourceMember": {"name": "Owner", "avatar": "/api/system/img/686ca73df892b2654b7b16ab.jpeg", "status": "active"}}]}