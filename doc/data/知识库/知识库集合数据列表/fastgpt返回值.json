{"code": 200, "statusText": "", "message": "", "data": {"list": [{"_id": "687714a250e203ae3889440a", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## 部署架构\n\n![microservice-01](../assets/example/microservice-01.jpg)\n\napollo（单主机，ip:*************）+应用（K8S平台）", "a": "", "chunkIndex": 0}, {"_id": "687714a250e203ae3889441f", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## 环境说明\n\n银河证券测试环境：\n\n| 服务名 | 地址 | 负责人 | 说明 |\n| --- | --- | --- | --- |\n| Eureka  | [http://**************:8080/](http://**************:8080/) |  | 服务注册中心 |\n| Apollo  | [http://**************:8070/](http://**************:8070/) |  | 配置中心。apollo / admin |\n| 前端地址 | [http://**************:22308](http://**************:22308) |  | admin / 1234qwer |", "a": "", "chunkIndex": 1}, {"_id": "687714a250e203ae38894425", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## apollo部署\n\n本次使用docker部署，部署顺序，准备mysql数据库，首先部署configservice，再部署adminservice，最后部署portal。", "a": "", "chunkIndex": 2}, {"_id": "687714a250e203ae38894433", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## apollo部署\n### 部署mysql\n\n执行\"docker run -d --name apollo-mysql -p 3306:3306 -e MYSQL_ROOT_PASSWORD=sinitek mysql:5.7\"，等待启动完毕，用客户端连接，数据库用户名密码root/sinitek，端口3306。分别执行数据库创建脚本[configdb.sql](https://github.com/nobodyiam/apollo-build-scripts/blob/master/sql/apolloconfigdb.sqll)和[apolloportaldb.sql](https://github.com/nobodyiam/apollo-build-scripts/blob/master/sql/apolloportaldb.sql)。", "a": "", "chunkIndex": 3}, {"_id": "687714a250e203ae3889443b", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## apollo部署\n### 部署configservice\n\n执行\"docker run -d --net=host --name apollo-configservice -e SPRING_DATASOURCE_URL= ********************************************************************* -e SPRING_DATASOURCE_USERNAME=root -e SPRING_DATASOURCE_PASSWORD=sinitek apolloconfig/apollo-configservice:1.7.1\"，等待执行完成，访问http://**************:8080，验证", "a": "", "chunkIndex": 4}, {"_id": "687714a250e203ae38894415", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## apollo部署\n### 部署adminservice\n\n执行\"docker run -d --net=host --name apollo-adminservice -e SPRING_DATASOURCE_URL= ********************************************************************* -e SPRING_DATASOURCE_USERNAME=root -e SPRING_DATASOURCE_PASSWORD=sinitek apolloconfig/apollo-adminservice:1.7.1\"，等待执行完成，访问http://*************:8090，验证", "a": "", "chunkIndex": 5}, {"_id": "687714a250e203ae3889440d", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## apollo部署\n### 部署portal\n\n执行\"docker run -d --net=host --name apollo-portal -e SPRING_DATASOURCE_URL= ********************************************************************* -e SPRING_DATASOURCE_USERNAME=root -e SPRING_DATASOURCE_PASSWORD=sinitek apolloconfig/apollo-portal:1.7.1\"，等待执行完成，访问http://**************:8070，验证用户名apollo，密码admin后登录成功。", "a": "", "chunkIndex": 6}, {"_id": "687714a250e203ae3889442d", "teamId": "685e0b4574813aa1df06dbd7", "datasetId": "6874bdcff892b2654b7c0cfd", "collectionId": "6877149ff892b2654b7c5cdd", "q": "# 微服务应用部署\n## 应用部署\n\n部署应用和一般的java程序一致，只是jvm参数不同，这里说明：\n\n-Dapp.id=IRP-PDSEND  #这里设置apollo内设置的子应用名称\n\n-Dserver.port=15811 #应用启动端口和外部访问进来的保持一致，K8S需要特别设置，裸机部署不需要\n\n-Dapollo.meta=http://**************:8080  #连接的apollo-configservice地址\n\n-Deureka.instance.prefer-ip-address=true  #开启注册eureka自定义IP，K8S需要特别设置，裸机部署不需要\n\n-Deureka.instance.ip-address=************** #设置子应用连回来IP，K8S需要特别设置，裸机部署不需要\n\n-Deureka.instance.instance-id=**************:15811 #设置子应用实例ID，K8S需要特别设置，裸机部署不需要\n\n-Deureka.instance.status-page-url=http://**************:15811/actuator/info\" #设置检查，K8S需要特别设置，裸机部署不需要", "a": "", "chunkIndex": 7}], "total": 8}}