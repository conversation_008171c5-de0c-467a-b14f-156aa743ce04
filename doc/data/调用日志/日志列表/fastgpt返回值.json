{"data": {"logs": [{"ttfb_milliseconds": 782, "timestamp_trunc_by_hour": 1752123600, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752125762254319", "id": 44, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 2809, "output_tokens": 1876, "total_tokens": 4685}, "created_at": 1752125814441, "request_at": 1752125762254}, {"ttfb_milliseconds": 413, "timestamp_trunc_by_hour": 1752123600, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752124880743371", "id": 43, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 189, "output_tokens": 10, "total_tokens": 199}, "created_at": 1752124881286, "request_at": 1752124880743}, {"ttfb_milliseconds": 421, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752123379363660", "id": 42, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 200, "output_tokens": 19, "total_tokens": 219}, "created_at": 1752123380117, "request_at": 1752123379363}, {"ttfb_milliseconds": 1231, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752123378059344", "id": 41, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 169, "output_tokens": 8, "total_tokens": 177}, "created_at": 1752123379290, "request_at": 1752123378059}, {"ttfb_milliseconds": 312, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752123368571590", "id": 40, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 148, "output_tokens": 16, "total_tokens": 164}, "created_at": 1752123369073, "request_at": 1752123368571}, {"ttfb_milliseconds": 522, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752123352183235", "id": 39, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 131, "output_tokens": 9, "total_tokens": 140}, "created_at": 1752123352773, "request_at": 1752123352183}, {"ttfb_milliseconds": 476, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752123292863815", "id": 38, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 6, "output_tokens": 16, "total_tokens": 22}, "created_at": 1752123293682, "request_at": 1752123292863}, {"ttfb_milliseconds": 235, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4v-flash", "request_id": "1752123292863602", "id": 37, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 60, "output_tokens": 10, "total_tokens": 70}, "created_at": 1752123293287, "request_at": 1752123292863}, {"timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "content": "{\"error\":{\"code\":\"1210\",\"message\":\"API 调用参数有误，请检查文档。 (aiproxy: 1752123255165023)\"}}", "model": "glm-4v-flash", "request_id": "1752123255165023", "id": 36, "channel": 1, "code": 400, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {}, "created_at": 1752123255401, "request_at": 1752123255165}, {"timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "content": "{\"error\":{\"code\":\"1210\",\"message\":\"API 调用参数有误，请检查文档。 (aiproxy: 1752123122696964)\"}}", "model": "glm-4v-flash", "request_id": "1752123122696964", "id": 35, "channel": 1, "code": 400, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {}, "created_at": 1752123122907, "request_at": 1752123122696}, {"timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "content": "{\"error\":{\"code\":\"1210\",\"message\":\"API 调用参数有误，请检查文档。 (aiproxy: 1752123028484521)\"}}", "model": "glm-4v-flash", "request_id": "1752123028484521", "id": 34, "channel": 1, "code": 400, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {}, "created_at": 1752123028701, "request_at": 1752123028484}, {"timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "content": "{\"error\":{\"code\":\"1210\",\"message\":\"API 调用参数有误，请检查文档。 (aiproxy: 1752122932253052)\"}}", "model": "glm-4v-flash", "request_id": "1752122932253052", "id": 33, "channel": 1, "code": 400, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {}, "created_at": 1752122932420, "request_at": 1752122932253}, {"ttfb_milliseconds": 131, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4v-flash", "request_id": "1752121707124517", "id": 32, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 60, "output_tokens": 10, "total_tokens": 70}, "created_at": 1752121707409, "request_at": 1752121707124}, {"ttfb_milliseconds": 489, "timestamp_trunc_by_hour": 1752120000, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752121701151021", "id": 31, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 6, "output_tokens": 30, "total_tokens": 36}, "created_at": 1752121702610, "request_at": 1752121701151}, {"ttfb_milliseconds": 836, "timestamp_trunc_by_hour": 1752116400, "endpoint": "/v1/chat/completions", "model": "glm-4-flash", "request_id": "1752118457555001", "id": 30, "channel": 1, "code": 200, "mode": 1, "ip": "**********", "downstream_result": true, "price": {}, "usage": {"input_tokens": 2820, "output_tokens": 1661, "total_tokens": 4481}, "created_at": 1752118503274, "request_at": 1752118457555}], "total": 55}, "success": true}