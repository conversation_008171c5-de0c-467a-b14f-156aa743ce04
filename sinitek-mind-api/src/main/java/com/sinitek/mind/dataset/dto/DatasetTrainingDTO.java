package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@Schema(description = "知识库训练数据DTO")
public class DatasetTrainingDTO {

    @Schema(description = "记录ID")
    private String _id;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "租户ID")
    private String tmbId;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "集合ID")
    private String collectionId;

    @Schema(description = "数据Id")
    private String dataId;

    @Schema(description = "账单ID")
    private String billId;

    @Schema(description = "模式")
    private String mode;

    @Schema(description = "文本内容")
    private String text;

    @Schema(description = "chunk索引")
    private Integer chunkIndex;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "重试次数")
    private Integer retryCount;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "问题")
    private String q;

    @Schema(description = "答案")
    private String a;

    @Schema(description = "索引大小")
    private Integer indexSize;

    @Schema(description = "权重")
    private Integer weight;

    @Schema(description = "索引列表")
    private List<Object> indexes;

    @Schema(description = "过期时间")
    private Date expireAt;

    @Schema(description = "锁定时间")
    private Date lockTime;

    @Schema(description = "版本号")
    private Integer __v;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}