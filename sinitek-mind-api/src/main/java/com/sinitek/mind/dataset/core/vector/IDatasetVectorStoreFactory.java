package com.sinitek.mind.dataset.core.vector;

import org.springframework.ai.vectorstore.VectorStore;

/**
 * 知识库向量存储工厂-接口层
 *
 * <AUTHOR>
 * date 2025-07-26
 */
public interface IDatasetVectorStoreFactory {

    /**
     * 通过databaseId获取VectorStore
     */
    VectorStore getVectorStoreByDatabaseId(String databaseId);

    /**
     * 通过vectorModel获取VectorStore
     */
    VectorStore getVectorStoreByVectorModel(String vectorModel);
}
