---
description: 
globs: 
alwaysApply: true
---
代码风格：
  1、每个属性之间需要有一行空格隔开。
  2、每个方法之间需要有一行空格隔开。
  3、if 语句的括号不能省略。
  4、不使用restful风格中参数作为url的一部分，url是明确的
  5、每个实体类、DTO的属性都需要通过Swagger注解说明属性作用
  6、比如： 获取属性param.getParentId()会用到多次就先 String parentId = param.getParentId() 获取到parentId，然后在parentId进行后续的处理
  7、每个DTO都是单独的类，不要使用子类DTO的方式
  8、public方法放在类上面，praivate方法放在public方法的下面

要求:

* java代码的顶层目录是 com.sinitek.mind

* 项目中数据库使用的是mongodb

* 每个功能放入自己专属的包下, 例如大模型相关的则放入com.sinitek.mind.model、用户相关的则放入com.sinitek.mind.user、公共类的放入com.sinitek.mind.common

* Swagger注解用 @Schema(description = "字段的描述")

* 所有java类,代码风格上属性与属性、方法与方法、获取属性与类名、方法与类名之间都需要有一行空格

* dto的set、get能用lombok的@Data注解则优先用

* 判空优先使用工具类，例如: 判断集合不能为空使用CollectionUtils.isNotEmpty、判断字符串不能为空使用StringUtils.isNotBlank、判断对象不能为空ObjectUtils.isNotEmpty等等

* 全部的类应该加上作者和时间和描述，在类名上面加载，效果如下。注意描述根据实际情况进行增加例如 模型Controller层、模型Service层等等，date 2025-07-02 应使用代码生成时实际的当前时间，你可以通过执行本地命令date来获取到系统当前时间



文件头部规则
  1、每个文件创建时都需要加上作者、注释、时间，效果如下
  /**
    * 模型类型常量
    *
    * <AUTHOR>
    * date 2025-07-01
    */
  2、其中时间date需要调用系统当前时间来获取

