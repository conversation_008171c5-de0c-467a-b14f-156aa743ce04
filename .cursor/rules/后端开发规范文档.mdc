---
description: 后端代码生成参考文档
alwaysApply: true
---
# 携宁后端开发规范文档（AI代码生成专用）

## 1. 包结构规范

### 标准包结构
```
com.sinitek.[项目名称]
├── dao/          # 持久化层
├── controller/   # 控制器层
├── service/      # 业务逻辑层
│   └── impl/     # 业务逻辑实现
├── mapper/       # 数据访问层
├── entity/       # 实体类
├── dto/          # 数据传输对象
├── po/           # 持久化对象
├── config/       # 配置类
├── util/         # 工具类
├── exception/    # 异常类
├── constant/     # 常量定义
├── support/      # 功能实现支持类
├── event/        # 当前模块声明的事件
├── enumation/    # 枚举类
└── listener/     # 当前模块监听的事件
```

### 包约束规则
- **【强制】** service包下实现类如果想实现持久化必须调用dao中方法
- **【强制】** dao中出参入参只能是po包下的类或者entity下的类
- **【强制】** service中出参入参只能是dto包下的类
- **【强制】** mapper包下接口必须以`所属Entity名称+Mapper`命名
- **【强制】** entity包下的实体类必须显式的使用`@TableName`注解声明自己对应的表,且必须声明一个静态允许公开访问的字符串常量`ENTITY_NAME`,对应值为对应表全大写后的值

## 2. 命名规范

### 类命名约定
- **【强制】** controller包下的类命名必须以Controller结尾
- **【强制】** 除mapper包外,接口命名必须以I开头,以Service结尾
- **【强制】** service.impl包下类命名必须以ServiceImpl结尾
- **【强制】** entity包下类命名必须以Entity结尾
- **【强制】** dto包下的类命名必须以DTO结尾
- **【强制】** po包下的类命名必须以PO结尾
- **【强制】** util包下的类命名必须以Util结尾
- **【强制】** event包下类命名必须以Event结尾
- **【强制】** listener包下类命名必须以Listener结尾
- **【强制】** enumation包下类命名必须以Enum结尾

### 属性命名约定
- **【强制】** Boolean属性必须使用`xxx_flag`/`xxxFlag`命名,不允许使用`is_xxx`/`isXxx`
- **【强制】** 时间,日期必须使用`java.util.Date`

### lombok使用约定
- **【强制】** 存在集成的类,必须标注`@EqualsAndHashCode(callSuper = true)`
- **【强制】** util类,必须标注`@AllArgsConstructor(access = AccessLevel.PRIVATE)`
- **【强制】** 所有dto,entity,po必须标注`@Data`,`@SuperBuilder`,`@NoArgsConstructor`,`@AllArgsConstructor`

### swagger使用约定
- **【强制】** 所有dto,entity,po类必须使用`@ApiModel(description = "xxx")`标注,属性使用`@ApiModelProperty("xxx")`标注

## 3. 接口规范

### 接口URL规则
后端接口的URL按如下规则进行编制：
```
/frontend/api/[通用分类]/[模块]/[功能]
```

#### 通用分类
| 分类 | 说明 |
|-----|------|
| `/frontend/api` | 表示应用数据接口 |
| `/frontend/api/remote` | 表示接口对外提供服务 |
| `/frontend/api/open-api` | 表示接口对第三方系统提供服务 |

#### 重定向URL
针对后端进行重定向的场景，URL按如下规则：
```
/pages/[模块]/[功能]
```

#### 常用动词
| 动词 | 说明 |
|------|------|
| list | 查询 |
| draft | 保存草稿，记录未生效 |
| save | 确定，无需流程，数据已生效 |
| submit | 提交，发起流程 |
| delete | 删除 |
| detail | 详细 |
| load | 加载 |
| match | 用于搜索框控件的模糊匹配 |

### 统一响应格式
```java
// 统一响应格式
public class RequestResult<T> {
    private boolean success;
    private String message;
    private String code;
    private T data;
}
```

### 参数验证框架
**【强制要求】** Controller接口必须根据实际需求添加参数校验，验证框架使用hibernate-validator结合Spring的BeanValidation。

#### Controller中的使用
```java
@PostMapping("/save")
@ApiOperation("保存用户信息")
public RequestResult<Void> saveUser(@RequestBody @Valid @ApiParam UserCreateDTO userCreateDTO) {
    userService.saveUser(userCreateDTO);
    return RequestResult.success();
}
```

## 4. 数据库规范

### 数据库表设计规约
#### 表命名规则
- **【强制】** 数据库表名是名词，例如【产品变更注册】业务，可以翻译成`product_register_change`
- **【强制】** 数据库表名单词与单词之间使用'_'进行分割，对应Java对象类名则将'_'去掉改成大驼峰
- **【强制】** 表名不使用复数名词
- **【强制】** 数据库表名尽量不要以`info`结尾
- **【推荐】** 表的命名最好是加上"业务名称_表的作用"

### 字段规约
#### 数据类型映射表
| 类型 | Java类型 | MySQL | Oracle | PostgreSQL | 备注 |
|------|----------|-------|--------|------------|------|
| 固定字符串 | String | char | char | char | 比如是否类，用char(1) |
| 变长字符串 | String | varchar | varchar2 | varchar | |
| 布尔型 | Integer | tinyint | number(4) | int2 | |
| 短整型 | Integer | tinyint | number(4) | int2 | |
| 整型 | Integer | int | number(10) | int4 | |
| 长整型 | Long | bigint | number(20) | int8 | |
| 浮点型 | BigDecimal | decimal(a,b) | number(a,b) | numeric(a,b) | |
| 日期 | Date | date | date | date | |
| 日期时间 | Date | datetime | timestamp | timestamp | |
| 大文本 | String | longtext | clob | text | |

#### 布尔字段规范
- **【强制】** 必须使用 `xxx_flag` 的方式命名
- 取值：tinyint(1表示是，0表示否)

#### 浮点数字段规范
**存储要求：**
- 金额：统一按【元】进行存储
- 百分比：统一按去掉百分号的形式存储（除以100）

**小数位数推荐：**
| 场景 | DB小数位数 | DB字段数据类型 | 备注 |
|------|------------|----------------|------|
| 金额 | 4（默认）, 6 | decimal(19,4), decimal(19,6) | 证监会监管报送中按【元】进行存储 |
| 百分比 | 6 | decimal(19,6) | 按除以100进行存储 |
| 收益率 | 19 | decimal(32,19) | Excel中最大小数位数是19位 |

#### 常用字段设计
**通用字段：**
| 字段 | 类型 | 备注 |
|------|------|------|
| name | varchar(?) | 显示名称 |
| title | varchar(?) | 标题 |
| code | varchar(50) | 内部编码 |
| thread_id | bigint | 线索ID |
| parent_id | bigint | 上级记录ID |
| sort | int | 排序字段 |

**时间字段：**
| 字段 | 类型 | 备注 |
|------|------|------|
| year | int | 年份，格式：YYYY |
| month | tinyint | 月份，取值：1,2,...,12 |
| quarter | tinyint | 季度，取值：1,2,3,4 |
| *_date | date | 带日期 |
| *_time | datetime | 带时分秒 |

**状态字段：**
| 字段 | 类型 | 备注 |
|------|------|------|
| status | int | 业务数据状态 |
| approve_status | int | 审批状态，应用于审批模型 |

**标识字段：**
| 字段 | 类型 | 备注 |
|------|------|------|
| latest_flag | tinyint | 最新标识，1=最新, 0=否 |
| attachment_flag | tinyint | 附件标识，1=有附件, 0=无附件 |
| remove_flag | tinyint | 逻辑删除标识，1=删除, 0=有效 |

### 索引规约
- **【强制】** 索引名为"i_数据库表<两位顺序号>"，例如：`i_sirm_setting01`
- **【强制】** 业务上具有唯一特性的字段，必须建成唯一索引，命名：`uk_数据库表名<两位顺序号>`

### SQL语句规范
#### JOIN语句规范
- **【强制】** on中只能进行字段的关联，不允许进行字段的筛选
- **【强制】** on部分需要括号括起来
- **【推荐】** 在保证结果正确性的基础上尽量让参与关联查询的自查询数据量最小

```sql
-- 反例
FROM wf_process p 
LEFT JOIN wf_processlist pl ON pl.name = 'ProcessStatus' AND p.status = pl.key

-- 正例
FROM wf_process p 
LEFT JOIN (
    SELECT * FROM wf_processlist WHERE name = 'ProcessStatus'
) pl ON (p.status = pl.key)
```

#### 查找最新数据
使用NOT EXISTS来处理：
```sql
-- 模板
SELECT * FROM 数据库表 a 
WHERE NOT EXISTS (
    SELECT * FROM 数据库表 aa 
    WHERE a.group_field = aa.group_field 
    AND a.时间字段 < aa.时间字段
)
```

## 5. 异常处理规范

### 异常分类
#### 业务异常
```java
// 业务异常，用于向前端显示错误信息
public class BusinessException extends RuntimeException {
    private String code;
    private String message;
    
    public BusinessException(String message) {
        super(message);
        this.message = message;
    }
    
    public BusinessException(String code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }
}
```

#### 系统异常
```java
// 系统异常，用于系统内部错误
public class SirmException extends RuntimeException {
    public SirmException(String message) {
        super(message);
    }
    
    public SirmException(String message, Throwable cause) {
        super(message, cause);
    }
}
```

### 异常消息规范
- **【强制】** 每个大的业务模块下必须在 resources\message 创建 messageCode 与具体消息内容的映射文件
    messages-【模块简称】_zh_CN.properties
- **【强制】** messageCode推荐包名的方式，例如:
```properties
com.sinitek.sirm.common.i18n.locale_can_not_null=locale不能为空
```
- **【强制】** Controller层不允许抛出异常

## 6. MyBatis Plus使用规范

### 实体类设计
#### 基础实体类
- **【强制】** 除非有特殊声明,否则 entity 强制继承自 BaseEntity
- **【强制】** id(主键)无需手动赋值,数据保存时会自动填充
- **【强制】** createTimeStamp(创建时间)无需手动赋值,数据新增时会自动填充,后续更新不会修改这个值
- **【强制】** updateTimeStamp(修改时间)无需手动赋值,数据每次更新时会自动填充
- **【强制】** version(乐观锁)无需手动赋值,数据保存/更新时会自动填充

```java
@Data
@TableName(value = "user")
@EqualsAndHashCode(callSuper = true)
@ApiModel("用户信息实体")
public class User extends BaseEntity {
    
    public static final String ENTITY_NAME = "USER";
    
    @ApiModelProperty(value = "名称")
    private String name;
    
    @ApiModelProperty(value = "年龄")
    private Integer age;
}
```

#### 实体基础类汇总
**BaseEntity 基础实体：**
- 包含：id(主键)、createTimeStamp(创建时间)、updateTimeStamp(修改时间)、version(乐观锁)

**BaseAuditEntity 基础审计实体：**
- 继承BaseEntity的内容
- 额外包含：creatorId(创建者)、updaterId(修改者)
- 特性：creatorId在insert数据时自动填充、updaterId在update数据时自动填充

### Mapper.xml规范
- **【强制】** 如果没有特殊说明,生成 mapper.xml 文件时不要生成 resultMap
- **【强制】** xml中如果碰到`>`,`<`,`>=`,`<=`一律写为`<![CDATA[ xx ]]>`

#### Like模糊查询推荐写法
```xml
<if test="name != null and name != ''">
    <bind name="name_like" value="@com.sinitek.sirm.common.utils.SQLUtils@like(name)"/>
    name LIKE #{name_like} ESCAPE '/'
</if>
```

**SQLUtils方法说明：**
| 方法名称 | 方法作用 |
|----------|----------|
| SQLUtils@escape(value) | 将输入的参数转义，如%转义为/% |
| SQLUtils@like(value) | 调用escape方法转义输入的参数，同时参数两边拼接上% |
| SQLUtils@likeLeft(value) | 调用escape方法转义输入的参数，同时参数左边拼接上% |
| SQLUtils@likeRight(value) | 调用escape方法转义输入的参数，同时参数右边拼接上% |

#### Order By推荐写法
```xml
<if test="@org.apache.commons.lang.StringUtils@isBlank(params.orderName)">
    ORDER BY create_time_stamp DESC
</if>
```

### Service层规范
- **【强制】** 存在api模块,service接口应该定义在api模块下,实现类定义在业务模块下
- **【强制】** 实现类中对应实体的dao必须简单命名为`dao`
- **【强制】** 若无特殊说明,设计数据新增，更新，删除的 Service 类的方法必须使用 `@Transactional(rollbackFor = Exception.class)` 注解

```java
@Slf4j
@Service
public class UserServiceImpl implements IUserService {
    
    @Autowired
    private UserDao userDao;
    
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveOrUpdateUser(User user) {
        userDao.saveOrUpdate(user);
    }
    
    @Service
    static class UserDao extends ServiceImpl<UserMapper, User> {
    }
}
```

### 分页查询规范
#### Controller分页例子
```java
@GetMapping("/user/page")
public TableResult page(PageDataParam pageDataParam) {
    Page page = pageDataParam.buildPage();
    IPage<User> iPage = userService.selectAllPage(page, "");
    return pageDataParam.build(iPage);
}
```

## 7. Swagger文档规范

**【强制要求】** 所有后端接口必须使用Swagger进行文档化。

### 接口文档注解
#### @Api 注解
```java
@RequestMapping("/frontend/api/demo/invest")
@Api(value="/frontend/api/demo/invest", tags="Demo-投资信息接口")
@Controller
public class DemoInvestController {
}
```

#### @ApiOperation 注解
```java
@PostMapping("/load")
@ApiOperation("加载投资信息")
public RequestResult<InvestResultDTO> loadInvest(@RequestBody @ApiParam LoadInvestDTO loadInvestDTO) {
    // 方法实现
}
```

#### @ApiParam 注解
```java
// 基本数据类型需要添加属性名称
@PostMapping("/delete")
@ApiOperation("删除投资信息")
public RequestResult<Void> deleteInvest(@RequestBody @ApiParam("记录id集合") Long[] ids) {
    // 方法实现
}
```

## 8. 缓存规范

### @Cacheable规范
**【强制】** 缓存Key命名规范：
```java
@Cacheable(value = com.sinitek.[产品].[模块:可以有多个模块].[方法名],
           key = 多个参数之间用'_'分隔)
public String 方法名(参数) {
    ...
}

// 例如
@Cacheable(value = 'com.sinitek.sirm.tenant.mechanism.getMechanismNameByCode',
           key = "#code")
public String getMechanismNameByCode(String code) {
    ...
}
```

