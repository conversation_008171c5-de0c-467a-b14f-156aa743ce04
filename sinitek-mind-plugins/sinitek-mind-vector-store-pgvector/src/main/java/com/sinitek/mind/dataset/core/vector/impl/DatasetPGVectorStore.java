//package com.sinitek.mind.dataset.core.impl;
//
//import com.sinitek.mind.dataset.core.vector.IDatasetVectorStore;
//import com.sinitek.sirm.common.spring.SpringFactory;
//import org.springframework.ai.embedding.EmbeddingModel;
//import org.springframework.ai.vectorstore.VectorStore;
//import org.springframework.ai.vectorstore.pgvector.PgVectorStore;
//import org.springframework.jdbc.core.JdbcTemplate;
//import org.springframework.stereotype.Service;
//
///**
// * PGVector向量存储
// *
// * <AUTHOR>
// * date 2025-07-22
// */
//@Service
//public class DatasetPGVectorStore implements IDatasetVectorStore {
//
//    @Override
//    public VectorStore buildVectorStore(EmbeddingModel embeddingModel) {
//        JdbcTemplate vectorJdbcTemplate = SpringFactory.getBean("vectorJdbcTemplate", JdbcTemplate.class);
//        PgVectorStore.PgVectorStoreBuilder pgVectorStoreBuilder = PgVectorStore.builder(vectorJdbcTemplate, embeddingModel)
//                .vectorTableName("vector_example");
//
//        return pgVectorStoreBuilder.build();
//    }
//}
