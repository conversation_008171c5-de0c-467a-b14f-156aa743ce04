//package com.sinitek.mind.dataset.config;
//
//import com.alibaba.druid.pool.DruidDataSource;
//import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceWrapper;
//import org.springframework.beans.factory.annotation.Qualifier;
//import org.springframework.boot.context.properties.ConfigurationProperties;
//import org.springframework.context.annotation.Bean;
//import org.springframework.context.annotation.Configuration;
//import org.springframework.context.annotation.Primary;
//import org.springframework.jdbc.core.JdbcTemplate;
//
//import javax.sql.DataSource;
//
///**
// * 知识库配置类
// *
// * <AUTHOR>
// * date 2025-07-22
// */
//@Configuration
//public class DatasetConfig {
//
//    /**
//     * 默认的数据源
//     * @return
//     */
//    @Bean
//    @Primary
//    public DruidDataSourceWrapper dataSource() {
//        return new DruidDataSourceWrapper();
//    }
//
//    /**
//     * 向量数据源
//     * @return
//     */
//    @Bean
//    @ConfigurationProperties("spring.datasource.vector")
//    public DataSource vectorDataSource() {
//        return new DruidDataSource();
//    }
//
//    /**
//     * 向量数据库专用JdbcTemplate
//     * @param vectorDataSource
//     * @return
//     */
//    @Bean(name = "vectorJdbcTemplate")
//    public JdbcTemplate vectorJdbcTemplate(@Qualifier("vectorDataSource") DataSource vectorDataSource) {
//        return new JdbcTemplate(vectorDataSource);
//    }
//}
