package com.sinitek.mind.dataset.core.config;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.ConnectParam;
import org.springframework.ai.vectorstore.milvus.autoconfigure.MilvusServiceClientProperties;
import org.springframework.ai.vectorstore.milvus.autoconfigure.MilvusVectorStoreProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.context.properties.PropertyMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * milvusClient配置类
 *  - 不使用SpringBoot的自动装配，因为AI平台会存在多个向量源
 *
 * <AUTHOR>
 * date 2025-07-24
 */
@Configuration
@EnableConfigurationProperties({ MilvusServiceClientProperties.class, MilvusVectorStoreProperties.class })
public class MilvusConfig {

    @Bean
    @ConditionalOnMissingBean
    public MilvusServiceClient milvusClient(MilvusVectorStoreProperties serverProperties,
                                            MilvusServiceClientProperties clientProperties) {

        var builder = ConnectParam.newBuilder()
                .withHost(clientProperties.getHost())
                .withPort(clientProperties.getPort())
                .withDatabaseName(serverProperties.getDatabaseName())
                .withConnectTimeout(clientProperties.getConnectTimeoutMs(), TimeUnit.MILLISECONDS)
                .withKeepAliveTime(clientProperties.getKeepAliveTimeMs(), TimeUnit.MILLISECONDS)
                .withKeepAliveTimeout(clientProperties.getKeepAliveTimeoutMs(), TimeUnit.MILLISECONDS)
                .withRpcDeadline(clientProperties.getRpcDeadlineMs(), TimeUnit.MILLISECONDS)
                .withSecure(clientProperties.isSecure())
                .withIdleTimeout(clientProperties.getIdleTimeoutMs(), TimeUnit.MILLISECONDS)
                .withAuthorization(clientProperties.getUsername(), clientProperties.getPassword());

        if (clientProperties.isSecure()) {
            PropertyMapper mapper = PropertyMapper.get();
            mapper.from(clientProperties::getUri).whenHasText().to(builder::withUri);
            mapper.from(clientProperties::getToken).whenHasText().to(builder::withToken);
            mapper.from(clientProperties::getClientKeyPath).whenHasText().to(builder::withClientKeyPath);
            mapper.from(clientProperties::getClientPemPath).whenHasText().to(builder::withClientPemPath);
            mapper.from(clientProperties::getCaPemPath).whenHasText().to(builder::withCaPemPath);
            mapper.from(clientProperties::getServerPemPath).whenHasText().to(builder::withServerPemPath);
            mapper.from(clientProperties::getServerName).whenHasText().to(builder::withServerName);
        }

        return new MilvusServiceClient(builder.build());
    }

}
