package com.sinitek.mind.dataset.core.vector.impl;

import io.milvus.client.MilvusServiceClient;
import io.milvus.param.IndexType;
import io.milvus.param.MetricType;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.ai.vectorstore.milvus.MilvusVectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Milvus向量存储
 *
 * <AUTHOR>
 * date 2025-07-24
 */
@Service
public class DatasetMilvusVectorStore extends AbstractDatasetVectorStore {

    @Autowired
    private MilvusServiceClient milvusServiceClient;

    @Override
    public VectorStore buildVectorStore(EmbeddingModel embeddingModel) {
        int dimensions = embeddingModel.dimensions();
        return MilvusVectorStore.builder(milvusServiceClient, embeddingModel)
                .collectionName("dataset_store")
                .embeddingDimension(dimensions)
                .indexType(IndexType.IVF_FLAT)
                .metricType(MetricType.COSINE)
                .initializeSchema(true)
                .build();
    }
}
