# 携宁智脑

## 模块结构

``` lua
sinitek-mind-backend
├── sinitek-mind-api -- 基础API模块：供其他模块可使用的api
├── sinitek-mind-app -- 应用启动模块：放核心业务逻辑
├── sinitek-mind-plugins -- 插件模块：供可插拔选择的功能
|    ├── sinitek-mind-plugins-vector-store-milvus -- 对接milvus向量数据库(默认使用)
|    ├── sinitek-mind-vector-store-pgvector -- 对接pgvector向量数据库
```

## 核心技术栈

> 后端框架

Spring Boot - 主要应用框架

Spring Cloud - 微服务架构支持

Spring AI (1.0.0) - AI集成框架

MyBatis Plus - ORM框架

> 前端框架

vue3 + Reactflow


> AI/机器学习

大模型集成 - openai、ollama、xinference

JTokkit (0.3.0) - Token计算工具


> 数据存储

Mysql - 主要数据存储（支持切换）

PGvector - 向量数据库（支持切换Milvus、Chroma、Es、Weavuate、Oracle、Redis等）

MongoDB - fastgpt数据存储 (前期使用)



> 部署与运维

Docker - 容器化部署 (基于JDK17镜像)

Jenkins - 自动化构建及部署






## 目录结构

com.sinitek.mind




## 混合部署方式

1. 通过Jenkins打包成docker镜像
2. 测试环境直接通过docker-compose运行镜像
3. nginx统一配置反向代理


