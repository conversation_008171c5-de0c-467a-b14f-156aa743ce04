package com.sinitek.mind.base.properties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 单元测试的配置类
 *
 * <AUTHOR>
 * @date 2022/9/16
 */
@Data
@Component
@ConfigurationProperties(prefix = "sinicube.test")
public class SiniCubeTestProperties {

    @ApiModelProperty("admin用户的密码")
    private String adminPw;
}
