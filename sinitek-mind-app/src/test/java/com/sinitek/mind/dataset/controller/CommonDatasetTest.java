package com.sinitek.mind.dataset.controller;

import com.jayway.jsonpath.JsonPath;
import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.dataset.constant.DatasetConstant;
import com.sinitek.mind.dataset.constant.DatasetTestConstant;
import com.sinitek.mind.dataset.dto.BaseDatasetCollectionParamsDTO;
import com.sinitek.mind.dataset.dto.DatasetCreateRequest;
import com.sinitek.mind.dataset.dto.FileIdCreateDatasetCollectionParamsDTO;
import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.model.dto.ModelUpdateRequest;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.model.utils.ModelTestDataUtil;
import com.sinitek.sirm.common.utils.JsonUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.platform.commons.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.hamcrest.Matchers.notNullValue;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * 知识库单元测试通用逻辑层
 *
 * <AUTHOR>
 * date 2025/07/25
 */
@Slf4j
public class CommonDatasetTest extends MindBaseTestApplication {

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private ISystemModelService systemModelService;

    /**
     * 初始化测试数据
     */
    @SneakyThrows
    @BeforeEach
    public void initTestData() {
        // 确保有LLM模型
        SystemModelDTO systemModelDTO = systemModelService.getModelByModelId(ModelTestDataUtil.LLM_TEST_MODEL_ID);
        if (ObjectUtils.isEmpty(systemModelDTO)) {
            ModelUpdateRequest modelUpdateRequest = ModelTestDataUtil.createLLMModel();
            mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/core/ai/model/update")
                            .content(JsonUtil.toJsonString(modelUpdateRequest))
            ).andExpect(status().isOk());
        }

        // 确保有索引模型
        SystemModelDTO embeddingSystemModelDTO = systemModelService.getModelByModelId(ModelTestDataUtil.EMBEDDING_TEST_MODEL_ID);
        if (ObjectUtils.isEmpty(embeddingSystemModelDTO)) {
            ModelUpdateRequest modelUpdateRequest = ModelTestDataUtil.createEmbeddingModel();
            mockMvc.perform(
                    postMockRequestForAdmin("/mind/api/core/ai/model/update")
                            .content(JsonUtil.toJsonString(modelUpdateRequest))
            ).andExpect(status().isOk());
        }


        // 在确保有初始化的知识库
        createDataset(DatasetTestConstant.DATASET_UNIT_NAME, ModelTestDataUtil.LLM_TEST_MODEL_ID, ModelTestDataUtil.EMBEDDING_TEST_MODEL_ID);
    }
    /**
     * 基于fileId创建集合
     *
     * @param datasetId
     * @param fileId
     * @param name
     * @param parentId
     * @return
     */
    @SneakyThrows
    public String createCollectionByFileId(String datasetId, String fileId, String name, String parentId) {
        FileIdCreateDatasetCollectionParamsDTO params = new FileIdCreateDatasetCollectionParamsDTO();
        fillBaseCollectionParams(params, name, datasetId);
        params.setFileId(fileId);
        params.setCustomPdfParse(false);
        params.setParentId(parentId);
        ResultActions createAction = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/collection/create/fileId")
                        .content(JsonUtil.toJsonString(params))
        );
        String createResp = createAction.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();
        String collectionId = JsonPath.read(createResp, "$.data.collectionId");
        assertThat(collectionId).isNotBlank();
        log.info("/collection/create/fileId接口的返回结果: {}", createResp);
        return collectionId;
    }

    /**
     * 重载知识库上传文件
     * @param file
     * @return
     */
    public String uploadDatasetFile(File file) {
        return uploadDatasetFile(file, null);
    }
    /**
     * 知识库上传文件
     * @param file
     * @param datasetId
     * @return
     */
    @SneakyThrows
    public String uploadDatasetFile(File file, String datasetId) {
        Map<String, String> data = new HashMap<>();
        data.put("datasetId", datasetId);

        org.springframework.mock.web.MockMultipartFile multipartFile = new org.springframework.mock.web.MockMultipartFile(
                "file",
                file.getName(),
                "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                new java.io.FileInputStream(file)
        );
        ResultActions resultActions = mockMvc.perform(
                MockMvcRequestBuilders.multipart("/mind/api/common/file/upload")
                        .file(multipartFile)
                        .header(ACCESS_TOKEN_NAME, adminToken)
                        .param("bucketName", DatasetConstant.DATASET_BUCKET_NAME)
                        .param("data", JsonUtil.toJsonString(data))
        );
        String uploadResp = resultActions.andExpect(status().isOk())
                .andReturn().getResponse().getContentAsString();

        // 验证上传文件的结果需要有文件id
        String fileId = JsonPath.read(uploadResp, "$.data.fileId");
        assertThat(fileId).isNotBlank();
        return fileId;
    }

    /**
     * 根据知识库名称创建知识库
     *  - 当知识库不存在时，会进行创建
     * @param datasetName
     */
    @SneakyThrows
    public String createDataset(String datasetName, String agentModel, String vectorModel) {
        String dbDatasetId = datasetService.getDatasetIdByName(datasetName);
        if (StringUtils.isNotBlank(dbDatasetId)) {
            return dbDatasetId;
        }
        DatasetCreateRequest createRequest = new DatasetCreateRequest();
        createRequest.setType("dataset");
        createRequest.setAvatar("core/dataset/commonDatasetColor");
        createRequest.setName(datasetName);
        createRequest.setIntro("");
        createRequest.setVectorModel(vectorModel);
        createRequest.setAgentModel(agentModel);
        createRequest.setVlmModel(null);
        ResultActions perform = mockMvc.perform(
                postMockRequestForAdmin("/mind/api/core/dataset/create")
                        .content(JsonUtil.toJsonString(createRequest))
        );
        String contentAsString = perform.andExpect(status().isOk())
                .andExpect(jsonPath("$.data", notNullValue()))
                .andReturn().getResponse().getContentAsString();
        String datasetId = JsonPath.read(contentAsString, "$.data");
        assertThat(datasetId).isNotBlank();
        log.info("/dataset/create接口的返回结果: {}", contentAsString);
        return datasetId;
    }

    /**
     * 公共参数组装方法
     */
    public void fillBaseCollectionParams(BaseDatasetCollectionParamsDTO params, String name, String datasetId) {
        params.setTrainingType("chunk");
        params.setChunkTriggerType("minSize");
        params.setChunkTriggerMinSize(1000);
        params.setDataEnhanceCollectionName(false);
        params.setImageIndex(false);
        params.setAutoIndexes(false);
        params.setChunkSettingMode("auto");
        params.setChunkSplitMode("paragraph");
        params.setParagraphChunkAIMode("forbid");
        params.setParagraphChunkDeep(5);
        params.setParagraphChunkMinSize(100);
        params.setChunkSize(1000);
        params.setChunkSplitter("");
        params.setIndexSize(8096);
        params.setQaPrompt("<Context></Context> 标记中是一段文本，学习和分析它，并整理学习成果：\n- 提出问题并给出每个问题的答案。\n- 答案需详细完整，尽可能保留原文描述，可以适当扩展答案描述。\n- 答案可以包含普通文字、链接、代码、表格、公示、媒体链接等 Markdown 元素。\n- 最多提出 50 个问题。\n- 生成的问题和答案和源文本语言相同。\n");
        params.setDatasetId(datasetId);
        params.setName(name);
    }
}
