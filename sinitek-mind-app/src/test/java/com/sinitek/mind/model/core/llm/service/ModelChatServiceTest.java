package com.sinitek.mind.model.core.llm.service;

import com.sinitek.mind.MindBaseTestApplication;
import com.sinitek.mind.dataset.constant.DatasetTestConstant;
import com.sinitek.mind.dataset.service.IDatasetService;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Collections;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

/**
 * IModelChatService-单元测试
 *
 * <AUTHOR>
 * date 2025-07-25
 */
@Slf4j
public class ModelChatServiceTest extends MindBaseTestApplication {

    @Autowired
    private IModelChatService modelChatService;

    @Autowired
    private IDatasetService datasetService;

    String model = "Qwen3-14B-AWQ";

    @Test
    @DisplayName("测试基础聊天方法")
    public void testChatWithString() {
        String message = "你好，这是一个测试消息，收到给我返回yes";
        String response = modelChatService.chat(message, model);
        
        assertThat(response).isNotBlank();
        log.info("基础聊天方法返回结果: {}", response);
    }

    @Test
    @DisplayName("测试知识库验证")
    public void testChatWithPrompt() {
        String message = "Spring漏洞CVE-2024-38816是什么";
        String datasetId = datasetService.getDatasetIdByName(DatasetTestConstant.DATASET_UNIT_NAME);
        String response = modelChatService.chat(message, model, Collections.singletonList(datasetId));

        assertThat(response).isNotBlank();
        log.info("测试知识库验证方法返回结果: {}", response);
    }
}