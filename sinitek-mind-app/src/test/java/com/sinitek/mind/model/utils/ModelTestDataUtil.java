package com.sinitek.mind.model.utils;


import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.ModelUpdateRequest;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * 模型测试数据工具类
 *
 * <AUTHOR>
 * date 2025-07-30
 */
public class ModelTestDataUtil {

    public static final String LLM_TEST_MODEL_ID = "Qwen3-32B-AWQ";

    public static final String EMBEDDING_TEST_MODEL_ID = "bge-base-zh";

    /**
     * Embedding模型
     *
     * @return
     */
    public static ModelUpdateRequest createEmbeddingModel() {
        ModelUpdateRequest modelUpdateRequest = new ModelUpdateRequest();
        modelUpdateRequest.setModel(EMBEDDING_TEST_MODEL_ID);

        Map<String, Object> metadata = new HashMap<>();
        // 有值的属性放在前面
        metadata.put("name", EMBEDDING_TEST_MODEL_ID);
        metadata.put("provider", ModelProviderConstant.OTHER);
        metadata.put("type", "embedding");
        metadata.put("isActive", true);
        metadata.put("isCustom", true);
        metadata.put("requestUrl", "http://192.168.22.246:9997");
        metadata.put("defaultToken", 8096);
        metadata.put("maxToken", 8096);
        metadata.put("normalization", false);
        metadata.put("charsPointsPrice", 0);

        modelUpdateRequest.setMetadata(metadata);
        return modelUpdateRequest;
    }

    /**
     * LLM模型
     *
     * @return
     */
    public static ModelUpdateRequest createLLMModel() {
        ModelUpdateRequest modelUpdateRequest = new ModelUpdateRequest();
        modelUpdateRequest.setModel(LLM_TEST_MODEL_ID);

        Map<String, Object> metadata = new HashMap<>();
        // 有值的属性放在前面
        metadata.put("provider", ModelProviderConstant.OTHER);
        metadata.put("name", LLM_TEST_MODEL_ID);
        metadata.put("type", "llm");
        metadata.put("isActive", true);
        metadata.put("isCustom", true);
        metadata.put("isDefault", true);
        metadata.put("requestUrl", "http://192.168.22.246:9997/");
        metadata.put("maxContext", 100000);
        metadata.put("quoteMaxToken", 100000);
        metadata.put("vision", false);
        metadata.put("datasetProcess", true);
        metadata.put("usedInClassify", false);
        metadata.put("usedInExtractFields", true);
        metadata.put("usedInToolCall", false);
        metadata.put("toolChoice", true);

        modelUpdateRequest.setMetadata(metadata);
        return modelUpdateRequest;
    }
}
