package com.sinitek.mind;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.SirmApplication;
import com.sinitek.mind.base.properties.SiniCubeTestProperties;
import com.sinitek.sirm.common.encryption.algorithm.asymmetric.IAsymmetricEncryption;
import com.sinitek.sirm.common.utils.JsonUtil;
import com.sinitek.sirm.framework.frontend.support.RequestResult;
import com.sinitek.sirm.framework.support.LoginRequestParam;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;
import org.springframework.test.web.servlet.ResultMatcher;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import java.nio.charset.StandardCharsets;
import java.util.HashMap;

import static org.hamcrest.Matchers.is;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * Mind测试基层类
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Slf4j
@Rollback
//@Transactional
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = SirmApplication.class,webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureMockMvc
public class MindBaseTestApplication {

    @Autowired
    public MockMvc mockMvc;

    @Autowired
    public ObjectMapper objectMapper;

    @Autowired
    private SiniCubeTestProperties siniCubeTestProperties;

    @Autowired
    public IAsymmetricEncryption asymmetricEncryption;

    public static final String ACCESS_TOKEN_NAME = "accesstoken";

    public String adminToken = "temp";

    @BeforeEach
    public void before() throws Exception {
        adminToken = simulatedLoginAdmin();
    }

    /**
     * get方式Mock Request
     *
     * @param url
     * @return
     */
    public MockHttpServletRequestBuilder getMockRequestForAdmin(String url) {
        return MockMvcRequestBuilders
                .get(url)
                .header(ACCESS_TOKEN_NAME, adminToken);
    }

    /**
     * post方式Mock Request
     *
     * @param url
     * @return
     */
    public MockHttpServletRequestBuilder postMockRequestForAdmin(String url) {
        return MockMvcRequestBuilders
                .post(url)
                .contentType(MediaType.APPLICATION_JSON)
                .characterEncoding(StandardCharsets.UTF_8.name())
                .header(ACCESS_TOKEN_NAME, adminToken);
    }

    /**
     * mockMvc 通用预期判断
     *
     * @param request
     * @throws Exception
     */
    public MvcResult mockMvcCommonExpect(MockHttpServletRequestBuilder request) throws Exception {
        return mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(getResultCodeMatcher())
                .andReturn();
    }

    public void printLog(Object t) {
        log.info(JsonUtil.toJsonString(t));
    }

    /**
     * 返回 resultcode断言 SUCCESS_CODE的 ResultMatcher
     *
     * @return
     */
    public ResultMatcher getResultCodeMatcher() {
        return jsonPath("$.resultcode", is(RequestResult.SUCCESS_CODE));
    }

    /**
     * 验证响应code是否为200
     */
    public ResultMatcher jsonPathCodeIs200() {
        return jsonPath("$.code", is(200));
    }

    /**
     * 返回 resultcode断言 messageCode的 ResultMatcher
     * 一般用于测异常
     * @return
     */
    public ResultMatcher getPredictCodeMatcher(String code) {
        return jsonPath("$.resultcode", is(code));
    }

    /**
     * 模拟登陆admin, 返回accessToken
     *
     * @return
     * @throws Exception
     */
    public String simulatedLoginAdmin() throws Exception {
        String adminName = "admin";
        String adminPwd = siniCubeTestProperties.getAdminPw();
        String s = simulatedLogin(adminName, adminPwd);
        log.debug(s);
        return s;
    }

    /**
     * 默认的登录参数
     * @return
     */
    public LoginRequestParam defaultLoginRequestParam() {
        String adminName = "admin";
        String adminPwd = siniCubeTestProperties.getAdminPw();
        return buildLoginRequestParam(adminName, adminPwd);
    }

    /**
     * 组装的登录参数
     * @return
     */
    public LoginRequestParam buildLoginRequestParam(String adminName, String adminPwd) {
        LoginRequestParam loginRequestParam = new LoginRequestParam();
        loginRequestParam.setUsername(adminName);
        loginRequestParam.setUserpwd(asymmetricEncryption.encryptByPublicKey(adminPwd));
        return loginRequestParam;
    }

    /**
     * 组装登录的请求对象
     * @param loginRequestParam
     * @return
     */
    @SneakyThrows
    public MockHttpServletRequestBuilder buildLoginRequest(LoginRequestParam loginRequestParam) {
        MockHttpServletRequestBuilder request = MockMvcRequestBuilders
                .post("/frontend/api/login")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(loginRequestParam));
        return request;
    }

    /**
     * 根据用户名/密码模拟登陆
     *
     * @return
     */
    public String simulatedLogin(String adminName, String adminPwd) throws Exception {
        LoginRequestParam loginRequestParam = this.buildLoginRequestParam(adminName, adminPwd);
        MockHttpServletRequestBuilder request = this.buildLoginRequest(loginRequestParam);

        MvcResult mvcResult = mockMvc.perform(request)
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.resultcode", is(RequestResult.SUCCESS_CODE)))
                .andReturn();

        String content = mvcResult.getResponse().getContentAsString();
        RequestResult requestResult = JsonUtil.toJavaObject(content, RequestResult.class);
        HashMap map = (HashMap) requestResult.getData();
        return MapUtils.getString(map, "accesstoken");
    }

}
