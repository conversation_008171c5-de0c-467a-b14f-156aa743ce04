---
sidebarDepth: 3
---

# Spring安全漏洞

## CVE-2024-38819


### 漏洞说明

#### 漏洞描述

通过功能性 Web 框架 WebMvc.fn 或 WebFlux.fn 提供静态资源的应用程序容易受到路径遍历攻击。攻击者可以构建恶意 HTTP 请求，并获取文件系统上任何文件，这些文件也可以被运行 Spring 应用程序的进程访问

#### 影响版本

* Spring Framework 6.1.0 - 6.1.13

* Spring Framework 6.0.0 - 6.0.24

* Spring Framework 5.3.0 - 5.3.40

以及不受支持的旧版本。

#### 安全版本

Spring >= 5.3.41

#### 推荐版本

Spring 升级到 5.3.41及以上

### 解决方案

#### 前置了解：Spring5.x停止维护说明

* 简单说：就是Spring5+SpringBoot2的开源版本已经停止维护了，只有购买了Spring商业版才能升级到Spring 5.3.41，所以无法在直接进行升级

* 更多信息可查看[Spring5开源版停止维护说明](/guides/upgrade/vulnerability/Spring5开源版停止维护说明.html)

#### 解决方案一：针对漏洞实际情况进行分析处理

* 找到Spring官方对这个漏洞的说明[https://spring.io/security/cve-2024-38819](https://spring.io/security/cve-2024-38819)

* 其中有说到漏洞的触发条件，需同时满足以下两个条件：
  * 1、应用程序通过RouterFunctions（WebMvc.fn/WebFlux.fn 的核心组件）处理静态资源
  * 2、静态资源的位置被显式配置为FileSystemResource（即资源存储在服务器本地文件系统中，而非打包在应用 JAR 包内的类路径资源等）

* 针对条件一、条件二可以项目全局搜索`RouterFunctions`，看看是否有如此处理静态资源。例如类似如下的代码

```java
@Bean
public RouterFunction<ServerResponse> staticResourceRouter() {
    return RouterFunctions
        .resources("/static/**", new FileSystemResource("/path/to/local/static/files/")); // 关键：使用FileSystemResource
}
```

* `总结：` 目前是前后端分离项目，框架在后端不会放静态资源所以也就不存在使用RouterFunctions处理静态资源，同时更不可能用FileSystemResource直接使用服务器的静态资源，
两者需要同时满足才有可能触发该漏洞。`而现在两者都不成立故此这个漏洞本质上根本不可能触发，项目上进行检查明确后可沟通讨论无需处理`

#### 解决方案二：升级到Spring6

* 框架[8.0.x](/guides/upgrade/update/8.0版本升级.html)就是Spring6的，等于需要项目上升级到框架8.0才行。**非必要，请勿升级。这属于大升级改造，应项目有规划时考虑，而不应该为了一个无法触发的漏洞升级**

* 升级Spring6的代价很高，JDK需要从8最低升级到17，配套的SpringBoot、SpringCloud全部都需要升级，还有各种第三方框架Tomcat、Swagger都有较多调整

## CVE-2024-22262

### 漏洞说明

#### 漏洞描述

如果 URL 在通过验证检查后使用，则用于解析外部提供的 URL（例如通过查询参数）并在已解析 URL
的主机上执行验证检查的应用程序可能容易受到开放重定向攻击或 SSRF 攻击。UriComponentsBuilder

这与 CVE-2024-22259 和 CVE-2024-22243 相同，但输入不同

官方修复说明: [https://spring.io/security/cve-2024-22262](https://spring.io/security/cve-2024-22262)

#### 影响版本

* Spring Framework 6.1.0 - 6.1.5

* Spring Framework 6.0.0 - 6.0.18

* Spring Framework 5.3.0 - 5.3.33

以及不受支持的旧版本。

#### 安全版本

Spring >= 5.3.34

#### 推荐版本

Spring 升级到 5.3.34及以上

### 解决方案

#### 方式一: 产品/项目的Pom.xml指定spring的版本

推荐场景: 较老项目、不太能升级框架版本的项目

```xml
<properties>
    <spring.version>5.3.34</spring.version>
</properties>
```

第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)

#### 方式二: 升级框架版本

推荐场景: 产品最新版本、或者能持续升级框架版本的项目

以下框架版本已解决该问题，**内置了方式一的解决**

| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             
|-------------------------------|---------------------------------------------------|
| 后端: 7.4.368及以上 前端: 7.4.330及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |
| 后端: 7.3.441及以上 前端: 7.3.449及以上 | 请参考[7.3版本升级说明](/guides/upgrade/update/7.3版本升级.md) |


### 微服务项目特别说明（仅微服务项目看）

* 微服务项目中，**有Gateway时，如果SpringBoot的版本为2.3.4，则无法直接升级spring的版本到5.3.x**，将得到如下报错

```text
[ERROR][SIRM][51360,144,reactor-http-nio-4][2024-08-05 17:09:17,961][reactor.util.Loggers$Slf4JLogger.error:319] - [id: 0x0db35a4b, L:/127.0.0.1:9999 - R:/127.0.0.1:9146] 
java.lang.NoSuchMethodError: reactor.netty.http.server.HttpServerRequest.hostPort()I
	at org.springframework.http.server.reactive.ReactorServerHttpRequest.resolveBaseUrl(ReactorServerHttpRequest.java:84) ~[spring-web-5.3.34.jar:5.3.34]
	at org.springframework.http.server.reactive.ReactorServerHttpRequest.initUri(ReactorServerHttpRequest.java:79) ~[spring-web-5.3.34.jar:5.3.34]
	at org.springframework.http.server.reactive.ReactorServerHttpRequest.<init>(ReactorServerHttpRequest.java:71) ~[spring-web-5.3.34.jar:5.3.34]
	at org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:58) ~[spring-web-5.3.34.jar:5.3.34]
	at org.springframework.http.server.reactive.ReactorHttpHandlerAdapter.apply(ReactorHttpHandlerAdapter.java:40) ~[spring-web-5.3.34.jar:5.3.34]
	at reactor.netty.http.server.HttpServerHandle.onStateChange(HttpServerHandle.java:64) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]
	at reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:537) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]
	at reactor.netty.tcp.TcpServerBind$ChildObserver.onStateChange(TcpServerBind.java:278) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]
	at reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:475) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]
	at reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:96) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:191) ~[reactor-netty-0.9.20.RELEASE.jar:0.9.20.RELEASE]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290) ~[netty-codec-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562) ~[netty-transport-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30) ~[netty-common-4.1.100.Final.jar:4.1.100.Final]
	at java.lang.Thread.run(Thread.java:748) ~[?:1.8.0_201]
```

* **原因：** Spring5.3.x删除了一些方法，导致SpringCloudGateway不兼容

* **解决方案一：** 升级框架版本到[sirmapp7.4.353 -> sirmapp7.4.365(漏洞升级-重要重要重要)](/guides/upgrade/update/7.4版本升级.html#sirmapp7-4-353-sirmapp7-4-365-漏洞升级-重要重要重要)，这将提升SpringBoot、SpringCloud的版本从而兼容

* **解决方案二：** Gateway项目单独不升级Spring版本，还是保持原版本
  * 不升级Gateway项目也不受该漏洞影响，漏洞的核心是`UriComponentsBuilder`类中的方法`fromUriString()`等可能导致验证绕过，而Gateway项目不会用到此类方法

### 测试方案

能做的是：

* 安全扫描。
* 冒烟测试。
* 确认修复后部署包中Spring的版本
  * 1、找开发人员询问对应项目的部署包,然后解压部署包
  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。
  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.3.32.jar】和【spring-context-5.3.34.jar】，这种情况就属于不同版本号的重复的jar
  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.34**。

## CVE-2024-22243

### 漏洞说明

#### 漏洞描述

Spring Framework中修复了一个URL解析不当漏洞（CVE-2024-22243）。

Spring Framework受影响版本中，由于UriComponentsBuilder中在处理URL 时未正确过滤用户信息中的”\[“，可能导致威胁者构造恶意URL绕过验证。
当应用程序使用UriComponentsBuilder来解析外部提供的URL（如通过查询参数）并对解析的URL的主机执行验证检查时可能容易受到Open重定向攻击和SSRF攻击，导致网络钓鱼和内部网络探测等。

官方修复说明: [https://spring.io/security/cve-2024-22243](https://spring.io/security/cve-2024-22243)

#### 影响版本

* Spring Framework 6.1.0 - 6.1.3

* Spring Framework 6.0.0 - 6.0.16

* Spring Framework 5.3.0 - 5.3.31

以及不受支持的旧版本。

#### 安全版本

Spring >= 5.3.32

#### 推荐版本

Spring 升级到 5.3.32及以上

### 解决方案

#### 方式一: 产品/项目的Pom.xml指定spring的版本

推荐场景: 较老项目、不太能升级框架版本的项目

```xml
<properties>
    <spring.version>5.3.32</spring.version>
</properties>
```

第三方框架漏洞更详细的处理请参考: [第三方框架漏洞处理步骤演示](/questions/security/第三方框架漏洞处理.md)

* **注意注意注意:** 框架7.2及以上有国际化功能，而spring5.3对国际化相关功能有调整
  * `当框架版本为框架7.2及以上时`,请复制如下代码到项目上(注意包名要和java中相同,实现覆盖SiniCube框架逻辑)。`框架7.1及以下无需该处理`
  * [SpringMessageConfig.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/SpringMessageConfig.java)[点击下载或右键另存为]
  * [LocaleChangeInterceptor.java](/doc/sirmapp-dev/questions/security/spring/CVE-2024-22243/LocaleChangeInterceptor.java)[点击下载或右键另存为]

#### 方式二: 升级框架版本

推荐场景: 产品最新版本、或者能持续升级框架版本的项目

以下框架版本已解决该问题，**内置了方式一的解决**

| 支持的版本                         | 升级说明(明确是否有脚本、配置、代码变动)                             |                                             
|-------------------------------|---------------------------------------------------|
| 后端: 7.4.331及以上 前端: 7.4.302及以上 | 请参考[7.4版本升级说明](/guides/upgrade/update/7.4版本升级.md) |
| 后端: 7.1.281及以上 前端: 7.1.148及以上 | 请参考[7.1版本升级说明](/guides/upgrade/update/7.1版本升级.md) |

### 测试方案

能做的是：

* 安全扫描。
* 冒烟测试。
* 确认修复后部署包中Spring的版本
  * 1、找开发人员询问对应项目的部署包,然后解压部署包
  * 2、确认解压后的部署包**WEB-INF/lib**或者**BOOT-INT/lib**(不同的部署包类型位置不一样,哪个目录有内容就验证哪个)下的jar都升级到位。
  * 3、**不能有不同版本号重复的jar**，例如同时有【spring-context-5.2.24.jar】和【spring-context-5.3.32.jar】，这种情况就属于不同版本号的重复的jar
  * 4、部署包中全部spring-开头的jar包,**版本都应该等于或大于5.3.32**。




