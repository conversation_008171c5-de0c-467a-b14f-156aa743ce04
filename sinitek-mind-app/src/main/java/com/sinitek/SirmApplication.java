package com.sinitek;

import com.sinitek.cloud.base.support.ActionTrackInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.ai.vectorstore.milvus.autoconfigure.MilvusVectorStoreAutoConfiguration;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.quartz.QuartzAutoConfiguration;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.context.annotation.PropertySource;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;

@SpringBootApplication(exclude = { QuartzAutoConfiguration.class, MilvusVectorStoreAutoConfiguration.class} )
@EnableFeignClients(basePackages = {"com.sinitek"})
@PropertySource(value = "classpath:sinicube.properties")
@MapperScan("com.sinitek.**.mapper")
@EnableAspectJAutoProxy(proxyTargetClass = true, exposeProxy = true)
@EnableRetry(proxyTargetClass = true)
public class SirmApplication {

    public static void main(String [] args) { SpringApplication.run(SirmApplication.class, args); }

    @Autowired
    private ActionTrackInterceptor actionTrackInterceptor;

    @Bean("restTemplate")
    @LoadBalanced
    public RestTemplate getRestTemplate() {
        RestTemplate restTemplate = new RestTemplate();
        restTemplate.setInterceptors(Collections.singletonList(actionTrackInterceptor));
        return restTemplate;
    }

}
