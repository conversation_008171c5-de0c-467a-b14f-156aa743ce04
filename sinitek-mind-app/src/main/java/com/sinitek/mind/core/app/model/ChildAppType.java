package com.sinitek.mind.core.app.model;

import com.sinitek.mind.core.workflow.model.SystemPluginTemplateItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@EqualsAndHashCode(callSuper=true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ChildAppType extends SystemPluginTemplateItemType {

    private String teamId;

    private String tmbId;
}
