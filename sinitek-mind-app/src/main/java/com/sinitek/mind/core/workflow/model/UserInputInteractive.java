package com.sinitek.mind.core.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class UserInputInteractive extends InteractiveNodeType {
    private String type = "userInput";
    private Params params;

    @Data
    @SuperBuilder
    public static class Params {
        private String description;
        private List<UserInputFormItemType> inputForm;
        private Boolean submitted;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public Params getParam() {
        return params;
    }
}