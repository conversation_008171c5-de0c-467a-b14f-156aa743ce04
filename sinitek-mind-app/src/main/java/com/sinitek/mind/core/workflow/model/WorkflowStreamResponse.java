package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.workflow.model.sse.SseResponseType;
import lombok.Builder;
import lombok.Data;

import java.util.function.Consumer;

/**
 * 工作流的流响应，使用sse发送至前端
 */
@Data
@Builder
public class WorkflowStreamResponse {

    private String event;
    private SseResponseType data;

    // TODO 需要确认这个函数是否需要
    private Consumer<String> writeFunction;
}
