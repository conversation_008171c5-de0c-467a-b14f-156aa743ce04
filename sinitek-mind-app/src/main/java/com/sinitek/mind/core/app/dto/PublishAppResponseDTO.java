package com.sinitek.mind.core.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 发布应用响应DTO
 * 对应TypeScript的publish接口响应
 */
@Data
@Schema(description = "发布应用响应")
public class PublishAppResponseDTO {
    
    @Schema(description = "操作成功标识", example = "true")
    private Boolean success = true;
    
    @Schema(description = "版本ID（仅在非自动保存模式下返回）")
    private String versionId;
    
    public PublishAppResponseDTO() {
        this.success = true;
    }
    
    public PublishAppResponseDTO(String versionId) {
        this.success = true;
        this.versionId = versionId;
    }
}