package com.sinitek.mind.core.workflow.model.sse;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.HashMap;
import java.util.Map;

/**
 * sse响应对象，map包装
 * @param <K>
 * @param <V>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SseMapResponse<K, V> extends HashMap<K, V> implements SseResponseType  {

    public SseMapResponse(Map<K, V> map) {
        super(map);
    }

}
