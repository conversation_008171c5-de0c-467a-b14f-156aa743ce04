package com.sinitek.mind.core.app.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.workflow.model.FlowNodeTemplateType;

import java.util.List;

public interface IPluginService {

    FlowNodeTemplateType getChildAppPreviewNode(String appId, String versionId);

    List<NodeTemplateListItem> getSystemPluginTemplates(String searchKey, String parentId);

    PageResult<VersionListItemType> getVersionList(String toolId, int offset, int pageSize, String userId);

    List<ParentTreePathItemType> getPluginPath(String pluginId, String type);

    PluginRuntimeType getChildAppRuntimeById(String id, String versionId);

    ChildAppType getSystemPluginTemplateById(String id, String versionId);
}
