package com.sinitek.mind.core.ai.model;

import lombok.Data;

/**
 * 聊天完成消息工具调用
 * 对应 TypeScript 中扩展的 ChatCompletionMessageToolCall
 */
@Data
public class ChatCompletionMessageToolCall {
    private String id;
    private String type;
    private Function function;
    private Integer index;
    private String toolName;
    private String toolAvatar;
    
    public ChatCompletionMessageToolCall() {}

    /**
     * 函数调用信息
     */
    @Data
    public static class Function {
        private String name;
        private String arguments;
        
        public Function() {}
        
        public Function(String name, String arguments) {
            this.name = name;
            this.arguments = arguments;
        }

    }
}