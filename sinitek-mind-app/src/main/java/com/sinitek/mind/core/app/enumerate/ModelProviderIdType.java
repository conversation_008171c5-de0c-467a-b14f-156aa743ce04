package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum ModelProviderIdType {
    OPENAI("OpenAI"),
    CLAUDE("Claude"),
    GEMINI("Gemini"),
    META("Meta"),
    MISTRAL_AI("MistralAI"),
    GROQ("Groq"),
    GROK("Grok"),
    JINA("Jina"),
    ALI_CLOUD("AliCloud"),
    QWEN("Qwen"),
    DOUBAO("Doubao"),
    DEEP_SEEK("DeepSeek"),
    CHAT_GLM("ChatGLM"),
    ERNIE("<PERSON>"),
    MOONSHOT("Moonshot"),
    MINIMAX("MiniMax"),
    SPARK_DESK("SparkDesk"),
    HUNYUAN("Hunyuan"),
    BAICHUAN("Baichuan"),
    STEPFUN("StepFun"),
    YI("Yi"),
    SILICONFLOW("Siliconflow"),
    <PERSON><PERSON>("PPIO"),
    OLLAMA("<PERSON>llama"),
    BAAI("BAAI"),
    FISH_AUDIO("FishAudio"),
    INTERN("Intern"),
    MOKA("Moka"),
    <PERSON>THER("Other");

    private final String value;

    ModelProviderIdType(String value) {
        this.value = value;
    }

}