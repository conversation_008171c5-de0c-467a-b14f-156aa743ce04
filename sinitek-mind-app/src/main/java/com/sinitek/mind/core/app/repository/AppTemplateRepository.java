package com.sinitek.mind.core.app.repository;

import com.sinitek.mind.core.app.entity.AppTemplate;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 应用模板数据访问层
 */
@Repository
public interface AppTemplateRepository extends MongoRepository<AppTemplate, String> {
    
    /**
     * 查找所有激活的模板
     * 
     * @return 激活的模板列表
     */
    @Query("{'isActive': true}")
    List<AppTemplate> findAllActive();
    
    /**
     * 根据类型查找激活的模板
     * 
     * @param type 应用类型
     * @return 指定类型的激活模板列表
     */
    @Query("{'isActive': true, 'type': ?0}")
    List<AppTemplate> findActiveByType(String type);
    
    /**
     * 查找所有快速模板
     * 
     * @return 快速模板列表
     */
    @Query("{'isActive': true, 'isQuickTemplate': true}")
    List<AppTemplate> findAllQuickTemplates();
    
    /**
     * 根据类型查找快速模板
     * 
     * @param type 应用类型
     * @return 指定类型的快速模板列表
     */
    @Query("{'isActive': true, 'type': ?0, 'isQuickTemplate': true}")
    List<AppTemplate> findQuickTemplatesByType(String type);
    
    /**
     * 根据模板ID查找模板
     * 
     * @param templateId 模板ID
     * @return 模板对象
     */
    AppTemplate findByTemplateId(String templateId);
    
    /**
     * 查找所有激活的模板，按order字段排序
     * 
     * @return 排序后的激活模板列表
     */
    @Query(value = "{'isActive': true}", sort = "{'order': 1}")
    List<AppTemplate> findAllActiveOrderByOrder();
}