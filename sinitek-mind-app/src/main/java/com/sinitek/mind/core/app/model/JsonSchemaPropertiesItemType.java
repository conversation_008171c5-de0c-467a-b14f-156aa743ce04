package com.sinitek.mind.core.app.model;

import lombok.Data;

import java.util.List;

@Data
public class JsonSchemaPropertiesItemType {

    private String description;

    private String type; // 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object'

    private List<String> enums;

    private Integer minimum;
    private Integer maximum;
    private ItemInfo items;

    public JsonSchemaPropertiesItemType (String type) {
        this.type = type;
    }

    public JsonSchemaPropertiesItemType (String type, ItemInfo items) {
        this.type = type;
        this.items = items;
    }

    @Data
    public static class ItemInfo {
        private String type; // 'string' | 'number' | 'integer' | 'boolean' | 'array' | 'object'

        public ItemInfo (String type) {
            this.type = type;
        }
    }
}
