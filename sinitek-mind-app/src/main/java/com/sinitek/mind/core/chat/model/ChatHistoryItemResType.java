package com.sinitek.mind.core.chat.model;

import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.sse.SseResponseType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class ChatHistoryItemResType extends DispatchNodeResponseType implements SseResponseType {

    private String nodeId;

    private String id;

    private String moduleType;

    private String moduleName;
}
