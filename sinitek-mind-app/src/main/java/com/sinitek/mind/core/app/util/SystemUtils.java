package com.sinitek.mind.core.app.util;

import java.net.URL;
import java.util.Arrays;
import java.util.List;

public class SystemUtils {
    
    private static final String SERVICE_LOCAL_HOST = System.getenv("HOSTNAME") != null ? 
            System.getenv("HOSTNAME") + ":" + (System.getenv("PORT") != null ? System.getenv("PORT") : "8080") : 
            "localhost:8080";
    
    public static boolean isInternalAddress(String urlString) {
        try {
            URL parsedUrl = new URL(urlString);
            String hostname = parsedUrl.getHost();
            String fullUrl = parsedUrl.toString();
            
            // 检查是否为本地主机地址
            if (hostname.equals(SERVICE_LOCAL_HOST)) {
                return true;
            }
            
            // 元数据端点白名单
            List<String> metadataEndpoints = Arrays.asList(
                // AWS
                "http://***************/latest/meta-data/",
                // Azure
                "http://***************/metadata/instance?api-version=2021-02-01",
                // GCP
                "http://metadata.google.internal/computeMetadata/v1/",
                // Alibaba Cloud
                "http://***************/latest/meta-data/",
                // Tencent Cloud
                "http://metadata.tencentyun.com/latest/meta-data/",
                // Huawei Cloud
                "http://***************/latest/meta-data/"
            );
            
            for (String endpoint : metadataEndpoints) {
                if (fullUrl.startsWith(endpoint)) {
                    return true;
                }
            }
            
            // 如果环境变量未设置为检查内部IP，则直接返回false
            if (!"true".equals(System.getenv("CHECK_INTERNAL_IP"))) {
                return false;
            }
            
            // 检查是否为IP地址
            String ipv4Pattern = "^(\\d{1,3}\\.){3}\\d{1,3}$";
            if (!hostname.matches(ipv4Pattern)) {
                return false; // 不是IP地址，是域名 - 默认认为是外部地址
            }
            
            // IP地址验证
            String[] parts = hostname.split("\\.");
            int[] ipParts = new int[4];
            
            for (int i = 0; i < 4; i++) {
                ipParts[i] = Integer.parseInt(parts[i]);
                if (ipParts[i] < 0 || ipParts[i] > 255) {
                    return false;
                }
            }
            
            // 只允许公共IP范围
            return !(ipParts[0] == 0 ||
                    ipParts[0] == 10 ||
                    ipParts[0] == 127 ||
                    (ipParts[0] == 169 && ipParts[1] == 254) ||
                    (ipParts[0] == 172 && ipParts[1] >= 16 && ipParts[1] <= 31) ||
                    (ipParts[0] == 192 && ipParts[1] == 168) ||
                    (ipParts[0] >= 224 && ipParts[0] <= 239) ||
                    (ipParts[0] >= 240 && ipParts[0] <= 255) ||
                    (ipParts[0] == 100 && ipParts[1] >= 64 && ipParts[1] <= 127) ||
                    (ipParts[0] == 9 && ipParts[1] == 0) ||
                    (ipParts[0] == 11 && ipParts[1] == 0));
            
        } catch (Exception e) {
            return false; // 如果URL解析失败，拒绝它作为潜在的不安全URL
        }
    }
}