package com.sinitek.mind.core.app.service.impl;

import com.mchange.v2.lang.ObjectUtils;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.dto.AuthAppDTO;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.UpdateHttpPluginDTO;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.service.IHttpPluginService;
import com.sinitek.mind.core.app.util.HttpPluginUtils;
import com.sinitek.mind.core.app.util.ImageUtils;
import com.sinitek.mind.core.app.util.MongoSessionUtils;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.MongoTransactionManager;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class HttpPluginServiceImpl implements IHttpPluginService {

    private final IPermissionService permissionService;

    private final IAppService appService;

    private MongoTransactionManager transactionManager;

    private final MongoTemplate mongoTemplate;

    private final ImageUtils imageUtils;

    private final IAuthAppService authAppService;

    @Override
    public ApiResponse<String> createHttpPlugin(CreateAppDTO dto) {
        try {
            // 参数校验
            if (dto.getName() == null || dto.getPluginData() == null) {
                return ApiResponse.error("缺少参数");
            }

            // 权限验证
            AuthAppDTO authAppDTO = new AuthAppDTO();
            if (dto.getParentId() != null) {
                // TODO 需要确认什么样的权限,以及appId
                authAppDTO = authAppService.authApp(dto.getParentId(), PermissionConstant.READ_PER);
            } else {
                // TODO 需要确认什么样的校验
//                AuthModeType authModeType = new AuthModeType();
//                authModeType.setAuthToken(true);
//                authModeType.setPer(PermissionConstant.APP_CREATE_PER);
//                authResult = permissionService.authUserPer(dto, authModeType);
            }

            // 检查团队应用限制
            // TODO 需要确认是否保留这个限制
//            permissionService.checkTeamAppLimit(authResult.getTeamId());

            // 事务中创建插件
            AuthAppDTO finalAuthAppDTO = authAppDTO;
            String httpPluginId = MongoSessionUtils.mongoSessionRun(() -> {
                String appId = appService.createApp(dto);
                // 计算子插件
                List<CreateAppDTO> childrenPlugins = HttpPluginUtils.httpApiSchema2Plugins(
                        appId,
                        dto.getPluginData().getApiSchemaStr(),
                        dto.getPluginData().getCustomHeaders()
                );
                for (CreateAppDTO childDTO : childrenPlugins) {
                    childDTO.setTeamId(finalAuthAppDTO.getTeamId());
                    childDTO.setTmbId(finalAuthAppDTO.getTmbId());
                    appService.createApp(childDTO);
                }

                return appId;
            }, transactionManager);

            return ApiResponse.success(httpPluginId);
        } catch (Exception e) {
            return ApiResponse.error(e.getMessage());
        }
    }

    @Override
    public void updateHttpPlugin(UpdateHttpPluginDTO dto) {
        // 参数校验
        if (dto.getName() == null || dto.getPluginData() == null) {
            throw new BussinessException("缺失参数");
        }
        AuthAppDTO authAppDTO = authAppService.authApp(dto.getAppId(), PermissionConstant.WRITE_PER);
        // 创建事务模板
        TransactionTemplate transactionTemplate = new TransactionTemplate(transactionManager);
        App app = authAppDTO.getApp();
        PluginData pluginData = app.getPluginData();
        transactionTemplate.execute(status -> {
            // 比较存储的数据和更新的数据
            Map<String, String> storeData = new HashMap<>();
            storeData.put("apiSchemaStr", app.getPluginData() != null ? app.getPluginData().getApiSchemaStr() : null);
            storeData.put("customHeaders", app.getPluginData() != null ? app.getPluginData().getCustomHeaders() : null);

            Map<String, String> updateData = new HashMap<>();
            updateData.put("apiSchemaStr", pluginData != null ? pluginData.getApiSchemaStr() : null);
            updateData.put("customHeaders", pluginData != null ? pluginData.getCustomHeaders() : null);

            // 如果数据不相等，更新子插件
            if (!ObjectUtils.eqOrBothNull(storeData, updateData)) {
                updateHttpChildrenPlugin(app.getTeamId(), app.getTmbId(), app.getId(), pluginData);
            }

            // 更新应用
            Update update = new Update();
            if (dto.getName() != null) {
                update.set("name", dto.getName());
            }
            if (dto.getAvatar() != null) {
                update.set("avatar", dto.getAvatar());
            }
            if (dto.getIntro() != null) {
                update.set("intro", dto.getIntro());
            }
            if (pluginData != null) {
                update.set("pluginData", pluginData);
            }
            Query query = new Query(Criteria.where("_id").is(app.getId()));
            mongoTemplate.updateFirst(query, update, App.class);

            // 刷新头像资源
            imageUtils.refreshSourceAvatar(dto.getAvatar(), app.getAvatar(), null);

            return null;
        });
    }

    private void updateHttpChildrenPlugin(String teamId, String tmbId, String parentId, PluginData pluginData) {
        if (pluginData == null || pluginData.getApiSchemaStr() == null) {
            return;
        }
        // 查询数据库中的插件
        Query query = new Query(Criteria.where("parentId").is(parentId).and("teamId").is(teamId));
        query.fields().include("pluginData");

        List<App> apps = mongoTemplate.find(query, App.class);

        // 从Schema生成插件
        List<CreateAppDTO> schemaPlugins = HttpPluginUtils.httpApiSchema2Plugins(
                parentId,
                pluginData.getApiSchemaStr(),
                pluginData.getCustomHeaders()
        );
        // 数据库中存在，schema不存在，删除
        for (App app : apps) {
            boolean found = false;

            for (CreateAppDTO dto : schemaPlugins) {
                if (dto.getName().equals(app.getPluginData().getPluginUniId())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                appService.deleteApp(app.getId());
            }
        }
        // 数据库中不存在，schema存在，新增
        for (CreateAppDTO dto : schemaPlugins) {
            boolean found = false;

            for (App app : apps) {
                if (app.getPluginData() != null &&
                dto.getName().equals(app.getPluginData().getPluginUniId())) {
                    found = true;
                    break;
                }
            }

            if (!found) {
                dto.setTmbId(teamId);
                dto.setTmbId(tmbId);
                appService.createApp(dto);
            }
        }
        // 数据库中存在，schema存在，更新
        for (CreateAppDTO dto : schemaPlugins) {
            for (App app : apps) {
                if (app.getPluginData() != null &&
                dto.getName().equals(app.getPluginData().getPluginUniId())) {
                    Update update = new Update();
                    update.set("name", dto.getName());
                    update.set("intro", dto.getIntro());
                    update.set("version", "v2");
                    Query queryNew = new Query(Criteria.where("_id").is(app.getId()));
                    mongoTemplate.updateFirst(queryNew, update, App.class);
                    break;
                }
            }
        }
    }
}
