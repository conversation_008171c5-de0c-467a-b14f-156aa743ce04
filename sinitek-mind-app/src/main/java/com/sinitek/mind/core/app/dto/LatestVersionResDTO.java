package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.Data;

import java.util.List;

@Data
public class LatestVersionResDTO {

    private List<StoreNodeItemType> nodes;

    private List<StoreEdgeItemType> edges;

    private AppChatConfigType chatConfig;
}
