package com.sinitek.mind.core.chat.model;

import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class ChatItemType {

    // 自有属性
    private String dataId;

    // ResponseTagItemType属性
    private List<SearchDataResponseItemType> totalQuoteList;

    private Integer llmModuleAccount;

    private Integer historyPreviewLength;

    private ChatRoleEnum obj;

    private Boolean hideInUI;

    private List<ChatItemValueItemType> value;

    private Map<String, Object> memories;

    private String userGoodFeedback;

    private String userBadFeedback;

    private List<String> customFeedbacks;

    private AdminFbkType adminFeedback;

    private List<ChatHistoryItemResType> responseData;
}