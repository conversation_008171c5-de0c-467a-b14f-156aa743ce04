package com.sinitek.mind.core.app.common;

import com.sinitek.mind.core.app.dto.ParentIdDTO;

import java.util.regex.Pattern;

public class ParentFolderUtils {

    private static final Pattern PARENT_ID_PATTERN = Pattern.compile("^[0-9a-fA-F]{24}$");

    /**
     * 解析父级ID为MongoDB查询条件
     * 
     * @param parentId 父级ID
     * @return MongoDB查询条件
     */
    public static ParentIdDTO parseParentIdInMongo(String parentId) {
        if (parentId == null || parentId.isEmpty()) {
            return new  ParentIdDTO();
        }
        // reg: /^[0-9a-fA-F]{24}$/ 根据reg验证parentId
        if (PARENT_ID_PATTERN.matcher(parentId).matches()) {
            ParentIdDTO parentIdDTO = new ParentIdDTO();
            parentIdDTO.setParentId(parentId);
            return parentIdDTO;
        }
        return new  ParentIdDTO();
    }
} 