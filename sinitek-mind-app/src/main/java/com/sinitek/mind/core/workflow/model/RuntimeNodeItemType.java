package com.sinitek.mind.core.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class RuntimeNodeItemType {

    private String nodeId;
    private String name;
    private String avatar;
    private String intro;
    private String flowNodeType;
    private Boolean showStatus;
    private Boolean isEntry;

    private List<FlowNodeInputItemType> inputs;
    private List<FlowNodeOutputItemType> outputs;

    private String pluginId;
    private String version;

    private NodeToolConfigType toolConfig;
}
