package com.sinitek.mind.core.chat.model;

import com.sinitek.mind.core.workflow.model.AIChatItemValue;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 聊天历史记录项
 */
@Data
@Builder
public class ChatHistoryItem {
    
    /**
     * 记录ID
     */
    private String id;
    
    /**
     * 对话ID
     */
    private String chatId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 消息类型
     */
    private String messageType;
    
    /**
     * 角色
     */
    private String role;
    
    /**
     * 消息内容
     */
    private String content;
    
    /**
     * 消息值
     */
    private List<AIChatItemValue> value;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 更新时间
     */
    private Date updateTime;
    
    /**
     * 元数据
     */
    private Map<String, Object> metadata;
}