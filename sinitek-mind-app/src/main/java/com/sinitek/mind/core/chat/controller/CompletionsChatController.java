package com.sinitek.mind.core.chat.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.entity.AppVersion;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.repository.AppVersionRepository;
import com.sinitek.mind.core.app.service.IAppService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.core.app.util.PluginUtils;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.dto.SaveChatDTO;
import com.sinitek.mind.core.chat.dto.V2ChatCompletionRequest;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.entity.ChatItem;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.enumerate.ChatSourceEnum;
import com.sinitek.mind.core.chat.model.*;
import com.sinitek.mind.core.chat.repository.ChatItemRepository;
import com.sinitek.mind.core.chat.repository.ChatRepository;
import com.sinitek.mind.core.chat.service.IChatService;
import com.sinitek.mind.core.chat.util.ChatUtil;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.WorkflowResponseWriter;
import com.sinitek.mind.core.workflow.service.IWorkflowService;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.support.openapi.context.OpenApiContext;
import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import com.sinitek.mind.support.openapi.service.IOpenApiAuthService;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.mind.support.outlink.dto.AppInfoDTO;
import com.sinitek.mind.support.outlink.dto.OutLinkAuthResultDTO;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.mind.support.outlink.service.IOutLinkService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.mind.support.wallet.util.WalletUtil;
import com.sinitek.mind.system.support.SystemGlobals;
import com.sinitek.sirm.common.utils.IpUtil;
import com.sinitek.sirm.framework.annotation.ResponseObjectOnly;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executor;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * chat聊天的openapi接口，v2版本
 *
 * <AUTHOR>
 * @date 2025/7/15
 */
@RestController("chatController-v2")
@RequestMapping("/mind")
@Tag(name = "对话管理-v2", description = "聊天相关接口")
@Slf4j
public class CompletionsChatController {

    @Autowired
    private IAuthOutLinkService authOutLinkService;

    @Autowired
    private IOutLinkService outLinkService;

    @Autowired
    private IChatService chatService;

    @Autowired
    private ChatRepository chatRepository;

    @Autowired
    private IAppService appService;

    @Autowired
    private IPermissionService permissionService;

    @Autowired
    private ChatItemRepository chatItemRepository;

    @Autowired
    private AppVersionRepository appVersionRepository;

    @Autowired
    private IWorkflowService workflowService;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private IOpenApiService openApiService;

    @Autowired
    private IOpenApiAuthService openApiAuthService;

    @Autowired
    private IAuthAppService authAppService;

    @Autowired
    private IAuthService authService;

    @Autowired
    @Qualifier("sinicubeAsyncThreadPool")
    private Executor taskExecutor;

    @PostMapping("/api/v2/chat/completions")
    @Operation(summary = "聊天-v2", description = "执行聊天接口")
    @ResponseObjectOnly
    public SseEmitter v2Completions(@RequestBody V2ChatCompletionRequest chatRequest, HttpServletRequest request) {
        SseEmitter sseEmitter = new SseEmitter(300000L);

        Supplier<AuthResultType> supplier = () -> authApiByToken(chatRequest, request);

        syncCompletions(chatRequest, request, sseEmitter, supplier);
        // 默认回复的都是流式输出
        return sseEmitter;
    }

    @PostMapping("/open-api/v2/chat/completions")
    @Operation(summary = "聊天-分享链接或OpenApi", description = "执行聊天接口")
    @ResponseObjectOnly
    public SseEmitter shareCompletions(@RequestBody V2ChatCompletionRequest chatRequest, HttpServletRequest request) {
        SseEmitter sseEmitter = new SseEmitter(300000L);

        // 验证逻辑函数，这样验证过程出错，也可以被捕获
        Supplier<AuthResultType> supplier = () -> {
            List<ChatCompletionMessageParam> messages = chatRequest.getMessages();
            String shareId = chatRequest.getShareId();
            String outLinkUid = chatRequest.getOutLinkUid();
            Map<String, Object> variables = chatRequest.getVariables();

            List<ChatItemType> chatMessages = ChatAdaptor.gptMessages2Chats(messages, true);

            String startHookText = generateStartHookText(chatMessages, variables);
            AuthResultType authResultType;
            if (StringUtils.isNotBlank(shareId) && StringUtils.isNotBlank(outLinkUid)) {
                authResultType = authShareChatStart(chatRequest, startHookText);
            } else {
                authResultType = authApiByOpenApi(chatRequest, request);
            }
            return authResultType;
        };

        syncCompletions(chatRequest, request, sseEmitter, supplier);

        // 默认回复的都是流式输出
        return sseEmitter;
    }

    private void syncCompletions(V2ChatCompletionRequest chatRequest, HttpServletRequest request, SseEmitter sseEmitter,
                                 Supplier<AuthResultType> function) {
        CompletableFuture.runAsync(() -> {
            String chatId = chatRequest.getChatId();
            String appId = chatRequest.getAppId();
            String customUid = chatRequest.getCustomUid();

            String shareId = chatRequest.getShareId();
            String outLinkUid = chatRequest.getOutLinkUid();

            String spaceTeamId = chatRequest.getTeamId();
            String teamToken = chatRequest.getTeamToken();

            Boolean stream = chatRequest.getStream();
            Boolean detail = chatRequest.getDetail();
            Boolean retainDatasetCite = chatRequest.getRetainDatasetCite();
            List<ChatCompletionMessageParam> messages = chatRequest.getMessages();
            Map<String, Object> variables = chatRequest.getVariables();
            String responseChatItemId = chatRequest.getResponseChatItemId();
            Map<String, Object> metadata = chatRequest.getMetadata();

            long startTime = System.currentTimeMillis();

            if (CollUtil.isEmpty(messages)) {
                throw new BussinessException("messages is not array");
            }

            List<ChatItemType> chatMessages = ChatAdaptor.gptMessages2Chats(messages, true);

            // 原有的校验逻辑
            AuthResultType authResultType = function.get();
//            AuthResultType authResultType = new AuthResultType();
//            if (StringUtils.isNotBlank(shareId) && StringUtils.isNotBlank(outLinkUid)) {
//                authResultType = authShareChatStart(chatRequest, startHookText);
//            } else if (StringUtils.isNotBlank(spaceTeamId) && StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(teamToken)) {
//                authResultType = authTeamChatStart(chatRequest);
//            } else {
//                authResultType = authApiOrToken(chatRequest, request);
//            }

            String teamId = authResultType.getTeamId();
            String tmbId = authResultType.getTmbId();
            App app = authResultType.getApp();
            String apikey = authResultType.getApikey();
            String authType = authResultType.getAuthType();
            String sourceName = authResultType.getSourceName();
            Boolean responseAllData = authResultType.getResponseAllData();
            Boolean responseDetail = authResultType.getResponseDetail();
            String outLinkUserId = authResultType.getOutLinkUserId();
            Boolean showNodeStatus = authResultType.getShowNodeStatus();

            retainDatasetCite = retainDatasetCite && responseDetail;
            boolean isPlugin = StringUtils.equals(app.getType(), AppTypeEnum.PLUGIN.getValue());

            // check message type
            if (isPlugin) {
                detail = true;
            } else {
                if (messages.isEmpty()) {
                    throw new BussinessException("messages is empty");
                }
            }

            // Get obj=human history
            ChatItemType userQuestion = getUserQuestion(isPlugin, app, variables, chatMessages);

            // Get and concat history;
            int limit = WorkflowUtil.getMaxHistoryLimitFromNodes(app.getModules());
            Pageable pageable = PageRequest.of(0, limit);
            Page<ChatItem> chatItemPage = chatItemRepository.findByChatIdAndAppIdOrderByTimeDesc(chatId, app.getId(), pageable);
            List<ChatItemType> histories = chatItemPage.get()
                    .map(chatItem -> {
                        ChatItemType chatItemType = new ChatItemType();
                        // 只需要设置dataId、obj、value、nodeOutputs(没有找到该字段)
                        chatItemType.setDataId(chatItem.getDataId());
                        chatItemType.setObj(ChatRoleEnum.getByValue(chatItem.getObj()));
                        chatItemType.setValue(chatItem.getValue());
                        return chatItemType;
                    })
                    // 最早的记录在前面
                    .sorted(Comparator.comparing(ChatItemType::getDataId))
                    .toList();
            List<StoreNodeItemType> nodes;
            List<StoreEdgeItemType> edges;
            AppChatConfigType chatConfig;
            Optional<AppVersion> appVersionOpt = appVersionRepository.findLatestByAppId(new ObjectId(app.getId()));
            if (appVersionOpt.isPresent()) {
                AppVersion appVersion = appVersionOpt.get();
                nodes = appVersion.getNodes();
                edges = appVersion.getEdges();
                chatConfig = appVersion.getChatConfig();
            } else {
                nodes = app.getModules();
                edges = app.getEdges();
                chatConfig = app.getChatConfig();
            }
            Chat chatDetail = chatRepository.findByAppIdAndChatId(app.getId(), chatId).orElse(new Chat());

            // Get store variables(Api variable precedence)
            if (chatDetail.getVariables() != null) {
                Map<String, Object> tempMap = new HashMap<>(chatDetail.getVariables());
                tempMap.putAll(variables);
                variables = tempMap;
            }

            // Get chat histories
            List<ChatItemType> newHistories = ChatUtil.concatHistories(histories, chatMessages);
            WorkflowInteractiveResponseType interactive = WorkflowUtil.getLastInteractiveValue(newHistories);

            // Get runtimeNodes
            List<RuntimeNodeItemType> tempRuntimeNodes = WorkflowUtil.storeNodes2RuntimeNodes(nodes, WorkflowUtil.getWorkflowEntryNodeIds(nodes, interactive));
            // 如果是插件根据变量更新插件输入
            if (isPlugin) {
                tempRuntimeNodes = WorkflowUtil.updatePluginInputByVariables(tempRuntimeNodes, variables);
                variables = new HashMap<>();
            }
            List<RuntimeNodeItemType> runtimeNodes = WorkflowUtil.rewriteNodeOutputByHistories(tempRuntimeNodes, interactive);

            WorkflowResponseConfig workflowResponseConfig = WorkflowResponseConfig.builder()
                    .sseEmitter(sseEmitter)
                    .detail(detail)
                    .streamResponse(stream)
                    .id(chatId)
                    .showNodeStatus(showNodeStatus)
                    .build();

            /* start flow controller */
            if (!StringUtils.equals(app.getVersion(), "v2")) {
                throw new BussinessException("您的工作流版本过低，请重新发布一次");
            }

            Map<String, Object> finalVariables = variables;
            Boolean finalRetainDatasetCite = retainDatasetCite;
            ChatDispatchProps dispatchProps = ChatDispatchProps.builder()
                    .res(sseEmitter)
                    .requestOrigin(request.getHeader("origin"))
                    .mode("chat")
                    .timezone("Asia/Shanghai")
                    // 暂时没有，需要在校验函数中提供
                    .externalProvider(new ExternalProviderType())
                    .runningAppInfo(RunningAppInfo.builder()
                            .id(app.getId())
                            .teamId(teamId)
                            .tmbId(tmbId)
                            .build())
                    .runningUserInfo(RunningUserInfo.builder()
                            .teamId(teamId)
                            .tmbId(tmbId)
                            .build())
                    .uid(tmbId)
                    .chatId(chatId)
                    .responseChatItemId(responseChatItemId)
                    .runtimeNodes(runtimeNodes)
                    .runtimeEdges(WorkflowUtil.storeEdges2RuntimeEdges(edges, interactive))
                    .variables(finalVariables)
                    .query(ChatUtil.removeEmptyUserInput(userQuestion.getValue()))
                    .lastInteractive(interactive)
                    .chatConfig(chatConfig)
                    .histories(newHistories)
                    .stream(stream)
                    .retainDatasetCite(finalRetainDatasetCite)
                    .maxRunTimes(SystemGlobals.getSecurityEnv().getWorkflowMaxRunTimes())
                    .workflowStreamResponse(WorkflowUtil.getWorkflowResponseWrite(workflowResponseConfig))
                    .version("v2")
                    .responseAllData(responseAllData)
                    .responseDetail(responseDetail)
                    .build();

            // 执行工作流并获取结果
            DispatchFlowResponse dispatchFlowResponse;
            try {
                dispatchFlowResponse = workflowService.dispatchWorkFlow(dispatchProps).get();
            } catch (ExecutionException | InterruptedException e) {
                log.error("工作流执行失败: {}", e.getMessage(), e);
                throw new RuntimeException("工作流执行失败: " + e.getMessage(), e);
            }

            // 获取执行结果
            List<ChatHistoryItemResType> flowResponses = dispatchFlowResponse.getFlowResponses();
            List<ChatNodeUsageType> flowUsage = dispatchFlowResponse.getFlowUsage();
            List<AIChatItemValueItemType> assistantResponses = dispatchFlowResponse.getAssistantResponses();
            Map<String, Object> system_memories = dispatchFlowResponse.getSystem_memories();
            Map<String, Object> newVariables = dispatchFlowResponse.getNewVariables();
            double durationSeconds = dispatchFlowResponse.getDurationSeconds();

            // save chat
            boolean isOwnerUser = StringUtils.isBlank(shareId)
                    && StringUtils.isBlank(spaceTeamId) && StringUtils.equals(tmbId, app.getTmbId());
            ChatSourceEnum source = ChatSourceEnum.ONLINE;
            if (StringUtils.isNotBlank(shareId)) {
                source = ChatSourceEnum.SHARE;
            } else if (StringUtils.equals(authType, "apikey")) {
                source = ChatSourceEnum.API;
            } else if (StringUtils.isNotBlank(spaceTeamId)) {
                source = ChatSourceEnum.TEAM;
            }

            boolean isInteractiveRequest = WorkflowUtil.getLastInteractiveValue(newHistories) != null;
            String userInteractiveVal = ChatAdaptor.chatValue2RuntimePrompt(userQuestion.getValue()).getText();

            String newTitle = "";
            if (isPlugin) {
                String cTime = (String) variables.get("cTime");
                if (!StringUtils.isBlank(cTime)) {
                    newTitle = cTime;
                } else {
                    // TODO 格式不确定是什么样子，暂写成当前的
                    newTitle = DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss");
                }
            } else {
                newTitle = ChatUtil.getChatTitleFromChatMessage(userQuestion);
            }

            ChatItemType aiResponse = new ChatItemType();
            aiResponse.setDataId(responseChatItemId);
            aiResponse.setObj(ChatRoleEnum.AI);
            aiResponse.setValue(objectMapper.convertValue(
                    assistantResponses,
                    new TypeReference<List<ChatItemValueItemType>>() {
                    }));
            aiResponse.setMemories(system_memories);
            aiResponse.setResponseData(flowResponses);

            String saveChatId = ObjectId.get().toString();
            if (StringUtils.isNotBlank(chatId)) {
                saveChatId = chatId;
            }
            if (isInteractiveRequest) {
                chatService.updateInteractiveChat(saveChatId, app.getId(), userInteractiveVal, aiResponse, newVariables, durationSeconds);
            } else {
                boolean isUpdateUseTime = isOwnerUser && StringUtils.equals(source.getValue(), ChatSourceEnum.ONLINE.getValue());
                Map<String, Object> newMetadata = new HashMap<>();
                newMetadata.put("originIp", IpUtil.getIpAddr(request));
                if (!Objects.isNull(metadata)) {
                    newMetadata.putAll(metadata);
                }
                SaveChatDTO dto = SaveChatDTO.builder()
                        .chatId(saveChatId)
                        .appId(app.getId())
                        .teamId(teamId)
                        .tmbId(tmbId)
                        .nodes(nodes)
                        .appChatConfig(chatConfig)
                        .variables(newVariables)
                        .isUpdateUseTime(isUpdateUseTime)
                        .newTitle(newTitle)
                        .shareId(shareId)
                        .outLinkUid(outLinkUserId)
                        .source(source.getValue())
                        .sourceName(sourceName)
                        .content(List.of(userQuestion, aiResponse))
                        .metadata(newMetadata)
                        .durationSeconds(durationSeconds)
                        .build();
                chatService.saveChat(dto);
            }

            log.info("completions running time: {}s", (System.currentTimeMillis() - startTime) / 1000);

            /* select fe response field */
            List<ChatHistoryItemResType> feResponseData = responseAllData ? flowResponses
                    : WorkflowUtil.filterPublicNodeResponseData(flowResponses, responseDetail);

            if (stream) {
                WorkflowResponseWriter.sseComplete(sseEmitter);
            } else {
                // 非流式输出，构建结果
                Map<String, Object> responseMap = new HashMap<>();

                Object responseContent = getResponseContent(assistantResponses, detail);
                Object formatResponseContent = ChatUtil.removeAIResponseCite(responseContent, retainDatasetCite);

                Object error = new Object();
                if (!flowResponses.isEmpty()) {
                    error = flowResponses.get(flowResponses.size() - 1).getError();
                }

                if (detail) {
                    responseMap.put("responseData", feResponseData);
                    responseMap.put("newVariables", newVariables);
                }
                responseMap.put("error", error);
                responseMap.put("id", chatId);
                responseMap.put("model", "");

                Map<String, Integer> tokenMap = new HashMap<>();
                tokenMap.put("prompt_tokens", 1);
                tokenMap.put("completion_tokens", 1);
                tokenMap.put("total_tokens", 1);
                responseMap.put("usage", tokenMap);

                Map<String, Object> messageMap = new HashMap<>();
                messageMap.put("role", "assistant");
                messageMap.put("content", formatResponseContent);

                // 创建外部 Map 来存储整体信息
                Map<String, Object> choiceMap = new HashMap<>();
                choiceMap.put("message", messageMap);
                choiceMap.put("finish_reason", "stop");
                choiceMap.put("index", 0);
                responseMap.put("choices", choiceMap);
            }

            // add record
            int totalPoints = chatService.createChatUsage(app.getName(), app.getId(), teamId, tmbId,
                    WalletUtil.getUsageSourceByAuthType(shareId, authType).getValue(), flowUsage);

            if (StringUtils.isNotBlank(shareId)) {
                authOutLinkService.authOutLinkChatFinish(shareId, outLinkUid, totalPoints);
                outLinkService.addOutLinkUsage(shareId, totalPoints);
            }

            if (StringUtils.isNotBlank(apikey)) {
                openApiService.recordUsage(apikey, (long) totalPoints);
            }

        }, taskExecutor).exceptionally(e -> {
            // 处理异常
            log.error("工作流执行异常: {}", e.getMessage(), e);
            WorkflowResponseWriter.sseErrRes(sseEmitter, e);
            return null;
        });
    }

    /**
     * 生成开始文本
     *
     * @param chatMessages
     * @param variables
     * @return
     */
    private String generateStartHookText(List<ChatItemType> chatMessages, Map<String, Object> variables) {
        ChatItemType userQuestion = chatMessages.get(chatMessages.size() - 1);
        if (ObjectUtil.equals(userQuestion.getObj(), ChatRoleEnum.HUMAN)) {
            RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(userQuestion.getValue());
            return runtimePromptType.getText();
        }

        return JSONUtil.toJsonStr(variables);
    }

    /**
     * 验证外链聊天
     *
     * @return
     */
    private AuthResultType authShareChatStart(V2ChatCompletionRequest chatRequest, String question) {
        String shareId = chatRequest.getShareId();
        String outLinkUid = chatRequest.getOutLinkUid();
        String chatId = chatRequest.getChatId();
        OutLinkAuthResultDTO outLinkAuthResultDTO = authOutLinkService.authOutLinkChatStart(shareId, outLinkUid, question);
        String uid = outLinkAuthResultDTO.getUid();
        AppInfoDTO appInfoDTO = outLinkService.getAppByShareId(shareId);

        // 需要在openApi上下文添加用户的基础信息，防止后续工作流执行过程中报错
        OpenApiAuthResultDTO openApiAuthResultDTO = new OpenApiAuthResultDTO();
        openApiAuthResultDTO.setTeamId(outLinkAuthResultDTO.getTeamId());
        openApiAuthResultDTO.setTmbId(outLinkAuthResultDTO.getTmbId());
        openApiAuthResultDTO.setSourceName(outLinkAuthResultDTO.getSourceName());
        openApiAuthResultDTO.setAppId(outLinkAuthResultDTO.getAppId());
        OpenApiContext.setAuth(openApiAuthResultDTO);

        // TODO 需要进行限流的判断

        // 验证app
        if (appInfoDTO == null) {
            throw new BussinessException("app不存在");
        }

        // 验证chat
        Optional<Chat> chatOpt = chatRepository.findById(chatId);
        if (chatOpt.isPresent() && (ObjectUtil.notEqual(chatOpt.get().getShareId(), shareId) || ObjectUtil.notEqual(chatOpt.get().getOutLinkUid(), uid))) {
            throw new BussinessException("Chat校验未通过");
        }

        String appId = outLinkAuthResultDTO.getAppId();
        App app = appService.getAppDetail(appId);

        AuthResultType authResultType = new AuthResultType();
        authResultType.setTeamId(outLinkAuthResultDTO.getTeamId());
        authResultType.setTmbId(outLinkAuthResultDTO.getTmbId());
        authResultType.setApp(app);
        authResultType.setApikey("");
        authResultType.setAuthType(outLinkAuthResultDTO.getAuthType());
        authResultType.setSourceName(outLinkAuthResultDTO.getSourceName());
        authResultType.setResponseAllData(false);
        authResultType.setResponseDetail(outLinkAuthResultDTO.getResponseDetail());
        authResultType.setOutLinkUserId(outLinkAuthResultDTO.getUid());
        authResultType.setShowNodeStatus(outLinkAuthResultDTO.getShowNodeStatus());
        return authResultType;
    }

    /**
     * 验证团队聊天
     *
     * @return
     */
    private AuthResultType authTeamChatStart(V2ChatCompletionRequest chatRequest) {
        String teamId = chatRequest.getTeamId();
        String teamToken = chatRequest.getTeamToken();
        String chatId = chatRequest.getChatId();
        String appId = chatRequest.getAppId();

        // TODO 找不到对应的接口（/support/user/team/tag/authTeamToken）

        return new AuthResultType();
    }

    /**
     * 使用token验证apis
     *
     * @return
     */
    private AuthResultType authApiByToken(V2ChatCompletionRequest chatRequest, HttpServletRequest request) {
        AuthDTO authDTO = authService.authCert();
        // 使用token调用
        String tmbId = authDTO.getTmbId();
        String teamId = authDTO.getTeamId();
        String appId = chatRequest.getAppId();
        String authType = "token";
        String apiKey = "";
        String sourceName = "";

        if (StringUtils.isBlank(appId)) {
            throw new BussinessException("appId is empty");
        }

        authAppService.authApp(appId, PermissionConstant.READ_PER);

        App app = appService.getAppDetail(appId);

        appId = app.getId();
        Optional<Chat> chat = chatRepository.findByAppIdAndChatId(appId, chatRequest.getChatId());

        if (chat.isPresent() && (!StringUtils.equals(chat.get().getTeamId(), teamId) ||
                (StringUtils.equals(authType, "token") && !StringUtils.equals(chat.get().getTmbId(), tmbId)))) {
            throw new BussinessException("chat 校验失败");
        }

        AuthResultType authResultType = new AuthResultType();
        authResultType.setTeamId(teamId);
        authResultType.setTmbId(tmbId);
        authResultType.setApp(app);
        authResultType.setApikey(apiKey);
        authResultType.setAuthType(authType);
        authResultType.setSourceName(sourceName);
        authResultType.setResponseAllData(true);
        authResultType.setResponseDetail(true);
        return authResultType;
    }

    /**
     * 使用OpenApi验证Api
     */
    private AuthResultType authApiByOpenApi(V2ChatCompletionRequest chatRequest, HttpServletRequest request) {
        String apiKey = ApiKeyUtil.extractApiKey(request);
        if (StringUtils.isBlank(apiKey)) {
            throw new BussinessException("API Key is empty");
        }

        // 通过api令牌调用
        OpenApiAuthResultDTO authResult = openApiAuthService.authenticateApiKey(apiKey);

        String teamId = authResult.getTeamId();
        String appId = authResult.getAppId();
        String tmbId = authResult.getTmbId();
        String sourceName = authResult.getSourceName();
        String authType = "apikey";

        String currentAppId = StringUtils.isNotBlank(appId) ? appId : chatRequest.getAppId();
        if (StringUtils.isBlank(currentAppId)) {
            throw new BussinessException("Key is error. You need to use the app key rather than the account key.");
        }

        App app = appService.getAppDetail(currentAppId);
        if (app == null) {
            throw new BussinessException("app is empty");
        }



        appId = app.getId();
        Optional<Chat> chat = chatRepository.findByAppIdAndChatId(appId, chatRequest.getChatId());

        if (chat.isPresent() && (!StringUtils.equals(chat.get().getTeamId(), teamId) ||
                (StringUtils.equals(authType, "token") && !StringUtils.equals(chat.get().getTmbId(), tmbId)))) {
            throw new BussinessException("chat 校验失败");
        }

        AuthResultType authResultType = new AuthResultType();
        authResultType.setTeamId(teamId);
        authResultType.setTmbId(tmbId);
        authResultType.setApp(app);
        authResultType.setApikey(apiKey);
        authResultType.setAuthType(authType);
        authResultType.setSourceName(sourceName);
        authResultType.setResponseAllData(true);
        authResultType.setResponseDetail(true);
        return authResultType;
    }

    private ChatItemType getUserQuestion(Boolean isPlugin, App app, Map<String, Object> variables, List<ChatItemType> chatMessages) {
        if (isPlugin) {
            return WorkflowUtil.getPluginRunUserQuery(
                    PluginUtils.getPluginInputsFromStoreNodes(app.getModules()),
                    variables,
                    ((List<ChatItemValueItemFileInfo>) variables.get("files"))
            );
        }

        ChatItemType lastHumanChat = chatMessages.get(chatMessages.size() - 1);
        if (lastHumanChat == null) {
            throw new BussinessException("User question is empty");
        }
        return lastHumanChat;
    }

    private Object getResponseContent(List<AIChatItemValueItemType> assistantResponses, boolean detail) {
        if (assistantResponses.isEmpty()) {
            return "";
        }
        if (assistantResponses.size() == 1 && assistantResponses.get(0).getText() != null && assistantResponses.get(0).getText().getContent() != null) {
            return assistantResponses.get(0).getText().getContent();
        }
        if (!detail) {
            return assistantResponses.stream()
                    .map(item -> item != null && item.getText() != null ? item.getText().getContent() : null)
                    .filter(item -> item != null && !item.isEmpty())
                    .collect(Collectors.joining("\n"));
        }
        return assistantResponses;
    }

    // 验证结果内部类
    @Data
    private class AuthResultType {
        private String teamId;
        private String tmbId;
        //        暂时不实现，时区固定东八区，全局变量目前没有应用场景
//        private String timezone;
//        private Object externalProvider;
        private App app;
        private String apikey;
        private String authType;
        private String sourceName;
        private Boolean responseAllData = false;
        private Boolean responseDetail = false;
        private String outLinkUserId;
        private Boolean showNodeStatus = false;
    }
}
