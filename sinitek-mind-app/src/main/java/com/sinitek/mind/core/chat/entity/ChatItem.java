package com.sinitek.mind.core.chat.entity;

import com.sinitek.mind.core.chat.model.AdminFeedback;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "chatitems")
public class ChatItem {
    @Id
    private String id;

    private String teamId;

    private String tmbId;

    private String userId;

    private String chatId;

    private String dataId;

    @Field(targetType = FieldType.OBJECT_ID)
    private String appId;

    private Date time;

    private boolean hideInUI;

    private String obj;

    private List<ChatItemValueItemType> value;

    private Object memories;

    private String errorMsg;

    private String userGoodFeedback;

    private String userBadFeedback;

    private List<String> customFeedback;

    private AdminFeedback adminFeedback;

    private List<Object> responseData;

    private double durationSeconds;
}
