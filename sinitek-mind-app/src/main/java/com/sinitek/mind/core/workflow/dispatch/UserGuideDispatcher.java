package com.sinitek.mind.core.workflow.dispatch;

import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component("userGuideDispatcher")
public class UserGuideDispatcher implements NodeDispatcher {
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return dispatchData.getParams();
    }
}
