package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum HeaderSecretType {
    BEARER("Bearer"),
    CUSTOM("custom");

    private final String value;

    HeaderSecretType(String value) {
        this.value = value;
    }

    public static HeaderSecretType fromValue(String value) {
        for (HeaderSecretType type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown HeaderSecretType: " + value);
    }
}