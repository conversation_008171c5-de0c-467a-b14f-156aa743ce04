package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum FlowNodeOutputTypeEnum {

    HIDDEN("hidden"),
    SOURCE("source"),
    STATIC("static"),
    DYNAMIC("dynamic");

    private final String value;

    FlowNodeOutputTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static FlowNodeOutputTypeEnum fromValue(String value) {
        for (FlowNodeOutputTypeEnum type : FlowNodeOutputTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的节点输出类型: " + value);
    }
}
