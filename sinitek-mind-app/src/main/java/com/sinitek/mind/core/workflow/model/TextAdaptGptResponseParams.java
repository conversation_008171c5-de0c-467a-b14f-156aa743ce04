package com.sinitek.mind.core.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TextAdaptGptResponseParams {
    private String model;
    private String text;
    private String reasoningContent;
    private String finishReason;
    private Map<String, Object> extraData;
}