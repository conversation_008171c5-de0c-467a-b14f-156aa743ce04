package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.enumerate.WorkflowIOValueTypeEnum;
import com.sinitek.mind.core.app.model.JsonSchemaPropertiesItemType;
import lombok.Data;

@Data
public class ToolValueTypeItem {
    // Getters
    private String label;
    private WorkflowIOValueTypeEnum value;
    private JsonSchemaPropertiesItemType jsonSchema;

    public ToolValueTypeItem(String label, WorkflowIOValueTypeEnum value, JsonSchemaPropertiesItemType jsonSchema) {
        this.label = label;
        this.value = value;
        this.jsonSchema = jsonSchema;
    }

}