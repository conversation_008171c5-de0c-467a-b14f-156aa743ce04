package com.sinitek.mind.core.app.util;

import org.quartz.CronExpression;

import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.TimeZone;

/**
 * 时间工具类
 * 对应原始的时间格式化函数
 */
public class TimeUtils {
    
    // 常用的时间格式
    private static final DateTimeFormatter YMDHM_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    private static final DateTimeFormatter YMDHMS_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final DateTimeFormatter YMD_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YMDHM_CN_FORMATTER = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm");
    private static final DateTimeFormatter YMDHMS_CN_FORMATTER = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss");
    
    /**
     * 格式化时间为 yyyy-MM-dd HH:mm 格式
     * 对应原始的formatTime2YMDHM函数
     * 
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMDHM(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(YMDHM_FORMATTER);
    }
    
    /**
     * 格式化时间为 yyyy-MM-dd HH:mm 格式
     * 重载方法，支持Date类型
     * 
     * @param date 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMDHM(Date date) {
        if (date == null) {
            return "";
        }
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return formatTime2YMDHM(dateTime);
    }
    
    /**
     * 格式化时间为 yyyy-MM-dd HH:mm:ss 格式
     * 对应原始的formatTime2YMDHMS函数
     * 
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMDHMS(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(YMDHMS_FORMATTER);
    }

    /**
     * 根据cron表达式和时区获取下一次触发时间
     * @param cronString cron表达式
     * @param timezone 时区ID (e.g., "Asia/Shanghai")
     * @return 下一次触发时间，解析失败时返回2099-01-01
     */
    public static Date getNextTimeByCronStringAndTimezone(String cronString, String timezone) {
        try {
            // 验证cron表达式格式
            if (!CronExpression.isValidExpression(cronString)) {
                throw new IllegalArgumentException("Invalid cron expression");
            }

            // 获取当前时间（默认时区）
            Date currentDate = new Date();

            // 创建带时区的cron表达式
            CronExpression cron = new CronExpression(cronString);
            cron.setTimeZone(TimeZone.getTimeZone(timezone));

            // 计算下一次触发时间
            return cron.getNextValidTimeAfter(currentDate);
        } catch (ParseException | IllegalArgumentException e) {
            // 错误处理：返回2099-01-01
            return new Date(4070908800000L); // 2099-01-01 00:00:00 UTC
        }
    }
    
    /**
     * 格式化时间为 yyyy-MM-dd HH:mm:ss 格式
     * 重载方法，支持Date类型
     * 
     * @param date 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMDHMS(Date date) {
        if (date == null) {
            return "";
        }
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return formatTime2YMDHMS(dateTime);
    }
    
    /**
     * 格式化时间为 yyyy-MM-dd 格式
     * 对应原始的formatTime2YMD函数
     * 
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMD(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(YMD_FORMATTER);
    }
    
    /**
     * 格式化时间为 yyyy-MM-dd 格式
     * 重载方法，支持Date类型
     * 
     * @param date 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMD(Date date) {
        if (date == null) {
            return "";
        }
        LocalDateTime dateTime = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        return formatTime2YMD(dateTime);
    }
    
    /**
     * 格式化时间为中文格式 yyyy年MM月dd日 HH:mm
     * 对应原始的formatTime2YMDHM_CN函数
     * 
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMDHM_CN(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(YMDHM_CN_FORMATTER);
    }
    
    /**
     * 格式化时间为中文格式 yyyy年MM月dd日 HH:mm:ss
     * 对应原始的formatTime2YMDHMS_CN函数
     * 
     * @param dateTime 时间对象
     * @return 格式化后的时间字符串
     */
    public static String formatTime2YMDHMS_CN(LocalDateTime dateTime) {
        if (dateTime == null) {
            return "";
        }
        return dateTime.format(YMDHMS_CN_FORMATTER);
    }
    
    /**
     * 获取当前时间的YMDHM格式字符串
     * 
     * @return 当前时间的格式化字符串
     */
    public static String getCurrentYMDHM() {
        return formatTime2YMDHM(LocalDateTime.now());
    }
    
    /**
     * 获取当前时间的YMDHMS格式字符串
     * 
     * @return 当前时间的格式化字符串
     */
    public static String getCurrentYMDHMS() {
        return formatTime2YMDHMS(LocalDateTime.now());
    }
    
    /**
     * 获取当前日期的YMD格式字符串
     * 
     * @return 当前日期的格式化字符串
     */
    public static String getCurrentYMD() {
        return formatTime2YMD(LocalDateTime.now());
    }
    
    /**
     * 将Date转换为LocalDateTime
     * 
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }
    
    /**
     * 将LocalDateTime转换为Date
     * 
     * @param dateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date localDateTimeToDate(LocalDateTime dateTime) {
        if (dateTime == null) {
            return null;
        }
        return Date.from(dateTime.atZone(ZoneId.systemDefault()).toInstant());
    }
    
    /**
     * 判断时间是否为今天
     * 
     * @param dateTime 时间对象
     * @return 是否为今天
     */
    public static boolean isToday(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return dateTime.toLocalDate().equals(now.toLocalDate());
    }
    
    /**
     * 判断时间是否为昨天
     * 
     * @param dateTime 时间对象
     * @return 是否为昨天
     */
    public static boolean isYesterday(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        LocalDateTime now = LocalDateTime.now();
        return dateTime.toLocalDate().equals(now.toLocalDate().minusDays(1));
    }
    
    /**
     * 计算两个时间之间的分钟差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 分钟差
     */
    public static long getMinutesBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return java.time.Duration.between(start, end).toMinutes();
    }
    
    /**
     * 计算两个时间之间的小时差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 小时差
     */
    public static long getHoursBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return java.time.Duration.between(start, end).toHours();
    }
    
    /**
     * 计算两个时间之间的天数差
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 天数差
     */
    public static long getDaysBetween(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return java.time.Duration.between(start, end).toDays();
    }
}