package com.sinitek.mind.core.dataset.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * 带RRF分数的搜索数据
 */
@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SearchDataWithRrfScore extends SearchDataResponseItemType {
    private double rrfScore;
}