package com.sinitek.mind.core.dataset.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class DefaultSearchDatasetDataProps extends SearchDatasetDataProps {

    private String datasetSearchExtensionModel;

    private Boolean datasetSearchUsingExtensionQuery;

    private String datasetSearchExtensionBg;
}
