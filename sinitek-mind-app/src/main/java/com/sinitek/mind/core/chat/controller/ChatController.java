package com.sinitek.mind.core.chat.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.chat.dto.*;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.entity.ChatItem;
import com.sinitek.mind.core.chat.service.IChatService;
import com.sinitek.mind.core.workflow.model.sse.WorkflowResponseWriter;
import com.sinitek.mind.support.outlink.dto.ChatInitResponse;
import com.sinitek.mind.support.outlink.service.IOutLinkService;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.framework.annotation.ResponseObjectOnly;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.constraints.NotBlank;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/mind/api/core/chat")
@RequiredArgsConstructor
@Tag(name = "对话管理", description = "聊天相关接口")
public class ChatController {

    private final IChatService chatService;

    private final IOutLinkService outLinkService;

    @GetMapping("/init")
    @Operation(summary = "初始化聊天", description = "根据appId和chatId初始化聊天会话")
    public ApiResponse<InitChatResponse> initChat(
            @RequestParam(value = "appId", required = false) String appId,
            @RequestParam(value = "chatId", required = false) String chatId) {

        try {
            // 参数校验
            if (appId == null || appId.trim().isEmpty()) {
                throw new BussinessException("You don't have an app yet");
            }

            InitChatRequest request = new InitChatRequest();
            request.setAppId(appId);
            request.setChatId(chatId);

            InitChatResponse response = chatService.initChat(request);
            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("初始化聊天失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

    @PostMapping("/getPaginationRecords")
    @Operation(summary = "分页获取聊天记录", description = "根据应用ID和聊天ID分页获取聊天记录")
    public ApiResponse<PageResult<ChatItem>> getPaginationRecords(
            @RequestBody GetPaginationRecordsRequest request) {
        try {
            if (!StringUtils.hasText(request.getAppId())) {
                throw new RuntimeException("缺少参数：appId");
            }
            if (!StringUtils.hasText(request.getChatId())) {
                throw new RuntimeException("缺少参数：chatId");
            }
            if (request.getOffset() < 0) {
                request.setOffset(0);
            }
            if (request.getPageSize() <= 0 || request.getPageSize() > 100) {
                request.setPageSize(20);
            }
            PageResult<ChatItem> result = chatService.getPaginationRecords(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("分页获取聊天记录失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }


    @PostMapping("/chatTest")
    @Operation(summary = "聊天测试", description = "执行聊天测试，支持工作流调度和流式响应")
    @ResponseObjectOnly
    public SseEmitter chatTest(@RequestBody ChatTestRequest request) {
        final SseEmitter emitter = new SseEmitter(300000L); // 5分钟超时

        ThreadPoolTaskExecutor threadExecutor = SpringFactory.getBean(ThreadPoolTaskExecutor.class);

        // 异步处理聊天逻辑
        CompletableFuture.runAsync(() -> {
            chatService.processChatTest(request, emitter);
        }, threadExecutor).exceptionally(e -> {
            // 处理异常
            log.error("聊天测试执行异常: {}", e.getMessage(), e);
            WorkflowResponseWriter.sseErrRes(emitter, e);
            return null;
        });

        return emitter;
    }

    @GetMapping("/outLink/init")
    @Operation(summary = "初始化免登录聊天", description = "初始化免登录聊天会话，验证链接有效性并返回聊天配置信息")
    public ApiResponse<ChatInitResponse> initChat(
        @Parameter(description = "分享链接ID（24位随机字符串）", required = true)
        @RequestParam @NotBlank(message = "分享链接ID不能为空") String shareId,
        @Parameter(description = "外链用户ID（用于识别用户身份）", required = true)
        @RequestParam @NotBlank(message = "外链用户ID不能为空") String outLinkUid,
        @Parameter(description = "聊天ID（可选，用于恢复已有聊天）")
        @RequestParam(required = false) String chatId) {
        log.info("初始化免登录聊天，shareId：{}，outLinkUid：{}，chatId：{}", shareId, outLinkUid, chatId);
        ChatInitResponse response = outLinkService.initChat(shareId, outLinkUid, chatId);
        return ApiResponse.success(response);
    }


    @PostMapping("/getHistories")
    @Operation(
        summary = "获取聊天历史记录",
        description = "支持外链认证、团队空间认证和普通用户认证的聊天历史记录获取"
    )
    public ApiResponse<PageResult<Chat>> getHistories(
        @Parameter(description = "查询参数")
        @RequestBody GetHistoriesRequest request) {
        PageResult<Chat> response = chatService.getHistories(request);
        return ApiResponse.success(response);
    }

    @PostMapping("/updateHistory")
    @Operation(
            summary = "更新历史标题",
            description = "更新聊天历史标题"
    )
    public ApiResponse<Void> updateHistory(
            @Parameter(description = "更新参数")
            @RequestBody UpdateHistoryRequest request) {
        chatService.updateHistory(request);
        return ApiResponse.success();
    }

}
