package com.sinitek.mind.core.workflow.dispatch.emptyOperation;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Component;

import java.util.Map;

// FlowNodeTypeEnum.toolSet
@Component("toolSetDispatcher")
public class ToolSetDispatcher implements NodeDispatcher {
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        return Map.of();
    }
}
