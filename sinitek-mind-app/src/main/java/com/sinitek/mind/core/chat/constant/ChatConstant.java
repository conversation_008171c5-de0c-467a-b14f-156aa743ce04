package com.sinitek.mind.core.chat.constant;

import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;

import java.util.HashMap;
import java.util.Map;

public class ChatConstant {

    public static final Map<String, ChatRoleEnum> ROLES  = new HashMap<>();

    static {
        ROLES.put("system", ChatRoleEnum.SYSTEM);
        ROLES.put("user", ChatRoleEnum.HUMAN);
        ROLES.put("assistant", ChatRoleEnum.AI);
        ROLES.put("function", ChatRoleEnum.AI);
        ROLES.put("tool", ChatRoleEnum.AI);
    }
}
