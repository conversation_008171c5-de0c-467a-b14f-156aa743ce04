package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.service.IMCPClientService;
import io.modelcontextprotocol.client.McpAsyncClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.mcp.SyncMcpToolCallbackProvider;
import org.springframework.ai.tool.ToolCallback;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class MCPClientServiceImpl implements IMCPClientService {

    private final List<McpAsyncClient> mcpAsyncClients;

    @Autowired(required = false)
    private SyncMcpToolCallbackProvider toolCallbackProvider;

    public MCPClientServiceImpl(List<McpAsyncClient> mcpAsyncClients) {
        this.mcpAsyncClients = mcpAsyncClients;
    }


    /**
     * 获取可用工具列表
     * @return 工具列表
     */
    @Override
    public ToolCallback[] getTools() {
        try {
            if (toolCallbackProvider == null) {
                log.warn("[MCP Client] SyncMcpToolCallbackProvider is not available. MCP client may be disabled in configuration.");
                return new ToolCallback[0]; // 返回空数组而不是抛出异常
            }
            log.debug("[MCP Client] Getting available tools via ToolCallbackProvider");
            return toolCallbackProvider.getToolCallbacks();
        } catch (Exception e) {
            log.error("[MCP Client] Failed to get tools", e);
            throw new RuntimeException("Failed to get tools: " + e.getMessage(), e);
        }
    }

    /**
     * 直接使用MCP客户端获取工具列表（异步方式）
     * @return 工具列表的Mono
     */
    @Override
    public Mono<List<McpToolConfig>> getToolsAsync() {
        if (mcpAsyncClients.isEmpty()) {
            return Mono.error(new RuntimeException("No MCP clients configured"));
        }

        // 使用第一个配置的客户端
        McpAsyncClient client = mcpAsyncClients.get(0);

        return client.listTools()
                .map(response -> response.tools().stream()
                        .map(tool -> new McpToolConfig(
                                tool.name(),
                                tool.description() != null ? tool.description() : "",
                                tool.inputSchema() != null ? JSONSchemaInputType.builder()
                                        .type(tool.inputSchema().type())
                                        .properties(tool.inputSchema().properties())
                                        .required(tool.inputSchema().required())
                                        .build() : JSONSchemaInputType.builder()
                                        .properties(Map.of())
                                        .type("object")
                                        .build()
                        ))
                        .toList())
                .doOnError(error -> log.error("[MCP Client] Failed to get tools async", error));
    }
}
