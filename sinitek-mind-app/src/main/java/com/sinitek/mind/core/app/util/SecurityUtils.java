package com.sinitek.mind.core.app.util;

import com.sinitek.mind.core.app.model.SecretValueType;
import com.sinitek.mind.core.workflow.dispatch.tools.PluginRunToolDispatcher;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

/**
 * 安全工具类
 * 用于加密解密密钥等敏感信息
 */
@Slf4j
@Component
public class SecurityUtils {
    
    @Value("${app.security.secret-key:fastgpt-default-secret-key-2024}")
    private static String secretKey;
    
    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/ECB/PKCS5Padding";
    
    /**
     * 加密密钥值
     * 对应原始代码中的 storeSecretValue 功能
     */
    public String encryptSecret(String plainText) {
        try {
            if (plainText == null || plainText.isEmpty()) {
                return plainText;
            }
            
            SecretKeySpec keySpec = new SecretKeySpec(
                getKeyBytes(), ALGORITHM);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec);
            
            byte[] encryptedBytes = cipher.doFinal(
                plainText.getBytes(StandardCharsets.UTF_8));
            
            String encrypted = Base64.getEncoder().encodeToString(encryptedBytes);
            log.debug("密钥加密成功");
            return encrypted;
            
        } catch (Exception e) {
            log.error("密钥加密失败: {}", e.getMessage(), e);
            throw new RuntimeException("密钥加密失败", e);
        }
    }
    
    /**
     * 解密密钥值
     * 对应原始代码中的 getSecretValue 功能
     */
    public static String decryptSecret(String encryptedText) {
        try {
            if (encryptedText == null || encryptedText.isEmpty()) {
                return encryptedText;
            }
            
            SecretKeySpec keySpec = new SecretKeySpec(
                getKeyBytes(), ALGORITHM);
            
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            cipher.init(Cipher.DECRYPT_MODE, keySpec);
            
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedText);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);
            
            String decrypted = new String(decryptedBytes, StandardCharsets.UTF_8);
            log.debug("密钥解密成功");
            return decrypted;
            
        } catch (Exception e) {
            log.error("密钥解密失败: {}", e.getMessage(), e);
            throw new RuntimeException("密钥解密失败", e);
        }
    }
    
    /**
     * 生成密钥字节数组
     */
    private static byte[] getKeyBytes() {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] keyBytes = digest.digest(secretKey.getBytes(StandardCharsets.UTF_8));
            
            // 取前16字节作为AES密钥
            byte[] aesKey = new byte[16];
            System.arraycopy(keyBytes, 0, aesKey, 0, 16);
            
            return aesKey;
        } catch (Exception e) {
            log.error("生成密钥字节数组失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成密钥字节数组失败", e);
        }
    }
    
    /**
     * 生成随机密钥
     */
    public String generateRandomKey(int length) {
        try {
            SecureRandom random = new SecureRandom();
            byte[] bytes = new byte[length];
            random.nextBytes(bytes);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("生成随机密钥失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成随机密钥失败", e);
        }
    }
    
    /**
     * 计算字符串的哈希值
     */
    public String calculateHash(String input) {
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(input.getBytes(StandardCharsets.UTF_8));
            return Base64.getEncoder().encodeToString(hashBytes);
        } catch (Exception e) {
            log.error("计算哈希值失败: {}", e.getMessage(), e);
            throw new RuntimeException("计算哈希值失败", e);
        }
    }
    
    /**
     * 验证哈希值
     */
    public boolean verifyHash(String input, String expectedHash) {
        try {
            String actualHash = calculateHash(input);
            return actualHash.equals(expectedHash);
        } catch (Exception e) {
            log.error("验证哈希值失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 生成安全的随机字符串
     */
    public String generateSecureRandomString(int length) {
        try {
            String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
            SecureRandom random = new SecureRandom();
            StringBuilder sb = new StringBuilder();
            
            for (int i = 0; i < length; i++) {
                sb.append(chars.charAt(random.nextInt(chars.length())));
            }
            
            return sb.toString();
        } catch (Exception e) {
            log.error("生成安全随机字符串失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成安全随机字符串失败", e);
        }
    }

    /**
     * 处理存储的密钥值，转换为HTTP请求头格式
     * 对应TypeScript版本的getSecretValue函数
     *
     * @param storeSecret 存储的密钥映射
     * @return 处理后的请求头映射
     */
    public static Map<String, String> getSecretValue(Map<String, SecretValueType> storeSecret) {
        if (storeSecret == null || storeSecret.isEmpty()) {
            return new HashMap<>();
        }

        Map<String, String> result = new HashMap<>();

        for (Map.Entry<String, SecretValueType> entry : storeSecret.entrySet()) {
            String key = entry.getKey();
            SecretValueType secretValue = entry.getValue();

            if (secretValue == null) {
                continue;
            }

            // 获取实际值：优先使用明文值，否则解密密文
            String actualValue = getActualValue(secretValue);

            if (!StringUtils.hasText(actualValue)) {
                continue;
            }

            // 根据密钥类型处理Authorization头
            if (PluginRunToolDispatcher.HeaderSecretTypeEnum.BEARER.getValue().equals(key)) {
                result.put("Authorization", "Bearer " + actualValue);
            } else if (PluginRunToolDispatcher.HeaderSecretTypeEnum.BASIC.getValue().equals(key)) {
                result.put("Authorization", "Basic " + actualValue);
            } else {
                result.put(key, actualValue);
            }
        }

        return result;
    }

    /**
     * 获取密钥的实际值
     *
     * @param secretValue 密钥值对象
     * @return 解密后的实际值
     */
    private static String getActualValue(SecretValueType secretValue) {
        // 如果有明文值，直接返回
        if (StringUtils.hasText(secretValue.getValue())) {
            return secretValue.getValue();
        }

        // 如果有加密值，进行解密
        if (StringUtils.hasText(secretValue.getSecret())) {
            try {
                return SecurityUtils.decryptSecret(secretValue.getSecret());
            } catch (Exception e) {
                log.error("解密密钥失败: {}", e.getMessage(), e);
                return null;
            }
        }

        return null;
    }
}