package com.sinitek.mind.core.dataset.model;

import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import lombok.Data;

import java.util.List;

// 内部类定义搜索响应
@Data
public class SearchDatasetDataResponse {
    // Getters and Setters
    private List<SearchDataResponseItemType> searchRes;
    private Integer embeddingTokens;
    private Integer reRankInputTokens;
    private String searchMode;
    private Integer limit;
    private Double similarity;
    private Boolean usingReRank;
    private Boolean usingSimilarityFilter;

    private QueryExtensionResult queryExtensionResult;

    private DispatchNodeResponseType.DeepSearchResult deepSearchResult; // 没有query字段

}