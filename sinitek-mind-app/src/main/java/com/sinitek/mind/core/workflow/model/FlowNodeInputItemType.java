package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowNodeInputItemType extends InputComponentPropsType {

    private Integer selectedTypeIndex;

    private List<String> renderTypeList;

    private String key;

    private String valueType;

    private String valueDesc;

    private Object value;

    private String label;

    private String debugLabel;

    private String description;

    private Boolean required;

    private String enums;

    private String toolDescription;

    private Boolean canEdit;

    private Boolean pro;

    private Boolean toolOutput;

    private Boolean canSelectFile;

    private Boolean canSelectImg;

    private Integer maxFiles;

    private Boolean deprecated;
}
