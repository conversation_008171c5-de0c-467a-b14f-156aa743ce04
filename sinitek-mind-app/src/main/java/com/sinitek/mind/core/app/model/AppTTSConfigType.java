package com.sinitek.mind.core.app.model;

import lombok.Data;

@Data
public class AppTTSConfigType {
    public enum Type {
        none("none"),
        web("web"),
        model("model");

        private final String value;
        Type(String value) { this.value = value; }
        public String getValue() { return value; }
    }

    private Type type;
    private String model;
    private String voice;
    private Double speed; // 可为 null
}