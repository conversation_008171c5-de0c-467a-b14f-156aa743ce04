package com.sinitek.mind.core.workflow.dispatch.plugin;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component("pluginOutputDispatcher")
public class PluginOutputDispatcher implements NodeDispatcher {
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        try {
            return dispatchPluginOutput(dispatchData);
        } catch (Exception e) {
            log.error("Plugin Output dispatch failed: {}", e.getMessage(), e);
            throw new RuntimeException("Plugin Output dispatch failed: " + e.getMessage(), e);
        }
    }

    private Map<String, Object> dispatchPluginOutput(ModuleDispatchProps props) {
        Map<String, Object> params = props.getParams();

        // 构建返回结果
        Map<String, Object> result = new HashMap<>(params);
        // 添加节点响应
        Map<String, Object> nodeResponse = new HashMap<>();
        nodeResponse.put("totalPoints", 0);
        nodeResponse.put("pluginOutput", params);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);
        return result;
    }
}
