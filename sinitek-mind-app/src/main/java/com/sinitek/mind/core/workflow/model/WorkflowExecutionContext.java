package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowExecutionContext {
    private ChatDispatchProps props;
    private List<ChatHistoryItemResType> chatResponses;
    private List<AIChatItemValueItemType> chatAssistantResponse;
    private List<ChatNodeUsageType> chatNodeUsages;
    private Object toolRunResponse;
    private List<RuntimeNodeItemType> debugNextStepRunNodes;
    private NodeInteractiveResponse nodeInteractiveResponse;
    private Map<String, Object> systemMemories;
    private List<RuntimeNodeItemType> runtimeNodes;
    private List<RuntimeEdgeItemType> runtimeEdges;
    private List<ChatItemType> histories;
    private Map<String, Object> variables;
    private boolean isRootRuntime;
    private Integer workflowRunTimes;
    private List<ChatItemValueItemType> query;
}