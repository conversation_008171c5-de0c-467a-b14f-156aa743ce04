package com.sinitek.mind.core.workflow.model.sse;

import com.sinitek.mind.core.workflow.model.Choice;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * Gpt返回结果
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class GptResponse implements SseResponseType {
    private Map<String, Object> extraData;
    private String id;
    private String object;
    private Long created;
    private String model;
    private List<Choice> choices;
}