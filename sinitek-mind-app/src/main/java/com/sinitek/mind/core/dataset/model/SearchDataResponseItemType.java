package com.sinitek.mind.core.dataset.model;

import com.sinitek.mind.core.dataset.enumerate.SearchScoreTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;
import java.util.List;

/**
 * 搜索数据响应项类型
 * 对应 TypeScript 中的 SearchDataResponseItemType
 * 继承 DatasetDataItemType 但排除 teamId、indexes、isOwner 字段
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class SearchDataResponseItemType {
    
    // 从 DatasetDataItemType 继承的字段（排除 teamId、indexes、isOwner）
    private String id;
    private String datasetId;
    private String imagePreviewUrl;
    private Date updateTime;
    private String collectionId;
    private String sourceName;
    private String sourceId;
    private Integer chunkIndex;
    
    // 从 DatasetDataFieldType 继承的字段
    private String q;  // 问题或大块内容
    private String a;  // 答案或自定义内容
    private String imageId;
    
    // SearchDataResponseItemType 特有的字段
    private List<SearchScore> score;
    
    /**
     * 搜索评分内部类
     */
    @Data
    public static class SearchScore {
        private SearchScoreTypeEnum type;
        private Double value;
        private Integer index;
    }

}