package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class SaveChatDTO {

    private String chatId;

    private String appId;

    private String teamId;

    private String tmbId;

    private List<StoreNodeItemType> nodes;

    private AppChatConfigType appChatConfig;

    private Map<String, Object> variables;

    private boolean isUpdateUseTime;

    private String newTitle;

    private String source;

    private String sourceName;

    private String shareId;

    private String outLinkUid;

    private List<ChatItemType> content;

    private Map<String, Object> metadata;

    private double durationSeconds;

    private String errorMsg;

}
