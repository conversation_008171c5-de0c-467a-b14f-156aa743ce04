package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "聊天测试响应")
public class ChatTestResponse {

    @Schema(description = "流程响应")
    private List<ChatHistoryItemResType> flowResponses;

    @Schema(description = "助手响应")
    private List<ChatItemValueItemType> assistantResponses;

    @Schema(description = "系统记忆")
    private Map<String, Object> systemMemories;

    @Schema(description = "新变量")
    private Map<String, Object> newVariables;

    @Schema(description = "流程使用情况")
    private List<Object> flowUsages;

    @Schema(description = "持续时间（秒）")
    private double durationSeconds;

    @Schema(description = "聊天ID")
    private String chatId;

    @Schema(description = "响应聊天项ID")
    private String responseChatItemId;

    @Schema(description = "是否成功")
    private boolean success;

    @Schema(description = "错误信息")
    private String errorMessage;
}