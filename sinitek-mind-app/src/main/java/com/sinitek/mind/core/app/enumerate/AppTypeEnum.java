package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum AppTypeEnum {

    FOLDER("folder", "文件夹"),
    SIMPLE("simple", "简单应用"),
    WORKFLOW("advanced", "工作流"),
    PLUGIN("plugin", "插件"),
    HTTP_PLUGIN("httpPlugin", "HTTP插件"),
    TOOL_SET("toolSet", "工具集"),
    TOOL("tool", "工具"),
    MCP_TOOLS("mcpTools", "MCP工具");

    private final String value;
    private final String description;

    AppTypeEnum(String value, String description) {
        this.value = value;
        this.description = description;
    }

    public static AppTypeEnum fromValue(String value) {
        for (AppTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown AppTypeEnum value: " + value);
    }
}
