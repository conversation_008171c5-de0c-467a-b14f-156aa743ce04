package com.sinitek.mind.core.ai.util;

import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

public class PromptUtil {

    /**
     * 获取文档引用提示词
     * 对应 TypeScript 中的 getDocumentQuotePrompt 函数
     *
     * @param version 版本号
     * @return 文档引用提示词模板
     */
    public static String getDocumentQuotePrompt(String version) {
        Map<String, String> promptMap = new HashMap<>();
        promptMap.put("4.9.7",
                "将 <FilesContent></FilesContent> 中的内容作为本次对话的参考:\n" +
                        "<FilesContent>\n" +
                        "{{quote}}\n" +
                        "</FilesContent>\n");

        return getPromptByVersion(version, promptMap);
    }

    /**
     * 根据版本号获取对应的提示词
     * 对应 TypeScript 中的 getPromptByVersion 函数
     *
     * @param version 版本号
     * @param promptMap 版本号到提示词的映射
     * @return 对应版本的提示词，如果未找到则返回最新版本的提示词
     */
    public static String getPromptByVersion(String version, Map<String, String> promptMap) {
        if (promptMap == null || promptMap.isEmpty()) {
            return null;
        }

        // 版本号大的在前面 - 按语义化版本排序
        List<String> versions = promptMap.keySet().stream()
                .sorted((a, b) -> {
                    String[] partsA = a.split("\\.");
                    String[] partsB = b.split("\\.");

                    int majorA = partsA.length > 0 ? Integer.parseInt(partsA[0]) : 0;
                    int minorA = partsA.length > 1 ? Integer.parseInt(partsA[1]) : 0;
                    int patchA = partsA.length > 2 ? Integer.parseInt(partsA[2]) : 0;

                    int majorB = partsB.length > 0 ? Integer.parseInt(partsB[0]) : 0;
                    int minorB = partsB.length > 1 ? Integer.parseInt(partsB[1]) : 0;
                    int patchB = partsB.length > 2 ? Integer.parseInt(partsB[2]) : 0;

                    if (majorA != majorB) return majorB - majorA;
                    if (minorA != minorB) return minorB - minorA;
                    return patchB - patchA;
                })
                .toList();

        // 如果没有指定版本，返回最新版本（排序后的第一个）
        if (!StringUtils.hasText(version)) {
            return promptMap.get(versions.get(0));
        }

        // 如果指定版本存在，返回对应的提示词
        if (promptMap.containsKey(version)) {
            return promptMap.get(version);
        }

        // 如果指定版本不存在，返回最新版本的提示词
        return promptMap.get(versions.get(0));
    }

    /**
     * 获取数据集搜索工具响应提示词
     * 对应TypeScript中的getDatasetSearchToolResponsePrompt函数
     *
     * @return 数据集搜索工具响应提示词
     */
    public static String getDatasetSearchToolResponsePrompt() {
        return "## Role\n" +
                "你是一个知识库回答助手，可以 \"cites\" 中的内容作为本次对话的参考。为了使回答结果更加可信并且可追溯，你需要在每段话结尾添加引用标记，标识参考了哪些内容。\n\n" +
                "## 追溯展示规则\n\n" +
                "- 使用 **[id](CITE)** 格式来引用 \"cites\" 中的知识，其中 CITE 是固定常量, id 为引文中的 id。\n" +
                "- 在 **每段话结尾** 自然地整合引用。例如: \"Nginx是一款轻量级的Web服务器、反向代理服务器[67e517e74767063e882d6861](CITE)。\"。\n" +
                "- 每段话**至少包含一个引用**，多个引用时按顺序排列，例如：\"Nginx是一款轻量级的Web服务器、反向代理服务器[67e517e74767063e882d6861](CITE)[67e517e74767063e882d6862](CITE)。\\n 它的特点是非常轻量[67e517e74767063e882d6863](CITE)。\"\n" +
                "- 不要把示例作为知识点。\n" +
                "- 不要伪造 id，返回的 id 必须都存在 cites 中！\n\n" +
                "## 通用规则\n" +
                "- 如果你不清楚答案，你需要澄清。\n" +
                "- 避免提及你是从 \"cites\" 获取的知识。\n" +
                "- 保持答案与 \"cites\" 中描述的一致。\n" +
                "- 使用 Markdown 语法优化回答格式。尤其是图片、表格、序列号等内容，需严格完整输出。\n" +
                "- 使用与问题相同的语言回答。";
    }

    /**
     * 获取分类问题的系统提示
     * 对应TypeScript中的getCQSystemPrompt方法
     */
    public static String getCQSystemPrompt(String systemPrompt, String memory, String typeList) {
        List<String> list = new ArrayList<>();
        if (StringUtils.hasText(systemPrompt)) {
            list.add("【背景知识】");
        }
        list.add("【历史记录】");
        if (StringUtils.hasText(memory)) {
            list.add("【上一轮分类结果】");
        }

        StringBuilder prompt = new StringBuilder();
        prompt.append("## 角色\n");
        prompt.append("你是一个\"分类助手\"，可以结合").append(String.join("、", list)).append("，来判断用户当前问题属于哪一个分类，并输出分类标记。\n\n");

        if (StringUtils.hasText(systemPrompt)) {
            prompt.append("## 背景知识\n");
            prompt.append(systemPrompt).append("\n\n");
        }

        if (StringUtils.hasText(memory)) {
            prompt.append("## 上一轮分类结果\n");
            prompt.append(memory).append("\n\n");
        }

        prompt.append("## 分类清单\n\n");
        prompt.append(typeList).append("\n\n");

        prompt.append("## 分类要求\n\n");
        prompt.append("1. 分类结果必须从分类清单中选择。\n");
        prompt.append("2. 连续对话时，如果分类不明确，且用户未变更话题，则保持上一轮分类结果不变。\n");
        prompt.append("3. 存在分类冲突或模糊分类时， 主语指向的分类优先级更高。\n\n");

        prompt.append("## 输出格式\n\n");
        prompt.append("只需要输出分类的 id 即可，无需输出额外内容。");

        return prompt.toString().replaceAll("\\n{3,}", "\\n\\n");
    }

    /**
     * 生成JSON工具提取的提示模板
     * @param systemPrompt 系统提示（可选）
     * @param memory 历史记忆（可选）
     * @return 格式化的提示字符串
     */
    public static String getExtractJsonToolPrompt(String systemPrompt, String memory) {
        // 构建动态列表
        List<String> list = new ArrayList<>();
        list.add("【历史记录】");
        list.add("【用户输入】");

        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            list.add("【背景知识】");
        }

        if (memory != null && !memory.trim().isEmpty()) {
            list.add("【历史提取结果】");
        }

        // 使用Stream API连接列表元素
        String listJoined = String.join("、", list);

        // 构建基础提示模板
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("## 背景\n")
                .append("用户需要执行一个叫 \"request_function\" 的函数，该函数需要你结合")
                .append(listJoined)
                .append("，来生成对应的参数\n\n")
                .append("## 基本要求\n\n")
                .append("- 不是每个参数都是必须生成的，如果没有合适的参数值，不要生成该参数，或返回空字符串。\n")
                .append("- 需要结合历史记录，一起生成合适的参数。最新的记录优先级更高。\n")
                .append("- 即使无法调用函数，也要返回一个 JSON 字符串，而不是回答问题。\n\n");

        // 添加特定要求部分（如果有systemPrompt）
        if (systemPrompt != null && !systemPrompt.trim().isEmpty()) {
            promptBuilder.append("## 特定要求\n")
                    .append(systemPrompt)
                    .append("\n\n");
        }

        // 添加历史提取结果部分（如果有memory）
        if (memory != null && !memory.trim().isEmpty()) {
            promptBuilder.append("## 历史提取结果\n")
                    .append(memory)
                    .append("\n\n");
        }

        // 处理多余的换行符（相当于TypeScript中的replace(/\n{3,}/g, '\n\n')）
        String prompt = promptBuilder.toString();
        return replaceMultipleNewlines(prompt);
    }

    /**
     * 替换连续的换行符为双换行符
     * 相当于JavaScript的 replace(/\n{3,}/g, '\n\n')
     */
    private static String replaceMultipleNewlines(String text) {
        Pattern pattern = Pattern.compile("\n{3,}");
        return pattern.matcher(text).replaceAll("\n\n");
    }
}
