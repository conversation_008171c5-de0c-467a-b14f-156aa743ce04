package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@EqualsAndHashCode(callSuper=true)
@JsonIgnoreProperties(ignoreUnknown = true)
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class SystemPluginTemplateItemType extends WorkflowTemplateType {

    public String customWorkflow;
    public String associatedPluginId;
    public String userGuide;
    public String templateType;
    public Boolean isTool;
    public Integer originCost;
    public Integer currentCost;
    public Boolean hasTokenFee;
    public Integer pluginOrder;
    public Boolean isActive;
    public Boolean isOfficial;
    public List<InputConfig> inputConfig;
}
