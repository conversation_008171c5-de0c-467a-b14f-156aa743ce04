package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

/**
 * 工作流响应配置
 */
@Data
@Builder
public class WorkflowResponseConfig {
    private SseEmitter sseEmitter;
    private boolean detail;
    private boolean streamResponse;
    @Builder.Default
    private String id = WorkflowUtil.getNanoid(24);
    @Builder.Default
    private boolean showNodeStatus = true;
}