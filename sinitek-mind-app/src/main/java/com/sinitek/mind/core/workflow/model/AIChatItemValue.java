package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * AI聊天项值
 */
@Data
@Builder
public class AIChatItemValue {
    
    /**
     * 聊天项值类型
     */
    private ChatItemValueType type;
    
    /**
     * 文本内容
     */
    private TextContent text;
    
    /**
     * 推理内容
     */
    private ReasoningContent reasoning;
    
    /**
     * 交互内容
     */
    private Object interactive;
    
    /**
     * 工具调用
     */
    private List<ToolCallContent> toolCalls;
    
    /**
     * 文件内容
     */
    private List<FileContent> files;
    
    /**
     * 图片内容
     */
    private List<ImageContent> images;
    
    /**
     * 音频内容
     */
    private List<AudioContent> audios;
    
    /**
     * 视频内容
     */
    private List<VideoContent> videos;
}