package com.sinitek.mind.core.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowTemplateType {

    public String id;

    public String parentId;

    public Boolean isFolder;

    public String name;

    public String avatar;

    public String intro;

    public String author;

    public String courseUrl;

    public String version;

    public String versionLabel;

    public Boolean isLatestVersion;

    public Boolean showStatus;

    public Integer weight;

    public WorkflowTemplateBasicType workflow;
}
