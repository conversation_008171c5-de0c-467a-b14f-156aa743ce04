package com.sinitek.mind.core.workflow.enumerate;

import lombok.Getter;

/**
 * 节点调度响应键枚举
 * 对应 TypeScript 中的 DispatchNodeResponseKeyEnum
 */
@Getter
public enum DispatchNodeResponseKeyEnum {
    SKIP_HANDLE_ID("skipHandleId"), // skip handle id
    NODE_RESPONSE("responseData"), // run node response
    NODE_DISPATCH_USAGES("nodeDispatchUsages"), // the node bill.
    CHILDREN_RESPONSES("childrenResponses"), // Some nodes make recursive calls that need to be returned
    TOOL_RESPONSES("toolResponses"), // The result is passed back to the tool node for use
    ASSISTANT_RESPONSES("assistantResponses"), // assistant response
    REWRITE_HISTORIES("rewriteHistories"), // If have the response, workflow histories will be rewrite
    INTERACTIVE("INTERACTIVE"), // is interactive
    RUN_TIMES("runTimes"), // run times
    NEW_VARIABLES("newVariables"), // new variables
    MEMORIES("system_memories"); // memories

    private final String value;

    DispatchNodeResponseKeyEnum(String value) {
        this.value = value;
    }

    /**
     * 根据字符串值获取对应的枚举
     * @param value 字符串值
     * @return 对应的枚举，如果未找到则返回 null
     */
    public static DispatchNodeResponseKeyEnum fromValue(String value) {
        for (DispatchNodeResponseKeyEnum keyEnum : values()) {
            if (keyEnum.value.equals(value)) {
                return keyEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return value;
    }
}