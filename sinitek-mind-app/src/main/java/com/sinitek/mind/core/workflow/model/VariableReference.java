package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 变量引用辅助模型
 * 用于封装变量引用信息，便于处理变量引用相关操作
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class VariableReference {
    
    /**
     * 节点ID
     * 标识变量所属的节点，"VARIABLE_NODE_ID" 表示全局变量
     */
    private String nodeId;
    
    /**
     * 变量键名
     * 变量在节点中的唯一标识符
     */
    private String variableKey;
    
    /**
     * 是否为全局变量
     * true: 全局变量，false: 节点输出变量
     */
    private boolean isGlobalVariable;
}