package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.model.*;
import com.sinitek.mind.core.chat.model.ChatInputGuideConfigType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class GuideModuleConfig {

    private String welcomeText;

    private List<VariableItemType> variables;

    private AppQGConfigType questionGuide;

    private AppTTSConfigType ttsConfig;

    private AppWhisperConfigType whisperConfig;

    private AppScheduledTriggerConfigType scheduledTriggerConfig;

    private ChatInputGuideConfigType chatInputGuide;

    private String instruction;

    private AppAutoExecuteConfigType autoExecute;
}
