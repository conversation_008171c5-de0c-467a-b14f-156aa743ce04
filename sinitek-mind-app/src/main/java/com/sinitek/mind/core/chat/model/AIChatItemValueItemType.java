package com.sinitek.mind.core.chat.model;

import com.sinitek.mind.core.workflow.model.ReasoningContent;
import com.sinitek.mind.core.workflow.model.TextContent;
import com.sinitek.mind.core.workflow.model.WorkflowInteractiveResponseType;
import lombok.Data;

import java.util.List;

@Data
public class AIChatItemValueItemType {

    private String type; // "text"、"reasoning"、"tool"、"interactive"

    private TextContent text;

    private ReasoningContent reasoning;

    private List<ToolModuleResponseItemType> tools;

    private WorkflowInteractiveResponseType interactive;
}
