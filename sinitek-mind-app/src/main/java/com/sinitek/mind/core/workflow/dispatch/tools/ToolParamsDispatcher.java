package com.sinitek.mind.core.workflow.dispatch.tools;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 工具参数调度器
 * 对应 TypeScript 中的 dispatchToolParams 函数
 * 用于处理工具参数的调度逻辑
 */
@Component("toolParamsDispatcher")
public class ToolParamsDispatcher implements NodeDispatcher {
    
    /**
     * 调度工具参数
     * 
     * @param dispatchData 模块调度属性
     * @return 调度结果，包含原始参数和节点响应
     */
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();
        
        // 创建返回结果
        Map<String, Object> result = new HashMap<>(params);
        
        // 添加节点响应
        Map<String, Object> nodeResponse = new HashMap<>();
        nodeResponse.put("toolParamsResult", params);
        
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);
        
        return result;
    }
}