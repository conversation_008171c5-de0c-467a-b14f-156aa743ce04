package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.chat.dto.GetChatLogsRequest;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class ExportChatLogsBody extends GetChatLogsRequest {

    /**
     * 导出文件标题
     */
    private String title;

    /**
     * 来源映射
     */
    private Map<String, SourceInfo> sourcesMap;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SourceInfo {
        public String label;
    }

}
