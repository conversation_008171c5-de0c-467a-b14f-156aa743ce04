package com.sinitek.mind.core.chat.repository;

import com.sinitek.mind.core.chat.entity.Chat;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

@Repository
public interface ChatRepository extends MongoRepository<Chat, String> {

    /**
     * 根据应用ID和聊天ID查找聊天记录
     */
    Optional<Chat> findByAppIdAndChatId(String appId, String chatId);

    /**
     * 根据聊天ID查找聊天记录
     */
    Optional<Chat> findByChatId(String chatId);

    /**
     * 根据appID删除聊天记录
     */
    void deleteByAppId(String id);

    /**
     * 根据条件查询聊天记录数量
     * @param teamId 团队ID
     * @param appId 应用ID
     * @param dateStart 开始时间
     * @param dateEnd 结束时间
     * @return 记录数量
     */
    @Query(value = "{'teamId': ?0, 'appId': ?1, 'updateTime': {'$gte': ?2, '$lte': ?3}}", count = true)
    long countByConditions(String teamId, String appId, Date dateStart, Date dateEnd);

    /**
     * 根据条件和来源查询聊天记录数量
     * @param teamId 团队ID
     * @param appId 应用ID
     * @param dateStart 开始时间
     * @param dateEnd 结束时间
     * @param sources 来源列表
     * @return 记录数量
     */
    @Query(value = "{'teamId': ?0, 'appId': ?1, 'updateTime': {'$gte': ?2, '$lte': ?3}, 'source': {'$in': ?4}}", count = true)
    long countByConditionsWithSources(String teamId, String appId, Date dateStart, Date dateEnd, List<String> sources);

    /**
     * 根据条件和标题查询聊天记录数量
     * @param teamId 团队ID
     * @param appId 应用ID
     * @param dateStart 开始时间
     * @param dateEnd 结束时间
     * @param titleRegex 标题正则表达式
     * @return 记录数量
     */
    @Query(value = "{'teamId': ?0, 'appId': ?1, 'updateTime': {'$gte': ?2, '$lte': ?3}, '$or': [{'title': {'$regex': ?4, '$options': 'i'}}, {'customTitle': {'$regex': ?4, '$options': 'i'}}]}", count = true)
    long countByConditionsWithTitle(String teamId, String appId, Date dateStart, Date dateEnd, String titleRegex);

    /**
     * 根据条件、来源和标题查询聊天记录数量
     * @param teamId 团队ID
     * @param appId 应用ID
     * @param dateStart 开始时间
     * @param dateEnd 结束时间
     * @param sources 来源列表
     * @param titleRegex 标题正则表达式
     * @return 记录数量
     */
    @Query(value = "{'teamId': ?0, 'appId': ?1, 'updateTime': {'$gte': ?2, '$lte': ?3}, 'source': {'$in': ?4}, '$or': [{'title': {'$regex': ?5, '$options': 'i'}}, {'customTitle': {'$regex': ?5, '$options': 'i'}}]}", count = true)
    long countByConditionsWithSourcesAndTitle(String teamId, String appId, Date dateStart, Date dateEnd, List<String> sources, String titleRegex);

    /**
     * 根据shareId、outLinkUid和时间范围分页查询聊天历史记录
     * @param shareId 分享链接ID
     * @param outLinkUid 外链用户ID
     * @param startTime 开始时间
     * @param pageable 分页参数
     * @return 分页聊天记录
     */
    Page<Chat> findByShareIdAndOutLinkUidAndUpdateTimeAfterOrderByUpdateTimeDesc(String shareId, String outLinkUid, Date startTime, Pageable pageable);

}
