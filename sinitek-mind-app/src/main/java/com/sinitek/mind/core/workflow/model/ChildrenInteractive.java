package com.sinitek.mind.core.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class ChildrenInteractive extends InteractiveNodeType {
    private String type = "childrenInteractive";
    private Params params;

    @Data
    @SuperBuilder
    public static class Params {
        private WorkflowInteractiveResponseType childrenResponse;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public Params getParam() {
        return params;
    }
}