package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class V2ChatCompletionRequest {

    // FastGptWebChatProps 相关属性
    String chatId;
    String appId;
    String customUid;
    Map<String, Object> metadata;

    // OutLinkChatAuthProps 相关属性
    String shareId;
    String outLinkUid;
    String teamId;
    String teamToken;

    List<ChatCompletionMessageParam> messages;
    String responseChatItemId;
    Boolean stream = false;
    Boolean detail = false;
    Boolean retainDatasetCite = false;
    Map<String, Object> variables;

    // ChatCompletionCreateParams 相关属性
    String model;
    Double temperature;
    Double top_p;
    Integer n;
    Object stop; // 可能是 String 或 List<String>
    Integer max_tokens;
    Double presence_penalty;
    Double frequency_penalty;
    Map<String, Double> logit_bias;
    String user;
    List<Function> functions;
    Object function_call; // 可能是 String 或 FunctionCallObject

    static class Function {
        String name;
        String description;
        Map<String, Object> parameters;
    }

    static class FunctionCallObject {
        String name;
    }
}