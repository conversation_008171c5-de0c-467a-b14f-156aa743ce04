package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.core.app.dto.AppTemplateListDTO;
import com.sinitek.mind.core.app.dto.AppTemplateResDTO;
import com.sinitek.mind.core.app.entity.AppTemplate;
import com.sinitek.mind.core.app.model.UserGuide;
import com.sinitek.mind.core.app.repository.AppTemplateRepository;
import com.sinitek.mind.core.app.service.IAppTemplateService;
import com.sinitek.mind.core.app.service.IFileTemplateService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AppTemplateServiceImpl implements IAppTemplateService {

    private final AppTemplateRepository appTemplateRepository;

    private final IFileTemplateService fileTemplateService;

    @Override
    public List<AppTemplateResDTO> getTemplateList(AppTemplateListDTO listRequest) {

        // 获取所有模板数据（对应getAppTemplatesAndLoadThem函数）
        List<AppTemplate> templateMarketItems = getAppTemplatesAndLoadThem();

        // 过滤逻辑：只返回激活的模板
        List<AppTemplate> filteredItems = templateMarketItems.stream()
                .filter(item -> item.getIsActive() != null && item.getIsActive())
                .collect(Collectors.toList());

        // 根据类型过滤
        if (!"all".equals(listRequest.getType())) {
            filteredItems = filteredItems.stream()
                    .filter(item -> listRequest.getType().equals(item.getType()))
                    .collect(Collectors.toList());
        }

        // 快速模板过滤逻辑
        if (listRequest.getIsQuickTemplate()) {
            // 检查是否有模板定义了isQuickTemplate字段
            boolean hasQuickTemplateField = filteredItems.stream()
                    .anyMatch(item -> item.getIsQuickTemplate() != null);

            if (hasQuickTemplateField) {
                // 如果有定义isQuickTemplate字段，则过滤出快速模板
                filteredItems = filteredItems.stream()
                        .filter(item -> item.getIsQuickTemplate() != null && item.getIsQuickTemplate())
                        .collect(Collectors.toList());
            } else {
                // 如果没有定义isQuickTemplate字段，则取前3个
                filteredItems = filteredItems.stream()
                        .limit(3)
                        .collect(Collectors.toList());
            }
        }

        // 转换为响应对象
        return filteredItems.stream()
                .map(this::convertToResponse)
                .collect(Collectors.toList());
    }

    @Override
    public AppTemplateResDTO getTemplateDetail(String templateId) {
        try {
            if (StringUtils.isBlank(templateId)) {
                throw new IllegalArgumentException("模板ID不能为空");
            }

            // 获取所有模板数据
            List<AppTemplate> allTemplates = getAppTemplatesAndLoadThem();

            // 查找指定ID的模板
            Optional<AppTemplate> templateOpt = allTemplates.stream()
                    .filter(template -> Objects.equals(template.getTemplateId(), templateId))
                    .findFirst();

            if (templateOpt.isPresent()) {
                return convertToResponse(templateOpt.get());
            } else {
                log.warn("未找到模板: {}", templateId);
                return null;
            }

        } catch (Exception e) {
            log.error("获取模板详情失败, templateId: {}", templateId, e);
            throw new RuntimeException("获取模板详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取应用模板并加载它们
     * 对应原始的getAppTemplatesAndLoadThem函数
     *
     * @return 应用模板列表
     */
    public List<AppTemplate> getAppTemplatesAndLoadThem() {
        try {
            // 获取社区模板（从文件系统加载）
            List<AppTemplate> communityTemplates = fileTemplateService.getFileTemplates();

            // 获取数据库中的模板配置
            List<AppTemplate> dbTemplates = appTemplateRepository.findAll();

            // 合并社区模板和数据库配置
            List<AppTemplate> mergedTemplates = mergeCommunityAndDbTemplates(communityTemplates, dbTemplates);

            // 添加纯数据库模板（不在社区模板中的）
            List<AppTemplate> pureDbTemplates = dbTemplates.stream()
                    .filter(dbTemplate -> communityTemplates.stream()
                            .noneMatch(communityTemplate ->
                                    communityTemplate.getTemplateId().equals(dbTemplate.getTemplateId())))
                    .collect(Collectors.toList());

            mergedTemplates.addAll(pureDbTemplates);

            return mergedTemplates;

        } catch (Exception e) {
            // 如果出错，返回空列表
            return List.of();
        }
    }

    /**
     * 合并社区模板和数据库模板配置
     *
     * @param communityTemplates 社区模板列表
     * @param dbTemplates 数据库模板列表
     * @return 合并后的模板列表
     */
    private List<AppTemplate> mergeCommunityAndDbTemplates(
            List<AppTemplate> communityTemplates,
            List<AppTemplate> dbTemplates) {

        return communityTemplates.stream()
                .map(template -> {
                    // 查找对应的数据库配置
                    Optional<AppTemplate> dbConfig = dbTemplates.stream()
                            .filter(t -> t.getTemplateId().equals(template.getTemplateId()))
                            .findFirst();

                    if (dbConfig.isPresent()) {
                        AppTemplate config = dbConfig.get();
                        // 用数据库配置覆盖社区模板的相应字段
                        template.setIsActive(config.getIsActive() != null ? config.getIsActive() : template.getIsActive());
                        template.setTags(config.getTags() != null ? config.getTags() : template.getTags());
                        template.setUserGuide(config.getUserGuide() != null ? config.getUserGuide() : template.getUserGuide());
                        template.setIsQuickTemplate(config.getIsQuickTemplate() != null ? config.getIsQuickTemplate() : template.getIsQuickTemplate());
                        template.setOrder(config.getOrder() != null ? config.getOrder() : template.getOrder());
                    }

                    return template;
                })
                .collect(Collectors.toList());
    }

    /**
     * 将实体对象转换为响应对象
     * 对应原始代码中的map函数逻辑
     *
     * @param template 模板实体
     * @return 响应对象
     */
    private AppTemplateResDTO convertToResponse(AppTemplate template) {
        AppTemplateResDTO response = new AppTemplateResDTO();
        response.setTemplateId(template.getTemplateId());
        response.setName(template.getName());
        response.setIntro(template.getIntro());
        response.setAvatar(template.getAvatar());
        response.setTags(template.getTags());
        response.setType(template.getType());
        response.setAuthor(template.getAuthor());

        // 转换UserGuide
        if (template.getUserGuide() != null) {
            UserGuide userGuide = new UserGuide();
            userGuide.setType(template.getUserGuide().getType());
            userGuide.setContent(template.getUserGuide().getContent());
            userGuide.setLink(template.getUserGuide().getLink());
            response.setUserGuide(userGuide);
        }

        // 设置空的workflow对象
        response.setWorkflow(template.getWorkflow());

        return response;
    }
}
