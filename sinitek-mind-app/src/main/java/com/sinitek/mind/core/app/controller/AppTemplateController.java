package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.dto.AppTemplateListDTO;
import com.sinitek.mind.core.app.dto.AppTemplateResDTO;
import com.sinitek.mind.core.app.service.IAppTemplateService;
import com.sinitek.mind.support.permission.service.IAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/template")
@RequiredArgsConstructor
@Tag(name = "模板管理", description = "模板相关接口")
public class AppTemplateController {

    private final IAppTemplateService appTemplateService;

    private final IAuthService authService;

    /**
     * 获取应用模板列表
     *
     * @param isQuickTemplate 是否为快速模板
     * @param type 应用类型
     * @return 应用模板列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取模板列表")
    public ApiResponse<List<AppTemplateResDTO>> getTemplateList(
            @RequestParam(value = "isQuickTemplate", defaultValue = "false") Boolean isQuickTemplate,
            @RequestParam(value = "type", defaultValue = "all") String type) {
        try {
            // 认证检查
            authService.authCert();

            // 创建请求参数对象
            AppTemplateListDTO listRequest = new AppTemplateListDTO();
            listRequest.setIsQuickTemplate(isQuickTemplate);
            listRequest.setType(type);

            // 获取模板列表
            List<AppTemplateResDTO> templates = appTemplateService.getTemplateList(listRequest);

            return ApiResponse.success(templates);

        } catch (Exception e) {
            throw new RuntimeException("获取模板列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取单个应用模板详情
     * 对应原始的 /api/core/app/template/detail 接口
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    @GetMapping("/detail")
    @Operation(summary = "获取单个应用模板详情")
    public ApiResponse<AppTemplateResDTO> getTemplateDetail(@RequestParam("templateId") String templateId) {
        try {
            // 认证检查
            authService.authCert();

            // 获取模板详情
            AppTemplateResDTO template = appTemplateService.getTemplateDetail(templateId);

            if (template == null) {
                throw new RuntimeException("模板不存在: " + templateId);
            }

            return ApiResponse.success(template);

        } catch (Exception e) {
            throw new RuntimeException("获取模板详情失败: " + e.getMessage(), e);
        }
    }

}
