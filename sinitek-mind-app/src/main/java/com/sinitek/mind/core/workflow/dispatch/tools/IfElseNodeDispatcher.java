package com.sinitek.mind.core.workflow.dispatch.tools;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.*;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;

// IfElseNodeDispatcher 类实现 NodeDispatcher 接口，用于处理 ifElse 节点逻辑
@Component("ifElseNodeDispatcher")
public class IfElseNodeDispatcher implements NodeDispatcher {
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {

        Map<String, Object> variables = dispatchData.getVariables();
        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        String nodeId = dispatchData.getNode().getNodeId();

        List<IfElseListItemType> ifElseList = (List<IfElseListItemType>) dispatchData.getParams().get(
            NodeInputKeyEnum.IF_ELSE_LIST);

        String res = IfElseResultEnum.ELSE.getName();
        for (int i = 0; i < ifElseList.size(); i++) {
            IfElseListItemType ifElseItem = ifElseList.get(i);
            IfElseConditionType condition = ifElseItem.getCondition();
            List<ConditionListItemType> list = ifElseItem.getList();

            boolean result = getResult(condition, list, variables, runtimeNodes);
            if (result) {
                res = getElseIFLabel(i);
                break;
            }
        }

        int length = ifElseList.size() + 1;
        List<String> skipHandleId = new ArrayList<>();
        for (int index = 0; index < length; index++) {
            String label;
            if (index < ifElseList.size()) {
                label = getElseIFLabel(index);
            } else {
                label = IfElseResultEnum.ELSE.getName();
            }
            if (Objects.equals(label, res)) {
                // 过滤掉结果节点
                continue;
            }
            skipHandleId.add(WorkflowUtil.getHandleId(nodeId, "source", label));
        }

        // 构建返回结果
        Map<String, Object> result = new HashMap<>();

        // ifelse结果
        result.put(NodeOutputKeyEnum.IF_ELSE_RESULT.getValue(), res);

        // 设置节点响应（对应 TypeScript 中的 DispatchNodeResponseKeyEnum.nodeResponse）
        DispatchNodeResponseType responseType = new DispatchNodeResponseType();
        responseType.setTotalPoints(0.0);
        responseType.setIfElseResult(res);
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), responseType);

        // 设置用户聊天输入（优先使用 text，如果为空则使用 userChatInput 参数）
        result.put(DispatchNodeResponseKeyEnum.SKIP_HANDLE_ID.getValue(), skipHandleId);

        return result;
    }

    /**
     * 根据条件类型和条件列表计算最终结果
     * @param condition 条件类型，AND 或 OR
     * @param list 条件列表
     * @param variables 变量映射
     * @param runtimeNodes 运行时节点列表
     * @return 计算结果
     */
    private boolean getResult(IfElseConditionType condition, List<ConditionListItemType> list, Map<String, Object> variables, List<RuntimeNodeItemType> runtimeNodes) {
        List<Boolean> listResult = new ArrayList<>();
        for (ConditionListItemType item : list) {
            VariableConditionEnum variableCondition = VariableConditionEnum.getByValue(item.getCondition());
            if (variableCondition == null) {
                continue;
            }

            Object conditionLeftValue = WorkflowUtil.getReferenceVariableValue(item.getVariable(), runtimeNodes, variables);
            Object conditionRightValue;
            if ("reference".equals(item.getValueType())) {
                conditionRightValue = WorkflowUtil.getReferenceVariableValue(item.getValue(), runtimeNodes, variables);
            } else {
                conditionRightValue = item.getValue();
            }

            listResult.add(checkCondition(variableCondition, conditionLeftValue, conditionRightValue));
        }

        if (condition == IfElseConditionType.AND) {
            for (Boolean result : listResult) {
                if (!result) {
                    return false;
                }
            }
            return true;
        } else {
            for (Boolean result : listResult) {
                if (result) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * 根据条件类型检查输入值和目标值是否符合条件
     * @param condition 条件类型
     * @param inputValue 输入值
     * @param value 目标值
     * @return 比较结果，true 表示符合，false 表示不符合
     */
    private boolean checkCondition(VariableConditionEnum condition, Object inputValue, Object value) {
        switch (condition) {
            case isEmpty:
                return isEmpty(inputValue);
            case isNotEmpty:
                return !isEmpty(inputValue);
            case equalTo:
                return inputValue != null && value != null &&
                    inputValue.toString().trim().equals(value.toString().trim());
            case notEqual:
                return inputValue == null || value == null ||
                    !inputValue.toString().trim().equals(value.toString().trim());
            case greaterThan:
                return compareNumbers(inputValue, value) > 0;
            case lessThan:
                return compareNumbers(inputValue, value) < 0;
            case greaterThanOrEqualTo:
                return compareNumbers(inputValue, value) >= 0;
            case lessThanOrEqualTo:
                return compareNumbers(inputValue, value) <= 0;
            case include:
                return isInclude(inputValue, value);
            case notInclude:
                return !isInclude(inputValue, value);
            case startWith:
                return inputValue != null && value != null &&
                    inputValue.toString().trim().startsWith(value.toString());
            case endWith:
                return inputValue != null && value != null &&
                    inputValue.toString().trim().endsWith(value.toString());
            case reg:
                return checkRegex(inputValue, value);
            case lengthEqualTo:
                return checkLength(inputValue, value, 0) == 0;
            case lengthNotEqualTo:
                return checkLength(inputValue, value, 0) != 0;
            case lengthGreaterThan:
                return checkLength(inputValue, value, 1) > 0;
            case lengthGreaterThanOrEqualTo:
                return checkLength(inputValue, value, 1) >= 0;
            case lengthLessThan:
                return checkLength(inputValue, value, 1) < 0;
            case lengthLessThanOrEqualTo:
                return checkLength(inputValue, value, 1) <= 0;
            default:
                return false;
        }
    }

    /**
     * 检查对象是否为空
     * @param obj 要检查的对象
     * @return 为空返回 true，否则返回 false
     */
    private boolean isEmpty(Object obj) {
        if (obj == null) {
            return true;
        }
        if (obj instanceof String) {
            return ((String) obj).trim().isEmpty();
        }
        if (obj instanceof java.util.Collection) {
            return ((java.util.Collection<?>) obj).isEmpty();
        }
        if (obj instanceof java.util.Map) {
            return ((java.util.Map<?, ?>) obj).isEmpty();
        }
        if (obj instanceof Object[]) {
            return ((Object[]) obj).length == 0;
        }
        return false;
    }

    /**
     * 比较两个对象的数值大小
     * @param inputValue 输入值
     * @param value 目标值
     * @return 比较结果，大于 0 表示 inputValue 大，小于 0 表示 value 大，等于 0 表示相等
     */
    private int compareNumbers(Object inputValue, Object value) {
        try {
            double inputNum = Double.parseDouble(inputValue.toString());
            double targetNum = Double.parseDouble(value.toString());
            return Double.compare(inputNum, targetNum);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 检查 inputValue 是否包含 value
     * @param inputValue 输入值
     * @param value 目标值
     * @return 包含返回 true，否则返回 false
     */
    private boolean isInclude(Object inputValue, Object value) {
        if (inputValue == null || value == null) {
            return false;
        }
        if (inputValue instanceof String) {
            return ((String) inputValue).contains(value.toString());
        }
        if (inputValue instanceof java.util.Collection) {
            return ((java.util.Collection<?>) inputValue).contains(value);
        }
        if (inputValue instanceof Object[]) {
            for (Object item : (Object[]) inputValue) {
                if (item != null && item.equals(value)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 检查输入值是否符合正则表达式
     * @param inputValue 输入值
     * @param value 正则表达式
     * @return 符合返回 true，否则返回 false
     */
    private boolean checkRegex(Object inputValue, Object value) {
        if (!(inputValue instanceof String) || value == null) {
            return false;
        }
        String regex = value.toString();
        if (regex.startsWith("/")) {
            regex = regex.substring(1);
        }
        if (regex.endsWith("/")) {
            regex = regex.substring(0, regex.length() - 1);
        }
        try {
            return Pattern.compile(regex).matcher(((String) inputValue).trim()).find();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查输入值的长度与目标值的关系
     * @param inputValue 输入值
     * @param value 目标值
     * @param adjust 调整值，用于处理长度比较
     * @return 比较结果，大于 0 表示输入值长度大，小于 0 表示目标值大，等于 0 表示相等
     */
    private int checkLength(Object inputValue, Object value, int adjust) {
        try {
            int inputLength = 0;
            if (inputValue instanceof String) {
                inputLength = ((String) inputValue).length();
            } else if (inputValue instanceof java.util.Collection) {
                inputLength = ((java.util.Collection<?>) inputValue).size();
            } else if (inputValue instanceof Object[]) {
                inputLength = ((Object[]) inputValue).length;
            }
            int targetLength = Integer.parseInt(value.toString()) + adjust;
            return Integer.compare(inputLength, targetLength);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 获取分支结果名称，用于指定节点
     * @param i 分支索引
     * @return 分支结果名称
     */
    private String getElseIFLabel(Integer i) {
        return i == 0 ? IfElseResultEnum.IF.getName() : IfElseResultEnum.ELSE_IF.getName() + " " + i;
    }

}
