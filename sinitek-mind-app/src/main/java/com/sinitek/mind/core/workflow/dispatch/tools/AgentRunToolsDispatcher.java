package com.sinitek.mind.core.workflow.dispatch.tools;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.ai.util.PromptUtil;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemTextInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Agent工具调用节点调度器
 * 对应 TypeScript 中的 runTool/index.ts 文件
 */
@Slf4j
@Component("toolsDispatcher")
@RequiredArgsConstructor
public class AgentRunToolsDispatcher implements NodeDispatcher {

    private final ISystemModelService systemModelService;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        try {
            return dispatchRunTools(dispatchData);
        } catch (Exception e) {
            throw new BussinessException("RunTools dispatch failed: " + e.getMessage(), e);
        }
    }

    /**
     * 执行工具调用节点
     * 对应 TypeScript 中的 dispatchRunTools 函数
     *
     * @param dispatchData 调度数据
     * @return 节点执行结果
     */
    private Map<String, Object> dispatchRunTools(ModuleDispatchProps dispatchData) {
        // 提取基本参数
        RuntimeNodeItemType node = dispatchData.getNode();
        String nodeId = node.getNodeId();
        String name = node.getName();
        Boolean isEntry = node.getIsEntry();
        String version = node.getVersion();
        List<FlowNodeInputItemType> inputs = node.getInputs();

        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        List<RuntimeEdgeItemType> runtimeEdges = dispatchData.getRuntimeEdges();
        List<ChatItemType> histories = dispatchData.getHistories();
        List<ChatItemValueItemType> query = dispatchData.getQuery();
        String requestOrigin = dispatchData.getRequestOrigin();
        AppChatConfigType chatConfig = dispatchData.getChatConfig();
        WorkflowInteractiveResponseType lastInteractive = dispatchData.getLastInteractive();
        RunningUserInfo runningUserInfo = dispatchData.getRunningUserInfo();
        ExternalProviderType externalProvider = dispatchData.getExternalProvider();

        // 提取参数
        Map<String, Object> params = dispatchData.getParams();
        String model = (String) params.get("model");
        String systemPrompt = (String) params.get("systemPrompt");
        String userChatInput = (String) params.get("userChatInput");
        Integer history = (Integer) params.getOrDefault("history", 6);
        List<String> fileLinks = (List<String>) params.get("fileUrlList");
        Boolean aiChatVision = (Boolean) params.get("aiChatVision");
        Boolean aiChatReasoning = (Boolean) params.get("aiChatReasoning");

        // 获取模型信息
        SystemModelDTO toolModel = systemModelService.getModelDetail(model);
        boolean useVision = Boolean.TRUE.equals(aiChatVision) && Boolean.TRUE.equals(toolModel.getVision());
        List<ChatItemType> chatHistories = DispatchUtil.getHistories(history, histories);

        // 更新参数
        params.put("aiChatVision", aiChatVision && Boolean.TRUE.equals(toolModel.getVision()));
        params.put("aiChatReasoning", aiChatReasoning && Boolean.TRUE.equals(toolModel.getReasoning()));

        // 检查文件URL输入
        Optional<FlowNodeInputItemType> fileUrlInput = inputs.stream()
                .filter(item -> NodeInputKeyEnum.FILE_URL_LIST.getValue().equals(item.getKey()))
                .findFirst();
        if (fileUrlInput.isEmpty() || fileUrlInput.get().getValue() == null ||
                (fileUrlInput.get().getValue() instanceof List && ((List<?>) fileUrlInput.get().getValue()).isEmpty())) {
            fileLinks = null;
        }

        // 获取工具节点ID列表
        List<String> toolNodeIds = DispatchUtil.filterToolNodeIdByEdges(nodeId, runtimeEdges);

        // 构建工具节点列表
        List<ToolNodeItemType> toolNodes = toolNodeIds.stream()
                .map(toolNodeId -> runtimeNodes.stream()
                        .filter(runtimeNode -> toolNodeId.equals(runtimeNode.getNodeId()))
                        .findFirst()
                        .orElse(null))
                .filter(Objects::nonNull)
                .map(this::buildToolNodeItem)
                .collect(Collectors.toList());

        // 检查是否有读取文件工具
        boolean hasReadFilesTool = toolNodes.stream()
                .anyMatch(item -> FlowNodeTypeEnum.READ_FILES.getValue().equals(item.getFlowNodeType()));

        // 设置节点非入口
        node.setIsEntry(false);

        // 获取全局文件
        List<ChatItemValueItemFileInfo> globalFiles = ChatAdaptor.chatValue2RuntimePrompt(query).getFiles();

        // 获取多输入处理结果
        MultiInputResult multiInputResult = getMultiInput(
                runningUserInfo, chatHistories, requestOrigin,
                chatConfig != null && chatConfig.getFileSelectConfig() != null ?
                        chatConfig.getFileSelectConfig().getMaxFiles() : 20,
                chatConfig != null && chatConfig.getFileSelectConfig() != null ?
                        chatConfig.getFileSelectConfig().getCustomPdfParse() : null,
                fileLinks, globalFiles, hasReadFilesTool
        );

        // 构建系统提示词
        String concatenateSystemPrompt = buildSystemPrompt(
                toolModel.getDefaultSystemChatPrompt(), systemPrompt,
                multiInputResult.getDocumentQuoteText(), version
        );

        // 构建消息列表
        List<ChatItemType> messages = buildMessages(
                concatenateSystemPrompt, chatHistories, userChatInput,
                multiInputResult.getUserFiles(), hasReadFilesTool, lastInteractive, isEntry
        );

        // 文本审查
        if (Boolean.TRUE.equals(toolModel.getCensor()) &&
                (externalProvider.getOpenaiAccount() == null ||
                        !StringUtils.hasText(externalProvider.getOpenaiAccount().getKey()))) {
            // TODO: 实现文本审查逻辑
            log.debug("Text censor check for: {}", systemPrompt + "\n" + userChatInput);
        }

        // TODO 执行工具调用
        RunToolResponse toolResponse = executeToolCall(
                dispatchData, toolModel, toolNodes, messages, lastInteractive
        );

        // 计算使用量和积分
        UsageCalculationResult usageResult = calculateUsage(
                model, toolResponse.getToolNodeInputTokens(),
                toolResponse.getToolNodeOutputTokens(), externalProvider
        );

        // 构建返回结果
        return buildResponse(
                nodeId, name, toolResponse, usageResult, userChatInput,
                useVision, toolResponse.getCompleteMessages()
        );
    }

    /**
     * 构建工具节点项
     */
    private ToolNodeItemType buildToolNodeItem(RuntimeNodeItemType tool) {
        List<FlowNodeInputItemType> toolParams = new ArrayList<>();
        JSONSchemaInputType jsonSchema = null;

        if (!CollectionUtils.isEmpty(tool.getInputs())) {
            for (FlowNodeInputItemType input : tool.getInputs()) {
                if (!input.getToolDescription().isEmpty()) {
                    toolParams.add(input);
                }

                if (NodeInputKeyEnum.TOOL_DATA.getValue().equals(input.getKey()) ||
                        "toolData".equals(input.getKey())) {
                    jsonSchema = objectMapper.convertValue(input.getValue(), JSONSchemaInputType.class);
                }
            }
        }

        return ToolNodeItemType.builder()
                .nodeId(tool.getNodeId())
                .name(tool.getName())
                .flowNodeType(tool.getFlowNodeType())
                .inputs(tool.getInputs())
                .outputs(tool.getOutputs())
                .toolParams(toolParams)
                .jsonSchema(jsonSchema)
                .build();
    }

    /**
     * 获取多输入处理结果   -packages\service\core\workflow\dispatch\agent\runTool\index.ts#getMultiInput
     */
    public static MultiInputResult getMultiInput(
            RunningUserInfo runningUserInfo, List<ChatItemType> histories,
            String requestOrigin, Integer maxFiles, Boolean customPdfParse,
            List<String> fileLinks, List<ChatItemValueItemFileInfo> inputFiles,
            boolean hasReadFilesTool) {

        // 如果没有文件链接或有读取文件工具，直接返回
        if (CollectionUtils.isEmpty(fileLinks) || hasReadFilesTool) {
            return MultiInputResult.builder()
                    .documentQuoteText("")
                    .userFiles(inputFiles != null ? inputFiles : new ArrayList<>())
                    .build();
        }

        List<String> filesFromHistories = DispatchUtil.getHistoryFileLinks(histories);

        List<String> urls = new ArrayList<>();
        urls.addAll(filesFromHistories);
        urls.addAll(fileLinks);

        if (urls.isEmpty()) {
            return MultiInputResult.builder()
                    .documentQuoteText("")
                    .userFiles(new ArrayList<>())
                    .build();
        }

        // TODO: 这里需要实现类似 getFileContentFromLinks 的功能 获取text
        String text = "";
        // TODO 过滤：fileLinks.map((url) => parseUrlToFileType(url)).filter(Boolean)

        return MultiInputResult.builder()
                .documentQuoteText(text)
                .userFiles(new ArrayList<>())
                .build();
    }


    /**
     * 构建系统提示词
     */
    private String buildSystemPrompt(String defaultPrompt, String systemPrompt,
                                     String documentQuoteText, String version) {
        List<String> prompts = new ArrayList<>();

        if (StringUtils.hasText(defaultPrompt)) {
            prompts.add(defaultPrompt);
        }
        if (StringUtils.hasText(systemPrompt)) {
            prompts.add(systemPrompt);
        }
        if (StringUtils.hasText(documentQuoteText)) {

            String documentQuotePrompt = PromptUtil.getDocumentQuotePrompt(version);
            String o = (String) WorkflowUtil.replaceVariable(documentQuotePrompt, Map.of("quote", documentQuoteText));
            // TODO: 实现文档引用提示词替换逻辑
            prompts.add(o);
        }

        return String.join("\n\n===---===---===\n\n", prompts);
    }

    /**
     * 构建消息列表
     */
    private List<ChatItemType> buildMessages(
            String systemPrompt, List<ChatItemType> chatHistories,
            String userChatInput, List<ChatItemValueItemFileInfo> userFiles,
            boolean hasReadFilesTool, WorkflowInteractiveResponseType lastInteractive,
            Boolean isEntry) {

        List<ChatItemType> messages = new ArrayList<>();

        // 添加系统提示词
        if (StringUtils.hasText(systemPrompt)) {
            ChatItemValueItemType itemType = new ChatItemValueItemType();
            itemType.setType(ChatItemValueType.TEXT.getValue());
            itemType.setText(new ChatItemValueItemTextInfo(systemPrompt));
            ChatItemType systemMessage = new ChatItemType();
            systemMessage.setObj(ChatRoleEnum.SYSTEM);
            systemMessage.setValue(List.of(itemType));
            messages.add(systemMessage);
        }

        // 添加历史消息
        for (ChatItemType history : chatHistories) {
            if (ChatRoleEnum.HUMAN.equals(history.getObj())) {
                // 适配工具调用消息
                ChatItemType chatItemType = new ChatItemType();
                BeanUtils.copyProperties(history, chatItemType);
                List<ChatItemValueItemType> itemTypes = DispatchUtil.toolCallMessagesAdapt(history.getValue(), !hasReadFilesTool);
                chatItemType.setValue(itemTypes);
                messages.add(chatItemType);
            } else {
                messages.add(history);
            }
        }

        // 添加当前用户输入
        RuntimePromptType type = new RuntimePromptType();
        type.setText(userChatInput);
        type.setFiles(userFiles);
        List<ChatItemValueItemType> userInput = ChatAdaptor.runtimePrompt2ChatsValue(type);
        List<ChatItemValueItemType> itemTypes = DispatchUtil.toolCallMessagesAdapt(userInput, !hasReadFilesTool);
        ChatItemType userMessage = new ChatItemType();
        userMessage.setObj(ChatRoleEnum.HUMAN);
        userMessage.setValue(itemTypes);
        messages.add(userMessage);

        // 如果是交互式入口，移除最后两条消息
        if (lastInteractive != null && Boolean.TRUE.equals(isEntry) && messages.size() >= 2) {
            messages = messages.subList(0, messages.size() - 2);
        }

        return messages;
    }

    /**
     * 执行工具调用
     */
    private RunToolResponse executeToolCall(
            ModuleDispatchProps dispatchData, SystemModelDTO toolModel,
            List<ToolNodeItemType> toolNodes, List<ChatItemType> messages,
            WorkflowInteractiveResponseType lastInteractive) {

        // TODO: 实现具体的工具调用逻辑
        // 根据模型类型选择不同的调用方式：toolChoice、functionCall 或 promptCall

        log.debug("Tool call execution not fully implemented yet");

        return RunToolResponse.builder()
                .toolNodeInputTokens(0)
                .toolNodeOutputTokens(0)
                .completeMessages(new ArrayList<>())
                .assistantResponses(new ArrayList<>())
                .runTimes(1)
                .finishReason("stop")
                .dispatchFlowResponse(new ArrayList<>())
                .build();
    }

    /**
     * 计算使用量
     */
    private UsageCalculationResult calculateUsage(
            String model, int inputTokens, int outputTokens,
            ExternalProviderType externalProvider) {

        // TODO: 实现积分计算逻辑
        int totalPoints = 0;
        if (externalProvider.getOpenaiAccount() == null ||
                !StringUtils.hasText(externalProvider.getOpenaiAccount().getKey())) {
            // 计算内部积分消耗
            totalPoints = inputTokens + outputTokens; // 简化计算
        }

        return UsageCalculationResult.builder()
                .totalPoints(totalPoints)
                .modelName(model)
                .toolAIUsage(totalPoints)
                .build();
    }

    /**
     * 构建响应结果
     */
    private Map<String, Object> buildResponse(
            String nodeId, String name, RunToolResponse toolResponse,
            UsageCalculationResult usageResult, String userChatInput,
            boolean useVision, List<Object> completeMessages) {

        Map<String, Object> result = new HashMap<>();

        // 设置运行次数
        result.put(DispatchNodeResponseKeyEnum.RUN_TIMES.getValue(), toolResponse.getRunTimes());

        // 设置答案文本
//        String answerText = toolResponse.getAssistantResponses().stream()
//                .filter(response -> response.getText() != null &&
//                        StringUtils.hasText(response.getText().getContent()))
//                .map(response -> response.getText().getContent())
//                .collect(Collectors.joining(""));
//        result.put(NodeOutputKeyEnum.ANSWER_TEXT.getValue(), answerText);

        // 设置助手响应
        result.put(DispatchNodeResponseKeyEnum.ASSISTANT_RESPONSES.getValue(),
                toolResponse.getAssistantResponses());

        // 设置节点响应
        Map<String, Object> nodeResponse = new HashMap<>();
        nodeResponse.put("totalPoints", usageResult.getTotalPoints());
        nodeResponse.put("toolCallInputTokens", toolResponse.getToolNodeInputTokens());
        nodeResponse.put("toolCallOutputTokens", toolResponse.getToolNodeOutputTokens());
        nodeResponse.put("model", usageResult.getModelName());
        nodeResponse.put("query", userChatInput);
        nodeResponse.put("mergeSignId", nodeId);
        nodeResponse.put("finishReason", toolResponse.getFinishReason());
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);

        // 设置使用量
        List<Map<String, Object>> usages = new ArrayList<>();
        Map<String, Object> usage = new HashMap<>();
        usage.put("moduleName", name);
        usage.put("model", usageResult.getModelName());
        usage.put("totalPoints", usageResult.getToolAIUsage());
        usage.put("inputTokens", toolResponse.getToolNodeInputTokens());
        usage.put("outputTokens", toolResponse.getToolNodeOutputTokens());
        usages.add(usage);
        result.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), usages);

        return result;
    }

    // 内部数据类

    @Data
    @Builder
    private static class UsageCalculationResult {
        private int totalPoints;
        private String modelName;
        private int toolAIUsage;
    }

    @Data
    @Builder
    private static class RunToolResponse {
        private int toolNodeInputTokens;
        private int toolNodeOutputTokens;
        private List<Object> completeMessages;
        private List<Object> assistantResponses;
        private int runTimes;
        private String finishReason;
        private List<Object> dispatchFlowResponse;
    }

}
