package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 运行时提示类型
 * 对应 TypeScript 中的 RuntimeUserPromptType
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuntimePromptType {
    
    /**
     * 文件列表
     */
    @Builder.Default
    private List<ChatItemValueItemFileInfo> files = new ArrayList<>();
    
    /**
     * 文本内容
     */
    @Builder.Default
    private String text = "";
    
    /**
     * 提示类型（扩展字段，用于更灵活的处理）
     */
    private String type;
    
    /**
     * 提示值（扩展字段，用于存储任意类型的值）
     */
    private Object value;
}