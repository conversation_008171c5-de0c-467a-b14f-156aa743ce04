package com.sinitek.mind.core.workflow.dispatch.tools;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.SseMapResponse;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 变量更新调度器
 * 对应 TypeScript 中的 dispatchUpdateVariable 函数
 * 用于处理工作流中的变量更新操作
 */
@Slf4j
@Component
public class UpdateVarDispatcher implements NodeDispatcher {

    /**
     * 全局变量节点ID常量
     */
    private static final String VARIABLE_NODE_ID = "VARIABLE_NODE_ID";

    /**
     * 调度方法 - 主要入口点
     * 
     * @param dispatchData 调度数据，包含执行所需的所有参数
     * @return 执行结果映射，包含更新后的变量状态和响应数据
     */
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        // 1. 参数提取和验证逻辑 - 从 ModuleDispatchProps 中获取 updateList
        Map<String, Object> params = dispatchData.getParams();
        Map<String, Object> variables = dispatchData.getVariables();
        List<RuntimeNodeItemType> runtimeNodes = dispatchData.getRuntimeNodes();
        RunningAppInfo runningAppInfo = dispatchData.getRunningAppInfo();
        ExternalProviderType externalProvider = dispatchData.getExternalProvider();

        List<String> nodeIds = runtimeNodes.stream()
                .map(RuntimeNodeItemType::getNodeId)
                .toList();

        // 获取更新列表参数
        List<UpdateListItem> updateList = (List<UpdateListItem>) params.get(NodeInputKeyEnum.UPDATE_LIST.getValue());

        List<Object> result = updateList.stream()
                .map(item -> {
                    Object[] variable = item.getVariable();

                    if (!WorkflowUtil.isValidReferenceValue(variable, nodeIds)) {
                        return null;
                    }

                    Object varNodeId = variable[0];
                    Object varKey = variable[1];

                    if (Objects.isNull(varKey)) {
                        return null;
                    }

                    Object value;
                    // If first item is empty, it means it is a input value
                    if (Objects.isNull(item.getValue()[0])) {
                        Object temp;
                        if (varKey instanceof String) {
                            temp = WorkflowUtil.replaceEditorVariable(item.getValue()[1], runtimeNodes, variables);
                        } else {
                            temp = item.getValue()[1];
                        }
                        value = WorkflowUtil.valueTypeFormat(temp, item.getValueType());
                    } else {
                        value = WorkflowUtil.getReferenceVariableValue(item.getValue(), runtimeNodes, variables);
                    }

                    // Update node output
                    // Global variable
                    if (Objects.equals(varNodeId, VARIABLE_NODE_ID)) {
                        variables.put((String) varKey, value);
                    } else {
                        // Other nodes
                        runtimeNodes.stream()
                                .filter(node -> node.getNodeId().equals(varNodeId))
                                .findFirst()
                                .ifPresent(node ->
                                        node.getOutputs().stream()
                                                .filter(output -> output.getId().equals(varKey))
                                                .findFirst()
                                                .ifPresent(output -> output.setValue(value))
                                );
                    }

                    return value;
                })
                .toList();

        if (!runningAppInfo.getIsChildApp()) {
            WorkflowStreamResponse workflowStreamResponse = WorkflowStreamResponse.builder()
                    .event(SseResponseEventEnum.UPDATE_VARIABLES.getValue())
                    .data(new SseMapResponse<>(WorkflowUtil.removeSystemVariable(variables, externalProvider.getExternalWorkflowVariables())))
                    .build();
            dispatchData.getWorkflowStreamResponse()
                    .accept(workflowStreamResponse);
        }

        // 构建响应结构
        Map<String, Object> nodeResult = new HashMap<>();
        nodeResult.put(DispatchNodeResponseKeyEnum.NEW_VARIABLES.getValue(), variables);
        nodeResult.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), result);
        return nodeResult;
    }

}