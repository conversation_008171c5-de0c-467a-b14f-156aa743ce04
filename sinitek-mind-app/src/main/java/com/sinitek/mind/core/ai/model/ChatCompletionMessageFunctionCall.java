package com.sinitek.mind.core.ai.model;

import lombok.Data;

/**
 * 聊天完成消息函数调用
 * 对应 TypeScript 中扩展的 ChatCompletionMessageFunctionCall
 */
@Data
public class ChatCompletionMessageFunctionCall {
    private String name;
    private String arguments;
    private String id;
    private String toolName;
    private String toolAvatar;
    
    public ChatCompletionMessageFunctionCall() {}
    
    public ChatCompletionMessageFunctionCall(String name, String arguments) {
        this.name = name;
        this.arguments = arguments;
    }

}