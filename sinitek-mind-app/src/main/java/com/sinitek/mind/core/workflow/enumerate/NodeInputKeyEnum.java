package com.sinitek.mind.core.workflow.enumerate;

import lombok.Getter;

/**
 * 节点输入键枚举
 * 对应 TypeScript 中的 NodeInputKeyEnum
 * <AUTHOR>
 */
@Getter
public enum NodeInputKeyEnum {
    // old
    WELCOME_TEXT("welcomeText"),
    SWITCH("switch"), // a trigger switch
    HISTORY("history"),
    ANSWER_TEXT("text"),

    // system config
    QUESTION_GUIDE("questionGuide"),
    TTS("tts"),
    WHISPER("whisper"),
    VARIABLES("variables"),
    SCHEDULE_TRIGGER("scheduleTrigger"),
    CHAT_INPUT_GUIDE("chatInputGuide"),
    AUTO_EXECUTE("autoExecute"),

    // plugin config
    INSTRUCTION("instruction"),

    // entry
    USER_CHAT_INPUT("userChatInput"),
    INPUT_FILES("inputFiles"),

    AGENTS("agents"), // cq agent key

    // latest
    // common
    AI_MODEL("model"),
    AI_SYSTEM_PROMPT("systemPrompt"),
    DESC<PERSON>P<PERSON><PERSON>("description"),
    ANY_INPUT("system_anyInput"),
    TEXTAREA_INPUT("system_textareaInput"),
    ADD_INPUT_PARAM("system_addInputParam"),
    FORBID_STREAM("system_forbid_stream"),
    HEADER_SECRET("system_header_secret"),

    // history
    HISTORY_MAX_AMOUNT("maxContext"),

    // ai chat
    AI_CHAT_TEMPERATURE("temperature"),
    AI_CHAT_MAX_TOKEN("maxToken"),
    AI_CHAT_SETTING_MODAL("aiSettings"),
    AI_CHAT_IS_RESPONSE_TEXT("isResponseAnswerText"),
    AI_CHAT_QUOTE_ROLE("aiChatQuoteRole"),
    AI_CHAT_QUOTE_TEMPLATE("quoteTemplate"),
    AI_CHAT_QUOTE_PROMPT("quotePrompt"),
    AI_CHAT_DATASET_QUOTE("quoteQA"),
    AI_CHAT_VISION("aiChatVision"),
    STRING_QUOTE_TEXT("stringQuoteText"),
    AI_CHAT_REASONING("aiChatReasoning"),
    AI_CHAT_TOP_P("aiChatTopP"),
    AI_CHAT_STOP_SIGN("aiChatStopSign"),
    AI_CHAT_RESPONSE_FORMAT("aiChatResponseFormat"),
    AI_CHAT_JSON_SCHEMA("aiChatJsonSchema"),

    // dataset
    DATASET_SELECT_LIST("datasets"),
    DATASET_SIMILARITY("similarity"),
    DATASET_MAX_TOKENS("limit"),
    DATASET_SEARCH_MODE("searchMode"),
    DATASET_SEARCH_EMBEDDING_WEIGHT("embeddingWeight"),

    DATASET_SEARCH_USING_RE_RANK("usingReRank"),
    DATASET_SEARCH_RERANK_WEIGHT("rerankWeight"),
    DATASET_SEARCH_RERANK_MODEL("rerankModel"),

    DATASET_SEARCH_USING_EXTENSION_QUERY("datasetSearchUsingExtensionQuery"),
    DATASET_SEARCH_EXTENSION_MODEL("datasetSearchExtensionModel"),
    DATASET_SEARCH_EXTENSION_BG("datasetSearchExtensionBg"),
    COLLECTION_FILTER_MATCH("collectionFilterMatch"),
    AUTH_TMB_ID("authTmbId"),
    DATASET_DEEP_SEARCH("datasetDeepSearch"),
    DATASET_DEEP_SEARCH_MODEL("datasetDeepSearchModel"),
    DATASET_DEEP_SEARCH_MAX_TIMES("datasetDeepSearchMaxTimes"),
    DATASET_DEEP_SEARCH_BG("datasetDeepSearchBg"),

    // concat dataset
    DATASET_QUOTE_LIST("system_datasetQuoteList"),

    // context extract
    CONTEXT_EXTRACT_INPUT("content"),
    EXTRACT_KEYS("extractKeys"),

    // http
    HTTP_REQ_URL("system_httpReqUrl"),
    HTTP_HEADERS("system_httpHeader"),
    HTTP_METHOD("system_httpMethod"),
    HTTP_PARAMS("system_httpParams"),
    HTTP_JSON_BODY("system_httpJsonBody"),
    HTTP_FORM_BODY("system_httpFormBody"),
    HTTP_CONTENT_TYPE("system_httpContentType"),
    HTTP_TIMEOUT("system_httpTimeout"),
    ABANDON_HTTP_URL("url"),

    // app
    RUN_APP_SELECT_APP("app"),

    // plugin
    PLUGIN_ID("pluginId"),
    PLUGIN_START("pluginStart"),

    // if else
    CONDITION("condition"),
    IF_ELSE_LIST("ifElseList"),

    // variable update
    UPDATE_LIST("updateList"),

    // code
    CODE("code"),
    CODE_TYPE("codeType"), // js|py

    // read files
    FILE_URL_LIST("fileUrlList"),

    // user select
    USER_SELECT_OPTIONS("userSelectOptions"),

    // loop
    LOOP_INPUT_ARRAY("loopInputArray"),
    CHILDREN_NODE_ID_LIST("childrenNodeIdList"),
    NODE_WIDTH("nodeWidth"),
    NODE_HEIGHT("nodeHeight"),
    LOOP_NODE_INPUT_HEIGHT("loopNodeInputHeight"),
    // loop start
    LOOP_START_INPUT("loopStartInput"),
    LOOP_START_INDEX("loopStartIndex"),
    // loop end
    LOOP_END_INPUT("loopEndInput"),

    // form input
    USER_INPUT_FORMS("userInputForms"),

    // comment
    COMMENT_TEXT("commentText"),
    COMMENT_SIZE("commentSize"),

    // Tool
    TOOL_DATA("system_toolData"),
    TOOL_SET_DATA("system_toolSetData");

    private final String value;

    NodeInputKeyEnum(String value) {
        this.value = value;
    }

    /**
     * 根据字符串值获取对应的枚举
     * @param value 字符串值
     * @return 对应的枚举，如果未找到则返回 null
     */
    public static NodeInputKeyEnum fromValue(String value) {
        for (NodeInputKeyEnum keyEnum : values()) {
            if (keyEnum.value.equals(value)) {
                return keyEnum;
            }
        }
        return null;
    }

    @Override
    public String toString() {
        return value;
    }
}
