package com.sinitek.mind.core.ai.model;

import lombok.Data;

import java.util.Map;

/**
 * 提示模板项
 * 对应 TypeScript 中的 PromptTemplateItem
 */
@Data
public class PromptTemplateItem {
    private String title;
    private String desc;
    private Map<String, String> value;
    
    public PromptTemplateItem() {}
    
    public PromptTemplateItem(String title, String desc, Map<String, String> value) {
        this.title = title;
        this.desc = desc;
        this.value = value;
    }

}