package com.sinitek.mind.core.app.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.app.model.SourceMemberDTO;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * app列表查询返回值DTO,对应type:AppListItemType
 */
@Data
@Schema(description = "应用列表项")
public class AppListItemDTO {

    @Schema(description = "应用ID")
    private String _id;

    @Schema(description = "团队成员ID")
    private String tmbId;

    @Schema(description = "应用名称")
    private String name;

    @Schema(description = "应用头像")
    private String avatar;

    @Schema(description = "应用介绍")
    private String intro;

    @Schema(description = "应用类型")
    private String type; // AppTypeEnum

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "插件数据")
    private PluginData pluginData;

//    @Schema(description = "权限信息")
    private PermissionDTO permission;

    @Schema(description = "是否继承权限")
    private Boolean inheritPermission;

    @Schema(description = "是否私有")
    @JsonProperty("private")
    private Boolean isPrivate;

    @Schema(description = "来源成员信息")
    private SourceMemberDTO sourceMember;
} 