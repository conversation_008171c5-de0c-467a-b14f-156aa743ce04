package com.sinitek.mind.core.app.util;

import com.sinitek.mind.core.app.enumerate.PluginSourceEnum;
import com.sinitek.mind.core.app.model.SplitCombineToolIdRes;
import com.sinitek.mind.core.app.service.IPluginService;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeTemplateType;
import com.sinitek.mind.core.workflow.model.PluginDataType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.dataset.repository.DatasetRepository;
import com.sinitek.sirm.common.spring.SpringFactory;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

public class AppUtil {
    
    public static String getI18nAppType(String appType) {
        if (appType == null) {
            return "未知类型";
        }
        
        switch (appType) {
            case "chat":
                return "对话应用";
            case "workflow":
                return "工作流应用";
            case "plugin":
                return "插件应用";
            case "folder":
                return "文件夹";
            default:
                return "未知类型";
        }
    }

    public static List<StoreNodeItemType> rewriteAppWorkflowToDetail(List<StoreNodeItemType> nodes, String teamId, boolean isRoot, String ownerTmbId) {
        Set<String> datasetIdSet = new HashSet<>();
        // 1. 处理 plugin 节点，补充 pluginData、versionLabel 等
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (StoreNodeItemType node : nodes) {
            if (node.getPluginId() == null) continue;
            futures.add(CompletableFuture.runAsync(() -> {
                try {
                    IPluginService pluginService = SpringFactory.getBean(IPluginService.class);
                    FlowNodeTemplateType preview = pluginService.getChildAppPreviewNode(node.getPluginId(), node.getVersion());
                    node.setPluginData(new PluginDataType(
                            preview.getDiagram(),
                            preview.getUserGuide(),
                            preview.getCourseUrl(),
                            preview.getName(),
                            preview.getAvatar()
                    ));
                    node.setVersionLabel(preview.getVersionLabel());
                    node.setIsLatestVersion(preview.getIsLatestVersion());
                    node.setVersion(preview.getVersion());
                } catch (Exception e) {
                    node.setPluginData(new PluginDataType(e.getMessage()));
                }
            }));
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();

        // 2. 收集所有 datasetId
        for (StoreNodeItemType node : nodes) {
            if (!"datasetSearchNode".equals(node.getFlowNodeType())) continue;
            List<FlowNodeInputItemType> inputs = node.getInputs();
            Optional<FlowNodeInputItemType> first = inputs.stream().filter(item -> NodeInputKeyEnum.DATASET_SELECT_LIST.getValue().equals(item.getKey()))
                    .findFirst();
            FlowNodeInputItemType firstItem = first.orElse(null);
            if (firstItem == null) continue;
            Object rawValue = firstItem.getValue();
            if (rawValue == null) continue;

            if (rawValue instanceof List) {
                List<?> list = (List<?>) rawValue;
                for (Object obj : list) {
                    if (obj instanceof Map) {
                        String datasetId = (String) ((Map<?, ?>) obj).get("datasetId");
                        if (datasetId != null) datasetIdSet.add(datasetId);
                    }
                }
            } else if (rawValue instanceof Map) {
                String datasetId = (String) ((Map<?, ?>) rawValue).get("datasetId");
                if (datasetId != null) datasetIdSet.add(datasetId);
            }
        }

        if (datasetIdSet.isEmpty()) return nodes;

        // 3. 查询所有 dataset 信息
        DatasetRepository datasetRepository = SpringFactory.getBean(DatasetRepository.class);
        List<Dataset> datasetList = datasetRepository.findDatasetsByIdIn(new ArrayList<>(datasetIdSet));
        Map<String, Dataset> datasetMap = datasetList.stream()
                .collect(Collectors.toMap(Dataset::getId, ds -> ds));

        // 4. 重写节点中的 dataset 信息
        for (StoreNodeItemType node : nodes) {
            if (!"datasetSearchNode".equals(node.getFlowNodeType())) continue;
            for (FlowNodeInputItemType item : node.getInputs()) {
                if (!"datasetSelectList".equals(item.getKey())) continue;
                Object val = item.getValue();
                if (val instanceof List) {
                    List<Map<String, Object>> newList = new ArrayList<>();
                    for (Object obj : (List<?>) val) {
                        if (obj instanceof Map) {
                            String datasetId = (String) ((Map<?, ?>) obj).get("datasetId");
                            Dataset data = datasetMap.get(datasetId);
                            if (data == null) {
                                newList.add(makeNotFoundDataset(datasetId));
                            } else {
                                newList.add(makeDatasetInfo(data));
                            }
                        }
                    }
                    item.setValue(newList);
                } else if (val instanceof Map) {
                    String datasetId = (String) ((Map<?, ?>) val).get("datasetId");
                    Dataset data = datasetMap.get(datasetId);
                    List<Map<String, Object>> newList = new ArrayList<>();
                    if (data == null) {
                        newList.add(makeNotFoundDataset(datasetId));
                    } else {
                        newList.add(makeDatasetInfo(data));
                    }
                    item.setValue(newList);
                }
            }
        }

        return nodes;
    }

    private static Map<String, Object> makeNotFoundDataset(String datasetId) {
        Map<String, Object> map = new HashMap<>();
        map.put("datasetId", datasetId);
        map.put("avatar", "");
        map.put("name", "Dataset not found");
        map.put("vectorModel", "");
        return map;
    }

    private static Map<String, Object> makeDatasetInfo(Dataset data) {
        Map<String, Object> map = new HashMap<>();
        map.put("datasetId", data.getId());
        map.put("avatar", data.getAvatar());
        map.put("name", data.getName());
        map.put("vectorModel", data.getVectorModel());
        return map;
    }

    public static SplitCombineToolIdRes splitCombineToolId(String id) {
        String[] split = id.split("-");
        if (split.length == 1) {
            return new SplitCombineToolIdRes(PluginSourceEnum.PERSONAL.getValue(), id);
        }
        String source = split[0];
        String pluginId = split[1];

        return new SplitCombineToolIdRes(source, pluginId);
    }
    public static String i18n(String key) {
        // 返回国际化字符串
        return key;
    }
}