package com.sinitek.mind.core.app.service.impl;

import com.sinitek.mind.core.app.repository.ImageRepository;
import com.sinitek.mind.core.app.service.IImageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ImageServiceImpl implements IImageService {

    private ImageRepository imageRepository;

    @Override
    public void removeImageByPath(String path) {
        if (path != null) {
            String[] split = path.split("/");
            String name = split[split.length - 1];
            if (name != null) {
                String id = name.split("\\.")[0];
                imageRepository.deleteById(id);
            }
        }
    }
}
