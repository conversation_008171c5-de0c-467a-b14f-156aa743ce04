package com.sinitek.mind.core.app.service;

import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.AppDetailType;

import java.util.List;

public interface IAppService {

    /**
     * 获取应用列表
     *
     * @param request 请求参数
     * @return 应用列表
     */
    List<AppListItemDTO> getAppList(ListAppDTO request);

    /**
     * 创建应用
     * @param body 创建应用请求
     * @return 应用ID
     */
    String createApp(CreateAppDTO body);

    AppDetailType getAppDetail(String appId);

    /**
     * 查找应用及其所有子应用
     * 对应TypeScript中的findAppAndAllChildren方法
     */
    List<App> findAppAndAllChildren(String teamId, String appId);

    /**
     * 根据ID列表获取应用基本信息
     * 对应TypeScript中的getAppBasicInfoByIds方法
     */
    List<AppBasicInfoDTO> getAppBasicInfoByIds(String teamId, List<String> ids);

    /**
     * 根据团队ID查找应用
     */
    List<App> findByTeamId(String teamId);

    /**
     * 根据团队ID和类型查找应用
     */
    List<App> findByTeamIdAndType(String teamId, String type);

    /**
     * 根据团队ID查找应用，按更新时间倒序排列
     */
    List<App> findByTeamIdOrderByUpdateTimeDesc(String teamId);

    void deleteApp(String appId);

    String copyApp(String appId);

    App updateApp(AppUpdateDTO params);

    String transitionWorkFlow(TransitionWorkflowDTO body);

    String resumeInheritPermission(String appId);

    List<BasicInfoResDTO> getBasicInfo(String teamId, List<String> ids);
}
