package com.sinitek.mind.core.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;


/**
 * 更新应用版本请求DTO
 */
@Data
@Schema(description = "更新应用版本请求")
public class UpdateAppVersionDTO {

    @NotBlank(message = "应用ID不能为空")
    @Schema(description = "应用ID", required = true)
    private String appId;

    @NotBlank(message = "版本ID不能为空")
    @Schema(description = "版本ID", required = true)
    private String versionId;

    @NotBlank(message = "版本名称不能为空")
    @Schema(description = "版本名称", required = true)
    private String versionName;
}