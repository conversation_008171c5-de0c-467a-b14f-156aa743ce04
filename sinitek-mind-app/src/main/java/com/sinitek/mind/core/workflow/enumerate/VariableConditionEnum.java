package com.sinitek.mind.core.workflow.enumerate;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 变量条件枚举
 */
@Getter
@AllArgsConstructor
public enum VariableConditionEnum {
    isEmpty("isEmpty"),
    isNotEmpty("isNotEmpty"),
    equalTo("equalTo"),
    notEqual("notEqual"),
    greaterThan("greaterThan"),
    lessThan("lessThan"),
    greaterThanOrEqualTo("greaterThanOrEqualTo"),
    lessThanOrEqualTo("lessThanOrEqualTo"),
    include("include"),
    notInclude("notInclude"),
    startWith("startWith"),
    endWith("endWith"),
    reg("reg"),
    lengthEqualTo("lengthEqualTo"),
    lengthNotEqualTo("lengthNotEqualTo"),
    lengthGreaterThan("lengthGreaterThan"),
    lengthGreaterThanOrEqualTo("lengthGreaterThanOrEqualTo"),
    lengthLessThan("lengthLessThan"),
    lengthLessThanOrEqualTo("lengthLessThanOrEqualTo");

    private final String value;

    public static VariableConditionEnum getByValue(String value) {
        for (VariableConditionEnum conditionEnum : VariableConditionEnum.values()) {
            if (conditionEnum.getValue().equals(value)) {
                return conditionEnum;
            }
        }
        return equalTo;
    }
}