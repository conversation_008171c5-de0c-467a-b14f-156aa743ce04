package com.sinitek.mind.core.workflow.dispatch.plugin;

import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatFileType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import com.sinitek.mind.core.workflow.model.RuntimePromptType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("pluginInputDispatcher")
public class PluginInputDispatcher implements NodeDispatcher {
    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        try {
            return dispatchPluginInput(dispatchData);
        } catch (Exception e) {
            log.error("Plugin Input dispatch failed: {}", e.getMessage(), e);
            throw new RuntimeException("Plugin Input dispatch failed: " + e.getMessage(), e);
        }
    }

    private Map<String, Object> dispatchPluginInput(ModuleDispatchProps props) {

        // 提取参数
        Map<String, Object> params = props.getParams();
        List<ChatItemValueItemType> query = props.getQuery();

        RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(query);
        List<ChatItemValueItemFileInfo> files = runtimePromptType.getFiles();

        /**
         * 对 params 中文件类型数据进行处理
         * 插件单独运行时，这里会是一个特殊的数组
         * 插件调用的话，这个参数是一个 string[] 不会进行处理
         * 硬性要求：API 单独调用插件时，要避免这种特殊类型冲突
         */
        // 遍历params
        for (String key : params.keySet()) {
            Object obj = params.get(key);
            // 检查是否为数组且所有元素都是文件或图片类型
            if (obj instanceof List<?>) {
                List<?> list = (List<?>) obj;
                // 检查是否所有元素都是ChatItemValueItemFileInfo 且类型为file或者image
                boolean allFile = list.stream().allMatch(item-> {
                    if (item instanceof ChatItemValueItemFileInfo) {
                        ChatItemValueItemFileInfo fileInfo = (ChatItemValueItemFileInfo) item;
                        String type = fileInfo.getType();
                        return ChatFileType.FILE.getValue().equals(type) || ChatFileType.IMAGE.getValue().equals(type);
                    }
                    return false;
                });

                if (allFile && !list.isEmpty()) {
                    List<String> urls = list.stream()
                            .map(item -> ((ChatItemValueItemFileInfo) item).getUrl())
                            .toList();

                    params.put(key, urls);
                }

            }
        }

        List<String> urls = files.stream()
                .map(item -> item == null ? "" : item.getUrl() == null ? "" : item.getUrl())
                .filter(url -> !url.isEmpty())
                .toList();

        // 构建返回结果
        Map<String, Object> result = new HashMap<>(params);
        result.put(NodeOutputKeyEnum.USER_FILES.getValue(), urls);
        // 添加节点响应
        Map<String, Object> nodeResponse = new HashMap<>();
        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);
        return result;
    }
}
