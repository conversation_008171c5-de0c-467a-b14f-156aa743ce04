package com.sinitek.mind.core.ai.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 图片内容部分
 * 对应 OpenAI SDK 中的图片内容类型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatCompletionContentPartImage extends ChatCompletionContentPart {
    private ImageUrl imageUrl;
    
    public ChatCompletionContentPartImage() {
        super("image_url");
    }
    
    public ChatCompletionContentPartImage(ImageUrl imageUrl) {
        super("image_url");
        this.imageUrl = imageUrl;
    }

    /**
     * 图片URL信息
     */
    @Data
    public static class ImageUrl {
        private String url;
        private String detail; // "auto", "low", "high"
        
        public ImageUrl() {}
        
        public ImageUrl(String url) {
            this.url = url;
        }
        
        public ImageUrl(String url, String detail) {
            this.url = url;
            this.detail = detail;
        }

    }
}