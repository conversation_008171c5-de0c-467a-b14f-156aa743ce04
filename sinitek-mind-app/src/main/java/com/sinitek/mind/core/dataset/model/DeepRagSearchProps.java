package com.sinitek.mind.core.dataset.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class DeepRagSearchProps extends SearchDatasetDataProps {

    private String datasetDeepSearchModel;

    private Integer datasetDeepSearchMaxTimes;

    private String datasetDeepSearchBg;
}
