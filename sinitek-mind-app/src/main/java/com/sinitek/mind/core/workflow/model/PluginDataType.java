package com.sinitek.mind.core.workflow.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PluginDataType {
    private String diagram;
    private String userGuide;
    private String courseUrl;
    private String name;
    private String avatar;
    private String error;

    public PluginDataType(String error) {
        this.error = error;
    }

    public PluginDataType(String diagram, String userGuide, String courseUrl,
                          String name, String avatar) {
        this.diagram = diagram;
        this.userGuide = userGuide;
        this.courseUrl = courseUrl;
        this.name = name;
        this.avatar = avatar;
    }
}
