package com.sinitek.mind.core.app.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 更新MCP工具集响应
 * 对应原始的 updateMCPToolsResponse
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMCPToolsResDTO {
    
    /**
     * 更新的应用ID
     */
    private String appId;
    
    /**
     * 更新的子工具ID列表
     */
    private List<String> updatedToolIds;
    
    /**
     * 新创建的子工具ID列表
     */
    private List<String> createdToolIds;
    
    /**
     * 删除的子工具ID列表
     */
    private List<String> deletedToolIds;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 状态信息
     */
    private String status;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 构造成功响应
     */
    public static UpdateMCPToolsResDTO success(String appId,
                                               java.util.List<String> updatedToolIds,
                                               java.util.List<String> createdToolIds,
                                               java.util.List<String> deletedToolIds) {
        UpdateMCPToolsResDTO response = new UpdateMCPToolsResDTO();
        response.setAppId(appId);
        response.setUpdatedToolIds(updatedToolIds);
        response.setCreatedToolIds(createdToolIds);
        response.setDeletedToolIds(deletedToolIds);
        response.setUpdateTime(java.time.LocalDateTime.now());
        response.setStatus("success");
        response.setMessage("MCP工具集更新成功");
        return response;
    }
}