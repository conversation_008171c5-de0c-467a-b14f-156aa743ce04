package com.sinitek.mind.core.chat.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.ExportChatLogsBody;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.chat.dto.ChatLogResponse;
import com.sinitek.mind.core.chat.dto.GetChatLogsRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;

public interface IChatLogService {

    PageResult<ChatLogResponse> getChatLogs(GetChatLogsRequest request, String teamId, String tmbId, App app);

    void exportChatLogs(ExportChatLogsBody request, String teamId, HttpServletResponse response) throws IOException;
}
