package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.UserGuide;
import com.sinitek.mind.core.workflow.model.WorkflowTemplateBasicType;
import lombok.Data;

import java.util.List;

/**
 * 应用模板响应数据
 */
@Data
public class AppTemplateResDTO {
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 模板名称
     */
    private String name;
    
    /**
     * 模板介绍
     */
    private String intro;
    
    /**
     * 模板头像
     */
    private String avatar;
    
    /**
     * 标签列表
     */
    private List<String> tags;
    
    /**
     * 应用类型
     */
    private String type;
    
    /**
     * 作者
     */
    private String author;
    
    /**
     * 用户指南
     */
    private UserGuide userGuide;
    
    /**
     * 工作流配置（空对象）
     */
    private WorkflowTemplateBasicType workflow;
}