package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "聊天测试请求")
public class ChatTestRequest {

    @Schema(description = "节点列表")
    private List<StoreNodeItemType> nodes;

    @Schema(description = "边列表")
    private List<StoreEdgeItemType> edges;

    @Schema(description = "消息列表")
    private List<ChatCompletionMessageParam> messages;

    @Schema(description = "响应聊天项ID")
    private String responseChatItemId;

    @Schema(description = "变量")
    private Map<String, Object> variables;

    @Schema(description = "应用名称")
    private String appName;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "聊天配置")
    private AppChatConfigType chatConfig;

    @Schema(description = "聊天ID")
    private String chatId;
}