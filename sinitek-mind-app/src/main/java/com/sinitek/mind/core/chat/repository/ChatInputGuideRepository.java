package com.sinitek.mind.core.chat.repository;

import com.sinitek.mind.core.chat.entity.ChatInputGuide;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface ChatInputGuideRepository extends MongoRepository<ChatInputGuide, String> {
    void deleteByAppId(String id);


    /**
     * 根据应用ID统计聊天输入引导数量
     * @param appId 应用ID
     * @return 数量
     */
    long countByAppId(String appId);
}
