package com.sinitek.mind.core.chat.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.chat.service.IChatInputGuideService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/mind/api/core/chat/inputGuide")
@RequiredArgsConstructor
@Tag(name = "输入指引管理", description = "输入指引管理相关接口")
public class ChatInputGuideController {
    private final IChatInputGuideService chatInputGuideService;

    @GetMapping("/countTotal")
    @Operation(summary = "统计聊天输入引导总数", description = "根据应用ID统计聊天输入引导的总数")
    public ApiResponse<Integer> countTotal(
            @RequestParam("appId") String appId) {

        try {

            Integer total = chatInputGuideService.countTotal(appId);
            return ApiResponse.success(total);
        } catch (Exception e) {
            log.error("统计聊天输入引导总数失败: {}", e.getMessage(), e);
            return ApiResponse.error(e.getMessage());
        }
    }

}
