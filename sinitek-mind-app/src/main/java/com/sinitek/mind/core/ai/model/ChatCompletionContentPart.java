package com.sinitek.mind.core.ai.model;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 聊天完成内容部分基类
 * 对应 TypeScript 中的 ChatCompletionContentPart
 */
@JsonTypeInfo(
    use = JsonTypeInfo.Id.NAME,
    include = JsonTypeInfo.As.PROPERTY,
    property = "type"
)
@JsonSubTypes({
    @JsonSubTypes.Type(value = ChatCompletionContentPartText.class, name = "text"),
    @JsonSubTypes.Type(value = ChatCompletionContentPartImage.class, name = "image_url"),
    @JsonSubTypes.Type(value = ChatCompletionContentPartFile.class, name = "file_url")
})
@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class ChatCompletionContentPart {
    protected String type;
}