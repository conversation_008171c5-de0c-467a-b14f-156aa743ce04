package com.sinitek.mind.core.app.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import io.swagger.parser.OpenAPIParser;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.Paths;
import io.swagger.v3.oas.models.servers.Server;
import io.swagger.v3.parser.core.models.SwaggerParseResult;
import org.springframework.util.StringUtils;
import org.yaml.snakeyaml.Yaml;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * HTTP插件工具类
 */
public class HttpPluginUtils {

    /**
     * 将HTTP API Schema转换为插件
     *
     * @param parentId 父ID
     * @param apiSchemaStr API Schema字符串
     * @param customHeader 自定义请求头
     * @return 子插件列表
     */
    public static List<CreateAppDTO> httpApiSchema2Plugins(
            String parentId,
            String apiSchemaStr,
            String customHeader) {
        
        List<CreateAppDTO> result = new ArrayList<>();
        
        try {
            // 解析API Schema
            OpenAPI openAPI = parseOpenAPISchema(apiSchemaStr);
            if (openAPI == null) {
                return result;
            }
            
            // 获取服务器路径
            String baseUrl = "";
            if (openAPI.getServers() != null && !openAPI.getServers().isEmpty()) {
                Server server = openAPI.getServers().get(0);
                baseUrl = server.getUrl();
            }
            
            // 处理每个路径
            Paths paths = openAPI.getPaths();
            if (paths == null) {
                return result;
            }
            
            for (Map.Entry<String, PathItem> pathEntry : paths.entrySet()) {
                String path = pathEntry.getKey();
                PathItem pathItem = pathEntry.getValue();
                
                // 处理每个HTTP方法
                processHttpMethod(result, parentId, baseUrl, path, "GET", pathItem.getGet());
                processHttpMethod(result, parentId, baseUrl, path, "POST", pathItem.getPost());
                processHttpMethod(result, parentId, baseUrl, path, "PUT", pathItem.getPut());
                processHttpMethod(result, parentId, baseUrl, path, "DELETE", pathItem.getDelete());
                processHttpMethod(result, parentId, baseUrl, path, "PATCH", pathItem.getPatch());
            }
            
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return result;
    }
    
    /**
     * 处理HTTP方法
     */
    private static void processHttpMethod(
            List<CreateAppDTO> result,
            String parentId,
            String baseUrl,
            String path,
            String method,
            Operation operation) {
        
        if (operation == null) {
            return;
        }
        
        String operationId = operation.getOperationId();
        String summary = operation.getSummary();
        String description = operation.getDescription();
        
        // 创建子插件请求
        CreateAppDTO request = new CreateAppDTO();
        request.setParentId(parentId);
        request.setName(StringUtils.hasText(summary) ? summary : (method + " " + path));
        request.setIntro(description);
        
        // 这里简化处理，实际应该根据OpenAPI规范解析参数和响应
        // 并构建相应的模块和边缘
        
        result.add(request);
    }
    
    /**
     * 解析OpenAPI Schema
     */
    private static OpenAPI parseOpenAPISchema(String schemaStr) {
        if (!StringUtils.hasText(schemaStr)) {
            return null;
        }
        
        try {
            // 尝试解析为JSON
            ObjectMapper objectMapper = new ObjectMapper();
            Object jsonObj = objectMapper.readValue(schemaStr, Object.class);
            SwaggerParseResult result = new OpenAPIParser().readContents(
                    objectMapper.writeValueAsString(jsonObj), null, null);
            return result.getOpenAPI();
        } catch (Exception jsonError) {
            try {
                // 尝试解析为YAML
                Yaml yaml = new Yaml();
                Object yamlObj = yaml.load(schemaStr);
                ObjectMapper objectMapper = new ObjectMapper();
                SwaggerParseResult result = new OpenAPIParser().readContents(
                        objectMapper.writeValueAsString(yamlObj), null, null);
                return result.getOpenAPI();
            } catch (Exception yamlError) {
                yamlError.printStackTrace();
                return null;
            }
        }
    }
    
    /**
     * 生成唯一ID
     */
    private static String generateId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 16);
    }
}