package com.sinitek.mind.core.app.entity;

import com.sinitek.mind.core.app.model.UserGuide;
import com.sinitek.mind.core.workflow.model.WorkflowTemplateBasicType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * 应用模板实体类
 * 对应MongoDB中的app_templates集合
 */
@Data
@Document(collection = "app_templates")
public class AppTemplate {
    
    @Id
    private String id;
    
    @Field("templateId")
    private String templateId;
    
    @Field("name")
    private String name;
    
    @Field("intro")
    private String intro;
    
    @Field("avatar")
    private String avatar;
    
    @Field("author")
    private String author;
    
    @Field("tags")
    private List<String> tags;
    
    @Field("type")
    private String type;
    
    @Field("isActive")
    private Boolean isActive;
    
    @Field("userGuide")
    private UserGuide userGuide;
    
    @Field("isQuickTemplate")
    private Boolean isQuickTemplate;
    
    @Field("order")
    private Integer order;
    
    @Field("workflow")
    private WorkflowTemplateBasicType workflow;

    private Integer weight;
}