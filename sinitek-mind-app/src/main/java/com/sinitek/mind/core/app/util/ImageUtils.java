package com.sinitek.mind.core.app.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 图片工具类
 */
@Slf4j
@Component
public class ImageUtils {

    @Value("${fe.domain:}")
    private static String feDomain;

    /**
     * 刷新资源头像
     * 
     * @param newAvatar 新头像URL
     * @param oldAvatar 旧头像URL
     * @param session MongoDB会话
     */
    public void refreshSourceAvatar(String newAvatar, String oldAvatar, Object session) {
        // 实际实现应该处理头像资源的更新和清理
        // TODO 这里简化处理，只打印日志
        if (newAvatar != null && !newAvatar.equals(oldAvatar)) {
            log.info("Refreshing avatar from {} to {}", oldAvatar, newAvatar);
        }
    }

    /**
     * 为图片URL添加端点域名
     * 对应TypeScript中的addEndpointToImageUrl函数
     *
     * @param text 包含图片链接的文本
     * @return 处理后的文本，图片链接已添加基础URL
     */
    public static String addEndpointToImageUrl(String text) {
        if (!StringUtils.hasText(feDomain) || !StringUtils.hasText(text)) {
            return text;
        }

        // 匹配 /api/system/img/xxx.xx 的图片链接，并追加 baseURL
        // 由于Java不支持可变长度的负向后查找，使用替代方案
        Pattern pattern = Pattern.compile("/api/system/img/[^\\s.]*\\.[^\\s]*");
        Matcher matcher = pattern.matcher(text);

        StringBuffer result = new StringBuffer();
        while (matcher.find()) {
            String match = matcher.group();
            // 检查匹配项前面是否已经有完整的URL协议
            int start = matcher.start();
            String prefix = "";
            if (start >= 8) {
                prefix = text.substring(Math.max(0, start - 8), start);
            }

            // 如果前面不包含http://或https://，则添加域名
            if (!prefix.contains("http://") && !prefix.contains("https://")) {
                matcher.appendReplacement(result, feDomain + match);
            } else {
                matcher.appendReplacement(result, match);
            }
        }
        matcher.appendTail(result);

        return result.toString();
    }
}