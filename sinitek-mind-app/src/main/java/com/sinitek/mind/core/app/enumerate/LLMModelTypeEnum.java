package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum LLMModelTypeEnum {

    ALL("all"),
    CLASSIFY("classify"),
    EXTRACT_FIELDS("extractFields"),
    TOOL_CALL("toolCall");

    private final String value;

    LLMModelTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static LLMModelTypeEnum fromValue(String value) {
        for (LLMModelTypeEnum type : LLMModelTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的大模型模型类型: " + value);
    }
}
