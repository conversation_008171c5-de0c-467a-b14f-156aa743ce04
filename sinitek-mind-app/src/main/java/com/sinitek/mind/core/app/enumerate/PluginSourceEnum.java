package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum PluginSourceEnum {

    PERSONAL("personal"),
    COMMUNITY("community"),
    COMMERCIAL("commercial");

    private final String value;

    PluginSourceEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static PluginSourceEnum fromValue(String value) {
        for (PluginSourceEnum type : PluginSourceEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的插件来源类型: " + value);
    }
}
