package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
public class ToolNodeItemType extends RuntimeNodeItemType {

    private List<FlowNodeInputItemType> toolParams;

    private JSONSchemaInputType jsonSchema;
}
