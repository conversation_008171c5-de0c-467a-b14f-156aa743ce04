package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

/**
 * 节点调度响应类型
 * 对应 TypeScript 的 DispatchNodeResponseType
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DispatchNodeResponseType {
    
    // ========== 通用字段 ==========
    private String moduleLogo;
    private double runningTime;
    private String query;
    private String textOutput;
    private Object error; // 可以是 Map<String, Object> 或 String
    private Map<String, Object> customInputs;
    private Map<String, Object> customOutputs;
    private Map<String, Object> nodeInputs;
    private Map<String, Object> nodeOutputs;
    private String mergeSignId;
    
    // ========== 计费相关 ==========
    @Deprecated
    private Integer tokens;
    private Integer inputTokens;
    private Integer outputTokens;
    private String model;
    private Integer contextTotalLen;
    private Double totalPoints;
    private Double childTotalPoints;
    
    // ========== 聊天相关 ==========
    private Double temperature;
    private Integer maxToken;
    private List<SearchDataResponseItemType> quoteList;
    private String reasoningText;
    private List<HistoryPreviewItem> historyPreview;
    private String finishReason; // CompletionFinishReason 枚举
    
    // ========== 数据集搜索相关 ==========
    private String embeddingModel;
    private Integer embeddingTokens;
    private Double similarity;
    private Integer limit;
    private String searchMode; // DatasetSearchModeEnum 枚举
    private Double embeddingWeight;
    private String rerankModel;
    private Double rerankWeight;
    private Integer reRankInputTokens;
    private Boolean searchUsingReRank;
    private QueryExtensionResult queryExtensionResult;
    private DeepSearchResult deepSearchResult;
    
    // ========== 数据集连接相关 ==========
    private Integer concatLength;
    
    // ========== 分类问题相关 ==========
    private List<ClassifyQuestionAgentItemType> cqList;
    private String cqResult;
    
    // ========== 内容提取相关 ==========
    private String extractDescription;
    private Map<String, Object> extractResult;
    
    // ========== HTTP 相关 ==========
    private Map<String, Object> params;
    private Object body; // 可以是 Map<String, Object> 或 String
    private Map<String, Object> headers;
    private Map<String, Object> httpResult;
    
    // ========== 插件输出相关 ==========
    private Map<String, Object> pluginOutput;
    private List<ChatHistoryItemResType> pluginDetail;
    
    // ========== 条件判断相关 ==========
    private String ifElseResult;
    
    // ========== 工具相关 ==========
    private Integer toolCallInputTokens;
    private Integer toolCallOutputTokens;
    private List<ChatHistoryItemResType> toolDetail;
    private Boolean toolStop;
    
    // ========== 代码相关 ==========
    private String codeLog;
    
    // ========== 文件读取相关 ==========
    private String readFilesResult;
    private List<ReadFileNodeResponse> readFiles;
    
    // ========== 用户选择相关 ==========
    private String userSelectResult;
    
    // ========== 变量更新相关 ==========
    private List<Object> updateVarResult;
    
    // ========== 循环相关 ==========
    private List<Object> loopResult;
    private List<Object> loopInput;
    private List<ChatHistoryItemResType> loopDetail;
    private Object loopInputValue;
    private Object loopOutputValue;
    
    // ========== 表单输入相关 ==========
    private String formInputResult;
    
    // ========== 工具参数相关 ==========
    private Map<String, Object> toolParamsResult;
    private Object toolRes;
    
    // ========== 废弃字段 ==========
    @Deprecated
    private String extensionModel;
    @Deprecated
    private String extensionResult;
    @Deprecated
    private Integer extensionTokens;
    
    // ========== 内部类定义 ==========
    
    /**
     * 历史预览项
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class HistoryPreviewItem {
        private String obj; // ChatRoleEnum 枚举值
        private String value;
    }
    
    /**
     * 查询扩展结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class QueryExtensionResult {
        private String model;
        private Integer inputTokens;
        private Integer outputTokens;
        private String query;
    }
    
    /**
     * 深度搜索结果
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class DeepSearchResult {
        private String model;
        private Integer inputTokens;
        private Integer outputTokens;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class ClassifyQuestionAgentItemType {
        private String key;
        private String value;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Accessors(chain = true)
    public static class ReadFileNodeResponse {
        private String url;
        private String name;
    }
}