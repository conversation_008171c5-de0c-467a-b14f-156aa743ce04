package com.sinitek.mind.core.ai.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文本内容部分
 * 对应 OpenAI SDK 中的文本内容类型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatCompletionContentPartText extends ChatCompletionContentPart {
    private String text;
    
    public ChatCompletionContentPartText() {
        super("text");
    }
    
    public ChatCompletionContentPartText(String text) {
        super("text");
        this.text = text;
    }

}