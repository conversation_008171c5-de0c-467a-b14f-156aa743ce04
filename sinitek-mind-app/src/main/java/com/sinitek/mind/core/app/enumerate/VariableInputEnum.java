package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum VariableInputEnum {

    INPUT("input"),
    TEXTAREA("textarea"),
    NUMBER_INPUT("numberInput"),
    SELECT("select"),
    CUSTOM("custom");

    private final String value;

    VariableInputEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static VariableInputEnum fromValue(String value) {
        for (VariableInputEnum type : VariableInputEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的变量输入类型: " + value);
    }
}
