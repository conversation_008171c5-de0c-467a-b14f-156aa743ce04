package com.sinitek.mind.core.workflow.enumerate;

import lombok.Getter;

// 内容类型枚举
@Getter
public enum ContentTypes {
    NONE("none", ""),
    FORM_DATA("formData", ""),
    X_WWW_FORM_URLENCODED("xWwwFormUrlencoded", "application/x-www-form-urlencoded"),
    JSON("json", "application/json"),
    XML("xml", "application/xml"),
    RAW("raw", "text/plain");

    private final String value;
    private final String mimeType;

    ContentTypes(String value, String mimeType) {
        this.value = value;
        this.mimeType = mimeType;
    }

    public static ContentTypes fromValue(String value) {
        for (ContentTypes type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return JSON; // 默认值
    }
}