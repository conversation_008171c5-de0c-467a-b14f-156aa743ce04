package com.sinitek.mind.core.workflow.model;

import lombok.Getter;

import java.util.List;

/**
 * 边分类结果类
 */
@Getter
public class EdgeSplitResult {
    private final List<RuntimeEdgeItemType> commonEdges;
    private final List<RuntimeEdgeItemType> recursiveEdges;
    
    public EdgeSplitResult(List<RuntimeEdgeItemType> commonEdges, List<RuntimeEdgeItemType> recursiveEdges) {
        this.commonEdges = commonEdges;
        this.recursiveEdges = recursiveEdges;
    }

}