package com.sinitek.mind.core.workflow.enumerate;

import lombok.Getter;

@Getter
public enum SseResponseEventEnum {
    ERROR("error"),
    WORKFLOW_DURATION("workflowDuration"),
    ANSWER("answer"),
    FAST_ANSWER("fastAnswer"),
    FLOW_NODE_STATUS("flowNodeStatus"),
    FLOW_NODE_RESPONSE("flowNodeResponse"),
    TOOL_CALL("toolCall"),
    TOOL_PARAMS("toolParams"),
    TOOL_RESPONSE("toolResponse"),
    FLOW_RESPONSE("flowResponse"),
    UPDATE_VARIABLES("updateVariables"),
    INTERACTIVE("interactive");
    private final String value;

    SseResponseEventEnum(String value) {
        this.value = value;
    }
}
