package com.sinitek.mind.core.app.enumerate;

import lombok.Getter;

@Getter
public enum WorkflowIOValueTypeEnum {
    STRING("string"),
    NUMBER("number"),
    BOOLEAN("boolean"),
    OBJECT("object"),

    ARRAY_STRING("arrayString"),
    ARRAY_NUMBER("arrayNumber"),
    ARRAY_BOOLEAN("arrayBoolean"),
    ARRAY_OBJECT("arrayObject"),
    ARRAY_ANY("arrayAny"),
    ANY("any"),

    CHAT_HISTORY("chatHistory"),
    DATASET_QUOTE("datasetQuote"),

    DYNAMIC("dynamic"),

    // plugin special type
    SELECT_DATASET("selectDataset"),

    // abandon
    SELECT_APP("selectApp");

    private final String value;

    WorkflowIOValueTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static WorkflowIOValueTypeEnum fromValue(String value) {
        for (WorkflowIOValueTypeEnum type : WorkflowIOValueTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的工作流IO值类型: " + value);
    }
}