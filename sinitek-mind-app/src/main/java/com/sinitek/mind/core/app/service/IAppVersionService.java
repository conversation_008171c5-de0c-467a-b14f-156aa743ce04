package com.sinitek.mind.core.app.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.VersionListItemType;

public interface IAppVersionService {

    AppVersionDTO getAppVersionById(String appId, String versionId, App app);

    AppVersionDTO getLatestAppVersion(String appId, App app);

    boolean checkIsLatestVersion(String appId, String versionId);

    PageResult<VersionListItemType> getVersionList(VersionListRequest request);

    VersionDetailResponse getVersionDetail(VersionDetailRequest versionDetailRequest, AuthAppDTO authAppDTO);

    LatestVersionResDTO getAppLatestVersion(String appId, AuthAppDTO authAppDTO);

    void updateAppVersion(String versionId, String versionName);

    PublishAppResponseDTO publishApp(String appId, PublishAppDTO request, AuthAppDTO authAppDTO);

}
