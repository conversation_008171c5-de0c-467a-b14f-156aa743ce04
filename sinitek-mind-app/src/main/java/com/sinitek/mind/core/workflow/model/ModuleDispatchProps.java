package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.List;
import java.util.Map;

/**
 * 模块调度属性
 * 用于节点执行时的参数传递
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@SuperBuilder
public class ModuleDispatchProps extends ChatDispatchProps {
    
    /**
     * SSE响应对象
     */
    private SseEmitter res;
    
    /**
     * 变量映射
     */
    private Map<String, Object> variables;
    
    /**
     * 聊天历史记录
     */
    private List<ChatItemType> histories;
    
    /**
     * 时区
     */
    private String timezone;
    
    /**
     * 外部提供者配置
     */
    private ExternalProviderType externalProvider;
    
    /**
     * 是否流式响应
     */
    private Boolean stream;
    
    /**
     * 是否保留数据集引用
     */
    private Boolean retainDatasetCite;
    
    /**
     * 当前执行的节点
     */
    private RuntimeNodeItemType node;
    
    /**
     * 运行时节点列表
     */
    private List<RuntimeNodeItemType> runtimeNodes;
    
    /**
     * 运行时边列表
     */
    private List<RuntimeEdgeItemType> runtimeEdges;
    
    /**
     * 节点执行参数
     */
    private Map<String, Object> params;
    
    /**
     * 执行模式（test/production等）
     */
    private String mode;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 团队ID
     */
    private String teamId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 聊天ID
     */
    private String chatId;
    
    /**
     * 响应ID
     */
    private String responseId;
    
    /**
     * 是否调试模式
     */
    private Boolean debug;
    
    /**
     * 最大令牌数
     */
    private Integer maxTokens;
    
    /**
     * 温度参数
     */
    private Double temperature;
    
    /**
     * 系统提示
     */
    private String systemPrompt;
    
    /**
     * 用户输入
     */
    private String userInput;
    
    /**
     * 文件列表
     */
    private List<String> files;
    
    /**
     * 工具列表
     */
    private List<Object> tools;
    
    /**
     * 插件配置
     */
    private Map<String, Object> pluginConfig;
    
    /**
     * HTTP配置
     */
    private HttpConfig httpConfig;

    /**
     * 查询参数
     */
    private List<ChatItemValueItemType> query;
    
    /**
     * 数据库配置
     */
    private DatabaseConfig databaseConfig;

    /**
     * HTTP配置内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class HttpConfig {
        /**
         * 请求URL
         */
        private String url;
        
        /**
         * 请求方法
         */
        private String method;
        
        /**
         * 请求头
         */
        private Map<String, String> headers;
        
        /**
         * 请求体
         */
        private Object body;
        
        /**
         * 超时时间（毫秒）
         */
        private Integer timeout;
        
        /**
         * 是否跟随重定向
         */
        private Boolean followRedirects;
        
        /**
         * 认证信息
         */
        private AuthConfig auth;
    }
    
    /**
     * 认证配置内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthConfig {
        /**
         * 认证类型（basic/bearer/api-key等）
         */
        private String type;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码
         */
        private String password;
        
        /**
         * 令牌
         */
        private String token;
        
        /**
         * API密钥
         */
        private String apiKey;
    }
    
    /**
     * 数据库配置内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DatabaseConfig {
        /**
         * 数据库类型
         */
        private String type;
        
        /**
         * 连接字符串
         */
        private String connectionString;
        
        /**
         * 用户名
         */
        private String username;
        
        /**
         * 密码
         */
        private String password;
        
        /**
         * 数据库名
         */
        private String database;
        
        /**
         * 表名
         */
        private String table;
        
        /**
         * SQL查询
         */
        private String query;
        
        /**
         * 连接池配置
         */
        private Map<String, Object> poolConfig;
    }
}