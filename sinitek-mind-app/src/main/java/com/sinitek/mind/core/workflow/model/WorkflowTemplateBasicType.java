package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowTemplateBasicType {

    public List<StoreNodeItemType> nodes;

    public List<StoreEdgeItemType> edges;

    public AppChatConfigType chatConfig;
}
