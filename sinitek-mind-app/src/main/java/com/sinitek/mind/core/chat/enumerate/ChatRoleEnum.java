package com.sinitek.mind.core.chat.enumerate;

import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.Getter;

@Getter
public enum ChatRoleEnum {

    SYSTEM("System", "系统"),
    HUMAN("Human", "用户"),
    AI("AI", "AI");

    private final String name;
    private final String value;

    ChatRoleEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ChatRoleEnum getByValue(String value) {
        for (ChatRoleEnum chatRoleEnum : ChatRoleEnum.values()) {
            if (chatRoleEnum.getValue().equals(value)) {
                return chatRoleEnum;
            }
        }
        throw new BussinessException("角色未找到");
    }

}
