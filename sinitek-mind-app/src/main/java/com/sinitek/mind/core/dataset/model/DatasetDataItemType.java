package com.sinitek.mind.core.dataset.model;

import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class DatasetDataItemType {

    private String id;

    private String teamId;

    private String datasetId;

    private String imagePreviewUrl;

    private Date updateTime;

    private String collectionId;

    private String sourceName;

    private String sourceId;

    private Integer chunkIndex;

    private List<DatasetDataIndexItemType> indexes;

    private boolean isOwner;
}
