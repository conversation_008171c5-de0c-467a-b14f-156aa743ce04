package com.sinitek.mind.core.app.util;

import io.swagger.parser.OpenAPIParser;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.parser.core.models.ParseOptions;

public class SwaggerUtils {
    
    public static OpenAPI loadOpenAPISchemaFromUrl(String url) throws Exception {
        ParseOptions options = new ParseOptions();
        options.setResolve(true);
        options.setResolveFully(true);
        
        return new OpenAPIParser().readLocation(url, null, options).getOpenAPI();
    }
    
    public static boolean checkOpenAPISchemaValid(String jsonStr) {
        try {
            ParseOptions options = new ParseOptions();
            options.setResolve(true);
            
            OpenAPI result = new OpenAPIParser().readContents(jsonStr, null, options).getOpenAPI();
            return result != null;
        } catch (Exception e) {
            return false;
        }
    }
}