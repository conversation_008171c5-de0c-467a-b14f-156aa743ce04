package com.sinitek.mind.core.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "初始化聊天请求")
public class InitChatRequest {

    @Schema(description = "应用ID", example = "64f1234567890abcdef12345")
    private String appId;

    @Schema(description = "聊天ID，可选", example = "64f1234567890abcdef12346")
    private String chatId;

    @Schema(description = "是否加载自定义反馈", example = "false")
    private Boolean loadCustomFeedbacks = false;
}