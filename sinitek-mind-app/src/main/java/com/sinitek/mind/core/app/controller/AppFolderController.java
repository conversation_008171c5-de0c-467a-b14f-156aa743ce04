package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.dto.CreateAppDTO;
import com.sinitek.mind.core.app.dto.GetPathDTO;
import com.sinitek.mind.core.app.model.ParentTreePathItemType;
import com.sinitek.mind.core.app.service.IAppFolderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/folder")
@RequiredArgsConstructor
@Tag(name = "文件夹管理", description = "应用文件夹相关接口")
public class AppFolderController {

    private final IAppFolderService  appFolderService;

    @PostMapping("/create")
    @Operation(summary = "创建文件夹")
    public ApiResponse<Void> createFolder(@RequestBody CreateAppDTO body) {
        appFolderService.createFolder(body);
        return ApiResponse.success(null);
    }


    @GetMapping("/path")
    @Operation(summary = "从根到当前 app 的完整路径")
    public ApiResponse<List<ParentTreePathItemType>> folderPath(GetPathDTO props) throws Exception {
        List<ParentTreePathItemType> pathList = appFolderService.folderPath(props);
        return ApiResponse.success(pathList);
    }
}
