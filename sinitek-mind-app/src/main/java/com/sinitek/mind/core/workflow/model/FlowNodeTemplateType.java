package com.sinitek.mind.core.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper=true)
public class FlowNodeTemplateType extends FlowNodeCommonType{
    private String id;
    private String templateType;

    private HandleType sourceHandle;
    private HandleType targetHandle;

    private Boolean isTool;

    private Boolean forbidDelete;
    private Boolean unique;

    private String diagram;
    private String courseUrl;
    private String userGuide;
}
