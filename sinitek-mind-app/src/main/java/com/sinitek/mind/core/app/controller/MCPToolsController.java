package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.dto.CreateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.CreateMCPToolsResDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsDTO;
import com.sinitek.mind.core.app.dto.UpdateMCPToolsResDTO;
import com.sinitek.mind.core.app.service.IMCPToolsService;
import com.sinitek.mind.support.permission.service.IAuthService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * MCP工具控制器
 * 对应原始的 create.ts 和 update.ts 接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/mcpTools")
@RequiredArgsConstructor
public class MCPToolsController {
    
    private final IMCPToolsService mcpToolsService;
    private final IAuthService authService;
    
    /**
     * 创建MCP工具集
     * POST /api/core/app/mcpTools/create
     * 对应原始的 create.ts
     */
    @PostMapping("/create")
    public ApiResponse<CreateMCPToolsResDTO> createMCPTools(
            @Valid @RequestBody CreateMCPToolsDTO request,
            @RequestHeader(value = "Authorization", required = false) String authToken,
            BindingResult bindingResult) {
        
        try {
            log.info("接收到创建MCP工具集请求: {}", request.getName());
            
            // 参数验证
            if (bindingResult.hasErrors()) {
                return ApiResponse.error("参数验证失败");
            }
            
            // 验证认证token
//            if (authToken == null || !authService.isValidToken(authToken)) {
//                return ResponseEntity.status(HttpStatus.UNAUTHORIZED)
//                    .body(Map.of("error", "认证失败，请提供有效的Authorization token"));
//            }
            
            CreateMCPToolsResDTO response = mcpToolsService.createMCPTools(request, authToken);
            
            log.info("MCP工具集创建成功: {}", response.getMcpToolsId());
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("创建MCP工具集系统异常: {}", e.getMessage(), e);
            return ApiResponse.error("服务器异常");
        }
    }
    
    /**
     * 更新MCP工具集
     * POST /api/core/app/mcpTools/update
     * 对应原始的 update.ts
     */
    @PostMapping("/update")
    public ApiResponse<UpdateMCPToolsResDTO> updateMCPTools(
            @Valid @RequestBody UpdateMCPToolsDTO request,
            @RequestHeader(value = "Authorization", required = false) String authToken,
            BindingResult bindingResult) {
        
        try {
            log.info("接收到更新MCP工具集请求: {}", request.getAppId());
            
            // 参数验证
            if (bindingResult.hasErrors()) {
                return ApiResponse.error("参数验证失败");
            }
            
            // 验证认证token
//            if (authToken == null || !authService.isValidToken(authToken)) {
//                return return ApiResponse.error("参数验证失败");
//            }
            
            UpdateMCPToolsResDTO response = mcpToolsService.updateMCPTools(request, authToken);
            
            log.info("MCP工具集更新成功: {}", response.getAppId());
            return ApiResponse.success(response);
            
        } catch (Exception e) {
            log.error("更新MCP工具集系统异常: {}", e.getMessage(), e);
            return ApiResponse.error("服务器异常");
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, String>> health() {
        return ResponseEntity.ok(Map.of(
            "status", "UP",
            "service", "MCP Tools Service",
            "timestamp", java.time.LocalDateTime.now().toString()
        ));
    }
}