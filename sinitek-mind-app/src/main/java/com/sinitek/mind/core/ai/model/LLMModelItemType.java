package com.sinitek.mind.core.ai.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class LLMModelItemType extends BaseModelItemType {

    private double charsPointPrice;

    private double inputPrice;

    private double outputPrice;

    private final String type = "llm";

    private Integer maxContext;

    private Integer maxResponse;

    private Integer quoteMaxToken;

    private boolean showTopP;

    private List<String> responseFormatList;

    private boolean showStopSign;
    private boolean censor;
    private boolean vision;
    private boolean reasoning;
    private boolean datasetProcess;
    private boolean usedInClassify;
    private boolean usedInExtractFields;
    private boolean usedInToolCall;
    private boolean functionCall;
    private boolean toolChoice;
    private String defaultSystemChatPrompt;

    private Map<String, Object> defaultConfig;

    private Map<String, String> fieldMap;


}
