package com.sinitek.mind.core.app.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;

@Data
@Document(collection = "images")
public class Image {

    @Id
    private String id;

    @Field(targetType = FieldType.OBJECT_ID)
    private String teamId;

    private Date createTime = new Date();

    private Date expiredTime;

    private byte[] binary;

    private Object metadata;
}
