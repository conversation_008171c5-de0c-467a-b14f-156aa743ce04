package com.sinitek.mind.core.dataset.util;

import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.dataset.model.SearchDataWithRrfScore;
import com.sinitek.mind.core.dataset.model.SearchResultItem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据集工具类
 */
@Slf4j
public class DatasetUtil {

    /**
     * 数据集搜索结果合并（使用RRF算法）
     *
     * @param searchResults 搜索结果列表，每个元素包含k值和结果列表
     * @return 合并后的搜索结果列表
     */
    public static List<SearchDataResponseItemType> datasetSearchResultConcat(
            List<SearchResultItem> searchResults) {

        // 过滤掉空结果
        List<SearchResultItem> filteredResults = searchResults.stream()
                .filter(item -> item.getList() != null && !item.getList().isEmpty())
                .toList();

        if (filteredResults.isEmpty()) {
            return new ArrayList<>();
        }

        if (filteredResults.size() == 1) {
            return filteredResults.get(0).getList();
        }

        // 使用Map存储合并结果，key为数据ID
        Map<String, SearchDataWithRrfScore> resultMap = new HashMap<>();

        // RRF算法实现
        for (SearchResultItem searchResult : filteredResults) {
            int k = searchResult.getK();
            List<SearchDataResponseItemType> dataList = searchResult.getList();

            for (int index = 0; index < dataList.size(); index++) {
                SearchDataResponseItemType data = dataList.get(index);
                int rank = index + 1;
                double score = 1.0 / (k + rank);

                SearchDataWithRrfScore existingRecord = resultMap.get(data.getId());
                if (existingRecord != null) {
                    // 合并分数，相同类型的分数取最大值
                    List<SearchDataResponseItemType.SearchScore> mergedScores = mergeScores(
                            existingRecord.getScore(), data.getScore());

                    existingRecord.setScore(mergedScores);
                    existingRecord.setRrfScore(existingRecord.getRrfScore() + score);
                    resultMap.put(data.getId(), existingRecord);
                } else {
                    // 创建新记录
                    SearchDataWithRrfScore newRecord = new SearchDataWithRrfScore();
                    BeanUtils.copyProperties(data, newRecord);
                    newRecord.setRrfScore(score);
                    resultMap.put(data.getId(), newRecord);
                }
            }
        }

        // 按RRF分数降序排序并转换为结果列表
        return resultMap.values().stream()
                .sorted((a, b) -> Double.compare(b.getRrfScore(), a.getRrfScore()))
                .map(item -> {
                    SearchDataResponseItemType result = new SearchDataResponseItemType();
                    BeanUtils.copyProperties(item, result);
                    return result;
                })
                .toList();
    }

    /**
     * 合并分数列表，相同类型的分数取最大值
     */
    private static List<SearchDataResponseItemType.SearchScore> mergeScores(
            List<SearchDataResponseItemType.SearchScore> existingScores,
            List<SearchDataResponseItemType.SearchScore> newScores) {

        List<SearchDataResponseItemType.SearchScore> mergedScores = new ArrayList<>(existingScores);

        for (SearchDataResponseItemType.SearchScore newScore : newScores) {
            SearchDataResponseItemType.SearchScore existingScore = mergedScores.stream()
                    .filter(score -> score.getType().equals(newScore.getType()))
                    .findFirst()
                    .orElse(null);

            if (existingScore != null) {
                // 相同类型的分数取最大值
                existingScore.setValue(Math.max(existingScore.getValue(), newScore.getValue()));
            } else {
                // 添加新的分数类型
                mergedScores.add(newScore);
            }
        }

        return mergedScores;
    }
}
