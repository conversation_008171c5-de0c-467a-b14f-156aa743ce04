package com.sinitek.mind.core.app.util;

import org.bson.Document;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperationContext;

/**
 * 自定义聚合操作类，用于支持原生MongoDB聚合表达式
 */
public class CustomAggregationOperation implements AggregationOperation {
    private final Document operation;

    public CustomAggregationOperation(Document operation) {
        this.operation = operation;
    }


    @Override
    public Document toDocument(AggregationOperationContext context) {
        return operation;
    }

    @Override
    public String getOperator() {
        return operation.keySet().iterator().next();
    }
}