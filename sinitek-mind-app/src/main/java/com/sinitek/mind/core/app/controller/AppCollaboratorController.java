package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.core.app.dto.AppChangeOwnerDTO;
import com.sinitek.mind.core.app.dto.AppClbDeleteDTO;
import com.sinitek.mind.core.app.dto.AppClbUpdateDTO;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.support.permission.dto.CollaboratorDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.service.ICollaboratorService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;

/**
 * App协作者Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@RestController
@RequestMapping("/mind/api/core/app/collaborator")
@Tag(name = "App协作者管理")
@RequiredArgsConstructor
public class AppCollaboratorController {

    @Autowired
    private ICollaboratorService collaboratorService;

    @Autowired
    private AppRepository appRepository;

    @GetMapping("/list")
    @Operation(summary = "获取App协作者列表")
    public ApiResponse<List<CollaboratorDTO>> getCollaboratorList(@RequestParam String appId) {
        return ApiResponse.success(collaboratorService.findClb(appId, ResourceTypeEnum.APP));
    }

    @PostMapping("/update")
    @Operation(summary = "更新App协作者")
    public ApiResponse<Void> updateCollaborator(@RequestBody AppClbUpdateDTO updateDTO) {

        List<String> orgIdList = new LinkedList<>();
        orgIdList.addAll(updateDTO.getOrgs());
        orgIdList.addAll(updateDTO.getMembers());
        orgIdList.addAll(updateDTO.getGroups());
        collaboratorService.updateClb(updateDTO.getAppId(), ResourceTypeEnum.APP, orgIdList, updateDTO.getPermission());
        return ApiResponse.success();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除App协作者")
    public ApiResponse<Void> updateCollaborator(AppClbDeleteDTO deleteDTO) {

        String orgId = "";
        if (StringUtils.isNotBlank(deleteDTO.getTmbId())) {
            orgId = deleteDTO.getTmbId();
        } else if (StringUtils.isNotBlank(deleteDTO.getGroupId())) {
            orgId = deleteDTO.getGroupId();
        } else if (StringUtils.isNotBlank(deleteDTO.getOrgId())) {
            orgId = deleteDTO.getOrgId();
        }

        collaboratorService.deleteClb(deleteDTO.getAppId(), ResourceTypeEnum.APP, orgId);
        return ApiResponse.success();
    }

    @PostMapping("/changeOwner")
    @Operation(summary = "更新App拥有者")
    public ApiResponse<Void> changeOwner(@RequestBody AppChangeOwnerDTO ownerDTO) {
        if (StringUtils.isBlank(ownerDTO.getAppId()) || StringUtils.isBlank(ownerDTO.getOwnerId())) {
            return ApiResponse.error("appId或ownerId不能为空");
        }
        appRepository.updateOwnerIdById(ownerDTO.getAppId(), ownerDTO.getOwnerId());
        return ApiResponse.success();
    }

}
