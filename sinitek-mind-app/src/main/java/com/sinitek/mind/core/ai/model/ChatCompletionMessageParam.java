package com.sinitek.mind.core.ai.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.model.WorkflowInteractiveResponseType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 聊天完成消息参数基类
 * 对应 TypeScript 中的 ChatCompletionMessageParam
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ChatCompletionMessageParam {
    @Schema(description = "消息角色")
    private String role;

    @Schema(description = "消息内容")
    private Object content;

    @Schema(description = "数据ID")
    private String dataId;

    @Schema(description = "是否在UI中隐藏")
    private Boolean hideInUI;

    @Schema(description = "推理文本")
    @JsonProperty("reasoning_text")
    private String reasoningText;

    @Schema(description = "工具调用ID")
    private String toolCallId;

    @Schema(description = "工具名称")
    private String name;

    @Schema(description = "工具调用列表")
    @JsonProperty("tool_calls")
    private List<ChatCompletionMessageToolCall> toolCalls;

    @Schema(description = "函数调用")
    @JsonProperty("function_calls")
    private ChatCompletionMessageFunctionCall functionCall;

    @Schema(description = "交互响应")
    private WorkflowInteractiveResponseType interactive;

    @Schema(description = "value")
    private List<ChatItemValueItemType> value;

}