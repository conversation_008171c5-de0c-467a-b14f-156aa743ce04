package com.sinitek.mind.core.app.mcp;

import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.SecretValueType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 对应McpToolDataType
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class McpToolDataType extends McpToolConfig {

    private String url;

    private Map<String, SecretValueType> headerSecret;
}
