package com.sinitek.mind.core.app.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.core.app.dto.*;
import com.sinitek.mind.core.app.model.VersionListItemType;
import com.sinitek.mind.core.app.service.IAppVersionService;
import com.sinitek.mind.core.app.service.IAuthAppService;
import com.sinitek.mind.support.operationlog.enumerate.OperationLogEventEnum;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.framework.exception.BussinessException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 应用版本控制器
 * 对应原始的 /api/core/app/version/list 接口
 */
@Slf4j
@RestController
@RequestMapping("/mind/api/core/app/version")
@RequiredArgsConstructor
@Tag(name = "应用版本管理", description = "应用版本相关接口")
public class AppVersionController {
    
    private final IAppVersionService appVersionService;
    
    private final IPermissionService permissionService;

    private final IOperationLogService operationLogService;

    private final IAuthAppService authAppService;

    /**
     * 获取应用版本列表
     *
     * @param request 版本列表请求参数
     * @return 版本列表分页数据
     */
    @PostMapping("/list")
    @Operation(summary = "获取应用版本列表")
    public ApiResponse<PageResult<VersionListItemType>> getVersionList(
            @Valid @RequestBody VersionListRequest request) {
        
        try {
            // 参数验证
            if (request.getAppId() == null || request.getAppId().trim().isEmpty()) {
                throw new IllegalArgumentException("缺少参数: appId");
            }
            
            // 处理分页参数
            processPaginationParams(request);
            
            // 应用权限验证 - 需要写权限和token认证
            authAppService.authApp(request.getAppId(), PermissionConstant.READ_PER);
            
            // 获取版本列表
            PageResult<VersionListItemType> response = appVersionService.getVersionList(request);
            
            return ApiResponse.success(response);
            
        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("获取版本列表失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取应用版本详情
     *
     * @param versionDetailRequest 版本详情请求参数
     * @return 版本详情数据
     */
    @PostMapping("/detail")
    public ApiResponse<VersionDetailResponse> getVersionDetail(@Valid @RequestBody VersionDetailRequest versionDetailRequest) {

        try {
            // 参数验证
            if (versionDetailRequest.getVersionId() == null || versionDetailRequest.getVersionId().trim().isEmpty()) {
                throw new IllegalArgumentException("缺少参数: versionId");
            }
            if (versionDetailRequest.getAppId() == null || versionDetailRequest.getAppId().trim().isEmpty()) {
                throw new IllegalArgumentException("缺少参数: appId");
            }

            // 应用权限验证 - 需要写权限和token认证
            AuthAppDTO authAppDTO = authAppService.authApp(versionDetailRequest.getAppId(), PermissionConstant.READ_PER);

            // 获取版本详情
            VersionDetailResponse response = appVersionService.getVersionDetail(
                    versionDetailRequest, authAppDTO);

            return ApiResponse.success(response);

        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("获取版本详情失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取应用最新版本
     * 对应原始的 /api/core/app/version/latest 接口
     *
     * @param appId 应用ID
     * @return 最新版本数据
     */
    @GetMapping("/latest")
    @Operation(summary = "获取应用最新版本")
    public ApiResponse<LatestVersionResDTO> getLatestVersion(@RequestParam("appId") String appId) {
        try {
            // 参数验证
            if (appId == null || appId.trim().isEmpty()) {
                throw new IllegalArgumentException("缺少参数: appId");
            }

            // 应用权限验证 - 需要写权限和token认证
            AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.WRITE_PER);

            // 获取最新版本
            LatestVersionResDTO response = appVersionService.getAppLatestVersion(appId, authAppDTO);

            return ApiResponse.success(response);

        } catch (IllegalArgumentException e) {
            throw new RuntimeException(e.getMessage(), e);
        } catch (Exception e) {
            throw new RuntimeException("获取最新版本失败: " + e.getMessage(), e);
        }
    }

    /**
     * 更新应用版本
     *
     * @param request 更新版本请求参数
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新应用版本", description = "更新应用版本名称")
    public ApiResponse<Void> updateAppVersion(@RequestBody UpdateAppVersionDTO request) {
        try {
            // 参数验证
            if (request.getAppId() == null || request.getAppId().trim().isEmpty()) {
                throw new BussinessException("缺少参数: appId");
            }
            if (request.getVersionId() == null || request.getVersionId().trim().isEmpty()) {
                throw new BussinessException("缺少参数: versionId");
            }
            if (request.getVersionName() == null || request.getVersionName().trim().isEmpty()) {
                throw new BussinessException("缺少参数: versionName");
            }

            // 应用权限验证 - 需要写权限和token认证
            authAppService.authApp(request.getAppId(), PermissionConstant.WRITE_PER);

            // 更新版本名称
            appVersionService.updateAppVersion(request.getVersionId(), request.getVersionName());

            return ApiResponse.success();

        } catch (Exception e) {
            log.error("更新应用版本失败: {}", e.getMessage(), e);
            throw new BussinessException("更新应用版本失败: " + e.getMessage());
        }
    }

    /**
     * 发布应用版本
     * 对应原始的 /api/core/app/version/publish 接口
     *
     * @param appId 应用ID
     * @param request 发布请求参数
     * @return 发布结果
     */
    @PostMapping("/publish")
    @Operation(summary = "发布应用版本", description = "发布或保存应用版本")
    public ApiResponse<PublishAppResponseDTO> publishApp(
            @RequestParam("appId") String appId,
            @RequestBody PublishAppDTO request) {
        try {
            // 参数验证
            if (appId == null || appId.trim().isEmpty()) {
                throw new BussinessException("缺少参数: appId");
            }
            if (request.getNodes() == null) {
                throw new BussinessException("缺少参数: nodes");
            }
            if (request.getEdges() == null) {
                throw new BussinessException("缺少参数: edges");
            }
            if (request.getChatConfig() == null) {
                throw new BussinessException("缺少参数: chatConfig");
            }

            // 应用权限验证 - 需要写权限和token认证
            AuthAppDTO authAppDTO = authAppService.authApp(appId, PermissionConstant.WRITE_PER);

            // 调用服务层发布应用
            PublishAppResponseDTO response = appVersionService.publishApp(appId, request, authAppDTO);

            // 记录操作日志
            Map<String, String> logParams = new HashMap<>();
            logParams.put("appName", authAppDTO.getApp().getName());
            logParams.put("operationName", request.getIsPublish() != null && request.getIsPublish()
                    ? "保存并发布" : "更新");
            logParams.put("appId", appId);
            logParams.put("appType", authAppDTO.getApp().getType());

            operationLogService.addOperationLog(OperationLogEventEnum.UPDATE_PUBLISH_APP, logParams);

            return ApiResponse.success(response);

        } catch (Exception e) {
            log.error("发布应用版本失败: {}", e.getMessage(), e);
            throw new BussinessException("发布应用版本失败: " + e.getMessage());
        }
    }
    
    /**
     * 处理分页参数
     * 对应原始的parsePaginationRequest函数
     */
    private void processPaginationParams(VersionListRequest request) {
        // 如果提供了pageNum，则计算offset
//        if (request.getPageNum() != null && request.getPageNum() > 0) {
//            int pageSize = request.getPageSize() != null ? request.getPageSize() : 20;
//            request.setOffset((request.getPageNum() - 1) * pageSize);
//        }
//
        // 设置默认值
        if (request.getPageSize() == null) {
            request.setPageSize(20);
        }
        if (request.getOffset() == null) {
            request.setOffset(0);
        }
    }
}