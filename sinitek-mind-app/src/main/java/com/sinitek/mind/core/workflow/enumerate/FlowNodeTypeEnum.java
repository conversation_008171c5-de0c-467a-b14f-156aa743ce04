package com.sinitek.mind.core.workflow.enumerate;

import lombok.Getter;

@Getter
public enum FlowNodeTypeEnum {
    EMPTY_NODE("emptyNode"),
    SYSTEM_CONFIG("userGuide"),
    P<PERSON>UGIN_CONFIG("pluginConfig"),
    GLOBAL_VARIABLE("globalVariable"),
    WORKFLOW_START("workflowStart"),
    CHAT_NODE("chatNode"),
    DATASET_SEARCH_NODE("datasetSearchNode"),
    DATASET_CONCAT_NODE("datasetConcatNode"),
    ANSWER_NODE("answerNode"),
    CLASSIFY_QUESTION("classifyQuestion"),
    CONTENT_EXTRACT("contentExtract"),
    HTTP_REQUEST_468("httpRequest468"),
    RUN_APP("app"),
    APP_MODULE("appModule"),
    PLUGIN_MODULE("pluginModule"),
    PLUGIN_INPUT("pluginInput"),
    PLUGIN_OUTPUT("pluginOutput"),
    QUERY_EXTENSION("cfr"),
    TOOLS("tools"),
    STOP_TOOL("stopTool"),
    TOOL_PARAMS("toolParams"),
    LAF_MODULE("lafModule"),
    IF_ELSE_NODE("ifElseNode"),
    VARIABLE_UPDATE("variableUpdate"),
    CODE("code"),
    TEXT_EDITOR("textEditor"),
    CUSTOM_FEEDBACK("customFeedback"),
    READ_FILES("readFiles"),
    USER_SELECT("userSelect"),
    LOOP("loop"),
    LOOP_START("loopStart"),
    LOOP_END("loopEnd"),
    FORM_INPUT("formInput"),
    COMMENT("comment"),
    TOOL("tool"),
    TOOL_SET("toolSet");

    private final String value;

    FlowNodeTypeEnum(String value) {
        this.value = value;
    }

    // 根据字符串值查找枚举
    public static FlowNodeTypeEnum fromValue(String value) {
        for (FlowNodeTypeEnum type : FlowNodeTypeEnum.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的节点类型: " + value);
    }
}