package com.sinitek.mind.core.chat.entity;

import com.sinitek.mind.core.app.model.VariableItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "chats")
public class Chat {

    @Id
    private String id;

    private String chatId;

    private String userId;

    private String teamId;

    private String tmbId;

    @Field(targetType = FieldType.OBJECT_ID)
    private String appId;

    private Date createTime;

    private Date updateTime;

    private String title;

    private String customTitle;

    private boolean top;

    private String source; // ChatSourceEnum

    private String sourceName;

    private String shareId;

    private String outLinkUid;

    private List<VariableItemType> variableList;

    private String welcomeText;

    private Map<String, Object> variables;

    private List<FlowNodeInputItemType> pluginInputs;

    private Map<String, Object> metadata;

}
