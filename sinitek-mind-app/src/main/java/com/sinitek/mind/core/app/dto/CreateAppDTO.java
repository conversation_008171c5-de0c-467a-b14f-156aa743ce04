package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.model.PluginData;
import com.sinitek.mind.core.app.model.ShortUrlParams;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.Data;

import java.util.List;

/**
 * 创建app入参对象；对应type CreateAppBody
 */
@Data
public class CreateAppDTO {

    private String parentId;

    private String name;

    private String avatar;

    private String type; // AppTypeEnum

    private List<StoreNodeItemType> modules;

    private List<StoreEdgeItemType> edges;

    private AppChatConfigType chatConfig;

    private ShortUrlParams utmParams;

    private String intro;

    private String teamId;

    private String tmbId;

    private PluginData pluginData;

    private String username;

    private String userAvatar;

}
