package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.chat.model.NodeOutputItemType;
import com.sinitek.mind.core.workflow.model.sse.SseResponseType;
import lombok.Data;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * 工作流的流响应，使用sse发送至前端
 */
@Data
@SuperBuilder
public class WorkflowInteractiveResponseType implements SseResponseType {
    // 来自 InteractiveBasicType
    private List<String> entryNodeIds;
    private List<RuntimeEdgeItemType> memoryEdges;
    private List<NodeOutputItemType> nodeOutputs;
    private ToolParams toolParams;
    private InteractiveNodeType nodeResponse;

    @Data
    @SuperBuilder
    public static class ToolParams {
        private List<String> entryNodeIds;
        private List<ChatCompletionMessageParam> memoryMessages; // 需替换为ChatCompletionMessageParam对应Java类
        private String toolCallId;
    }
}