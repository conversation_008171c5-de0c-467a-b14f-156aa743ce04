package com.sinitek.mind.core.chat.dto;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Schema(description = "初始化聊天响应")
public class InitChatResponse {

    @Schema(description = "聊天ID")
    private String chatId;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "用户头像")
    private String userAvatar;

    @Schema(description = "聊天标题")
    private String title;

    @Schema(description = "变量")
    private Map<String, Object> variables;

    @Schema(description = "应用信息")
    private AppInfo app;

    @Data
    @Schema(description = "应用信息")
    public static class AppInfo {

        @Schema(description = "聊天配置")
        private AppChatConfigType chatConfig;

        @Schema(description = "聊天模型列表")
        private List<String> chatModels;

        @Schema(description = "应用名称")
        private String name;

        @Schema(description = "应用头像")
        private String avatar;

        @Schema(description = "应用介绍")
        private String intro;

        @Schema(description = "是否可用")
        private Boolean canUse;

        @Schema(description = "应用类型")
        private String type;

        @Schema(description = "插件输入")
        private List<FlowNodeInputItemType> pluginInputs;
    }
}