package com.sinitek.mind.core.workflow.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.springframework.data.mongodb.core.mapping.Field;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FlowNodeOutputItemType {

    @Field("id")
    private String id;
    private String type; //FlowNodeOutputTypeEnum
    private String key;
    private String valueType; //WorkflowIOValueTypeEnum
    private String valueDesc;
    private Object value;
    private String label;
    private String description;
    private Object defaultValue;
    private Boolean required;
    private Boolean invalid;
    private CustomFieldConfigType customFieldConfig;
    private Boolean deprecated;
}
