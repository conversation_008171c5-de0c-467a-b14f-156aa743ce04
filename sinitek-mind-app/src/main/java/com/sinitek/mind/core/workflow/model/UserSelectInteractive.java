package com.sinitek.mind.core.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@EqualsAndHashCode(callSuper = true)
public class UserSelectInteractive extends InteractiveNodeType {
    private String type = "userSelect";
    private Params params;

    @Data
    @SuperBuilder
    public static class Params {
        private String description;
        private List<UserSelectOptionItemType> userSelectOptions;
        private String userSelectedVal;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public Params getParam() {
        return params;
    }
}