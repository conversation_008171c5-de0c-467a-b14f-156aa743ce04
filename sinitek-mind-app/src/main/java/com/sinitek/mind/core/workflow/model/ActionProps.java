package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.model.dto.SystemModelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * classifyQuestion的ActionProps
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ActionProps extends ModuleDispatchProps{

    private SystemModelDTO cqModel;

    private DispatchNodeResponseType.ClassifyQuestionAgentItemType lastMemory;
}
