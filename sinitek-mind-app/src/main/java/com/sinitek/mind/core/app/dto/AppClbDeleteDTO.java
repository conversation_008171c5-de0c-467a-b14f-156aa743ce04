package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.support.permission.dto.CollaboratorDeleteDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * App协作者信息更新DTO
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "App协作者删除对象")
public class AppClbDeleteDTO extends CollaboratorDeleteDTO {

    @Schema(description = "appID）")
    private String appId;
}