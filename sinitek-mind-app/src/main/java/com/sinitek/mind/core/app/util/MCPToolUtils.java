package com.sinitek.mind.core.app.util;

import com.sinitek.mind.core.app.enumerate.FlowNodeInputTypeEnum;
import com.sinitek.mind.core.app.enumerate.FlowNodeOutputTypeEnum;
import com.sinitek.mind.core.app.enumerate.WorkflowIOValueTypeEnum;
import com.sinitek.mind.core.app.model.JSONSchemaInputType;
import com.sinitek.mind.core.app.model.JsonSchemaPropertiesItemType;
import com.sinitek.mind.core.app.model.McpToolConfig;
import com.sinitek.mind.core.app.model.SecretValueType;
import com.sinitek.mind.core.workflow.enumerate.FlowNodeTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeOutputItemType;
import com.sinitek.mind.core.workflow.model.RuntimeNodeItemType;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class MCPToolUtils {
    
    /**
     * 创建 MCP 工具运行时节点
     * 
     * @param tool MCP 工具配置
     * @param url MCP 服务地址
     * @param headerSecret 请求头密钥（可选）
     * @param avatar 节点头像（可选，默认为 'core/app/type/mcpToolsFill'）
     * @return 运行时节点对象
     */
    public static RuntimeNodeItemType getMCPToolRuntimeNode(
            McpToolConfig tool,
            String url,
            Map<String, SecretValueType> headerSecret,
            String avatar) {
        
        if (avatar == null || avatar.isEmpty()) {
            avatar = "core/app/type/mcpToolsFill";
        }
        
        // 生成16位随机ID
        String nodeId = WorkflowUtil.getNanoid(16);

        // 构建工具数据输入
        FlowNodeInputItemType toolDataInput = new FlowNodeInputItemType();
        toolDataInput.setKey(NodeInputKeyEnum.TOOL_DATA.getValue());
        toolDataInput.setLabel("Tool Data");
        toolDataInput.setValueType(WorkflowIOValueTypeEnum.OBJECT.getValue());
        toolDataInput.setValue(buildToolDataValue(tool, url, headerSecret));
        toolDataInput.setRenderTypeList(List.of(FlowNodeInputTypeEnum.HIDDEN.getValue()));

        // 从 JSON Schema 生成输入参数
        List<FlowNodeInputItemType> schemaInputs = jsonSchema2NodeInput(tool.getInputSchema());
        
        // 合并所有输入
        List<FlowNodeInputItemType> allInputs = new ArrayList<>();
        allInputs.add(toolDataInput);
        allInputs.addAll(schemaInputs);

        // 构建输出
        FlowNodeOutputItemType rawResponseOutput = new FlowNodeOutputItemType();
        rawResponseOutput.setId(NodeOutputKeyEnum.RAW_RESPONSE.getValue());
        rawResponseOutput.setKey(NodeOutputKeyEnum.RAW_RESPONSE.getValue());
        rawResponseOutput.setRequired(true);
        rawResponseOutput.setLabel("workflow:raw_response");
        rawResponseOutput.setDescription("workflow:tool_raw_response_description");
        rawResponseOutput.setValueType(WorkflowIOValueTypeEnum.ANY.getValue());
        rawResponseOutput.setType(FlowNodeOutputTypeEnum.STATIC.getValue());
        RuntimeNodeItemType runtimeNodeItemType = new RuntimeNodeItemType();
        runtimeNodeItemType.setNodeId(nodeId);
        runtimeNodeItemType.setFlowNodeType(FlowNodeTypeEnum.TOOL.getValue());
        runtimeNodeItemType.setAvatar(avatar);
        runtimeNodeItemType.setIntro(tool.getDescription());
        runtimeNodeItemType.setInputs(allInputs);
        runtimeNodeItemType.setOutputs(List.of(rawResponseOutput));
        runtimeNodeItemType.setName(tool.getName());
        runtimeNodeItemType.setVersion("");
        return runtimeNodeItemType;

    }
    
    /**
     * 构建工具数据值对象
     */
    private static Map<String, Object> buildToolDataValue(
            McpToolConfig tool,
            String url, 
            Map<String, SecretValueType> headerSecret) {
        Map<String, Object> toolData = new HashMap<>();
        
        // 复制工具配置属性
        toolData.put("name", tool.getName());
        toolData.put("description", tool.getDescription());
        toolData.put("inputSchema", tool.getInputSchema());
        
        // 添加额外属性
        toolData.put("url", url);
        if (headerSecret != null) {
            toolData.put("headerSecret", headerSecret);
        }
        
        return toolData;
    }
    
    /**
     * 从 JSON Schema 转换为节点输入列表
     */
    private static List<FlowNodeInputItemType> jsonSchema2NodeInput(JSONSchemaInputType jsonSchema) {
        if (jsonSchema == null || jsonSchema.getProperties() == null) {
            return new ArrayList<>();
        }
        
        return jsonSchema.getProperties().entrySet().stream()
                .map(entry -> {
                    String key = entry.getKey();
                    JsonSchemaPropertiesItemType value = (JsonSchemaPropertiesItemType)entry.getValue();
                    FlowNodeInputItemType itemtype = new FlowNodeInputItemType();
                    itemtype.setKey(key);
                    itemtype.setLabel(key);
                    itemtype.setValueType(getNodeInputTypeFromSchemaInputType(value.getType(), value.getItems()).getValue());
                    itemtype.setDescription(value.getDescription());
                    itemtype.setToolDescription(value.getDescription() != null ? value.getDescription() : key);
                    itemtype.setRequired(jsonSchema.getRequired() != null && jsonSchema.getRequired().contains(key));
                    itemtype.setRenderTypeList(getNodeInputRenderTypeFromSchemaInputType(value));
                    return itemtype;
                })
                .toList();
    }
    
    // 辅助方法实现...
    private static WorkflowIOValueTypeEnum getNodeInputTypeFromSchemaInputType(String type, JsonSchemaPropertiesItemType.ItemInfo items) {
        // 实现类型转换逻辑
        switch (type) {
            case "string": return WorkflowIOValueTypeEnum.STRING;
            case "number": return WorkflowIOValueTypeEnum.NUMBER;
            case "boolean": return WorkflowIOValueTypeEnum.BOOLEAN;
            case "object": return WorkflowIOValueTypeEnum.OBJECT;
            case "array":
                if (items != null) {
                    switch (items.getType()) {
                        case "string": return WorkflowIOValueTypeEnum.ARRAY_STRING;
                        case "number": return WorkflowIOValueTypeEnum.ARRAY_NUMBER;
                        case "boolean": return WorkflowIOValueTypeEnum.ARRAY_BOOLEAN;
                        case "object": return WorkflowIOValueTypeEnum.ARRAY_OBJECT;
                    }
                }
                return WorkflowIOValueTypeEnum.ARRAY_ANY;
            default: return WorkflowIOValueTypeEnum.ANY;
        }
    }
    
    private static List<String> getNodeInputRenderTypeFromSchemaInputType(JsonSchemaPropertiesItemType value) {
        // 根据 schema 属性确定渲染类型
        if (value.getEnums() != null && !value.getEnums().isEmpty()) {
            return List.of(FlowNodeInputTypeEnum.SELECT.getValue());
        }

        return switch (value.getType()) {
            case "string" -> List.of(FlowNodeInputTypeEnum.INPUT.getValue());
            case "number" -> List.of(FlowNodeInputTypeEnum.NUMBER_INPUT.getValue());
            case "boolean" -> List.of(FlowNodeInputTypeEnum.SWITCH.getValue());
            case "object" -> List.of(FlowNodeInputTypeEnum.JSON_EDITOR.getValue());
            default -> List.of(FlowNodeInputTypeEnum.INPUT.getValue());
        };
    }
}