package com.sinitek.mind.core.workflow.template;

import com.sinitek.mind.common.util.I18nUtil;
import com.sinitek.mind.core.app.enumerate.FlowNodeInputTypeEnum;
import com.sinitek.mind.core.app.enumerate.WorkflowIOValueTypeEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;

import java.util.List;

public class InputTemplate {

    public static FlowNodeInputItemType INPUT_TEMPLATE_HISTORY;
    public static FlowNodeInputItemType INPUT_TEMPLATE_USER_CHAT_INPUT;
    public static FlowNodeInputItemType INPUT_TEMPLATE_DYNAMIC_INPUT;
    public static FlowNodeInputItemType INPUT_TEMPLATE_SELECT_AI_MODEL;
    public static FlowNodeInputItemType INPUT_TEMPLATE_SETTING_AI_MODEL;
    public static FlowNodeInputItemType INPUT_TEMPLATE_SYSTEM_PROMPT;
    public static FlowNodeInputItemType INPUT_TEMPLATE_DATASET_QUOTE;
    public static FlowNodeInputItemType INPUT_TEMPLATE_TEXT_QUOTE;
    public static FlowNodeInputItemType INPUT_TEMPLATE_FILE_LINK;
    public static FlowNodeInputItemType INPUT_TEMPLATE_CHILDREN_NODE_LIST;
    public static FlowNodeInputItemType INPUT_TEMPLATE_NODE_WIDTH;
    public static FlowNodeInputItemType INPUT_TEMPLATE_NODE_HEIGHT;
    public static FlowNodeInputItemType INPUT_TEMPLATE_LOOP_NODE_OFFSET;
    public static FlowNodeInputItemType INPUT_TEMPLATE_STREAM_MODE;

    static {
        // INPUT_TEMPLATE_HISTORY
        INPUT_TEMPLATE_HISTORY = new FlowNodeInputItemType();
        INPUT_TEMPLATE_HISTORY.setKey(NodeInputKeyEnum.HISTORY.getValue());
        INPUT_TEMPLATE_HISTORY.setRenderTypeList(List.of(FlowNodeInputTypeEnum.NUMBER_INPUT.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()));
        INPUT_TEMPLATE_HISTORY.setValueType(WorkflowIOValueTypeEnum.CHAT_HISTORY.getValue());
        INPUT_TEMPLATE_HISTORY.setLabel(I18nUtil.t("core.module.input.label.chat history"));
        INPUT_TEMPLATE_HISTORY.setDescription(I18nUtil.t("max_dialog_rounds"));
        INPUT_TEMPLATE_HISTORY.setRequired(true);
        INPUT_TEMPLATE_HISTORY.setMin(0);
        INPUT_TEMPLATE_HISTORY.setMax(50);
        INPUT_TEMPLATE_HISTORY.setValue(6);

        // INPUT_TEMPLATE_USER_CHAT_INPUT
        INPUT_TEMPLATE_USER_CHAT_INPUT = new FlowNodeInputItemType();
        INPUT_TEMPLATE_USER_CHAT_INPUT.setKey(NodeInputKeyEnum.USER_CHAT_INPUT.getValue());
        INPUT_TEMPLATE_USER_CHAT_INPUT.setRenderTypeList(List.of(FlowNodeInputTypeEnum.REFERENCE.getValue(), FlowNodeInputTypeEnum.TEXTAREA.getValue()));
        INPUT_TEMPLATE_USER_CHAT_INPUT.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        INPUT_TEMPLATE_USER_CHAT_INPUT.setLabel(I18nUtil.t("user_question"));
        INPUT_TEMPLATE_USER_CHAT_INPUT.setToolDescription(I18nUtil.t("user_question_tool_desc"));
        INPUT_TEMPLATE_USER_CHAT_INPUT.setRequired(true);

        // INPUT_TEMPLATE_DYNAMIC_INPUT
        INPUT_TEMPLATE_DYNAMIC_INPUT = new FlowNodeInputItemType();
        INPUT_TEMPLATE_DYNAMIC_INPUT.setKey(NodeInputKeyEnum.ADD_INPUT_PARAM.getValue());
        INPUT_TEMPLATE_DYNAMIC_INPUT.setRenderTypeList(List.of(FlowNodeInputTypeEnum.ADD_INPUT_PARAM.getValue()));
        INPUT_TEMPLATE_DYNAMIC_INPUT.setValueType(WorkflowIOValueTypeEnum.DYNAMIC.getValue());
        INPUT_TEMPLATE_DYNAMIC_INPUT.setLabel("");
        INPUT_TEMPLATE_DYNAMIC_INPUT.setRequired(false);

        // INPUT_TEMPLATE_SELECT_AI_MODEL
        INPUT_TEMPLATE_SELECT_AI_MODEL = new FlowNodeInputItemType();
        INPUT_TEMPLATE_SELECT_AI_MODEL.setKey(NodeInputKeyEnum.AI_MODEL.getValue());
        INPUT_TEMPLATE_SELECT_AI_MODEL.setRenderTypeList(List.of(FlowNodeInputTypeEnum.SELECT_LLM_MODEL.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()));
        INPUT_TEMPLATE_SELECT_AI_MODEL.setLabel(I18nUtil.t("core.module.input.label.aiModel"));
        INPUT_TEMPLATE_SELECT_AI_MODEL.setRequired(true);
        INPUT_TEMPLATE_SELECT_AI_MODEL.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());

        // INPUT_TEMPLATE_SETTING_AI_MODEL
        INPUT_TEMPLATE_SETTING_AI_MODEL = new FlowNodeInputItemType();
        INPUT_TEMPLATE_SETTING_AI_MODEL.setKey(NodeInputKeyEnum.AI_MODEL.getValue());
        INPUT_TEMPLATE_SETTING_AI_MODEL.setRenderTypeList(List.of(FlowNodeInputTypeEnum.SETTING_LLM_MODEL.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()));
        INPUT_TEMPLATE_SETTING_AI_MODEL.setLabel(I18nUtil.t("core.module.input.label.aiModel"));
        INPUT_TEMPLATE_SETTING_AI_MODEL.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());

        // INPUT_TEMPLATE_SYSTEM_PROMPT
        INPUT_TEMPLATE_SYSTEM_PROMPT = new FlowNodeInputItemType();
        INPUT_TEMPLATE_SYSTEM_PROMPT.setKey(NodeInputKeyEnum.AI_SYSTEM_PROMPT.getValue());
        INPUT_TEMPLATE_SYSTEM_PROMPT.setRenderTypeList(List.of(FlowNodeInputTypeEnum.TEXTAREA.getValue(), FlowNodeInputTypeEnum.REFERENCE.getValue()));
        INPUT_TEMPLATE_SYSTEM_PROMPT.setMax(3000);
        INPUT_TEMPLATE_SYSTEM_PROMPT.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());
        INPUT_TEMPLATE_SYSTEM_PROMPT.setLabel(I18nUtil.t("core.ai.Prompt"));
        INPUT_TEMPLATE_SYSTEM_PROMPT.setDescription(I18nUtil.t("system_prompt_tip"));
        INPUT_TEMPLATE_SYSTEM_PROMPT.setPlaceholder(I18nUtil.t("chat_node_system_prompt_tip"));

        // INPUT_TEMPLATE_DATASET_QUOTE
        INPUT_TEMPLATE_DATASET_QUOTE = new FlowNodeInputItemType();
        INPUT_TEMPLATE_DATASET_QUOTE.setKey(NodeInputKeyEnum.AI_CHAT_DATASET_QUOTE.getValue());
        INPUT_TEMPLATE_DATASET_QUOTE.setRenderTypeList(List.of(FlowNodeInputTypeEnum.SETTING_DATASET_QUOTE_PROMPT.getValue()));
        INPUT_TEMPLATE_DATASET_QUOTE.setLabel("");
        INPUT_TEMPLATE_DATASET_QUOTE.setDebugLabel(I18nUtil.t("knowledge_base_reference"));
        INPUT_TEMPLATE_DATASET_QUOTE.setDescription("");
        INPUT_TEMPLATE_DATASET_QUOTE.setValueType(WorkflowIOValueTypeEnum.DATASET_QUOTE.getValue());

        // INPUT_TEMPLATE_TEXT_QUOTE
        INPUT_TEMPLATE_TEXT_QUOTE = new FlowNodeInputItemType();
        INPUT_TEMPLATE_TEXT_QUOTE.setKey(NodeInputKeyEnum.STRING_QUOTE_TEXT.getValue());
        INPUT_TEMPLATE_TEXT_QUOTE.setRenderTypeList(List.of(FlowNodeInputTypeEnum.REFERENCE.getValue(), FlowNodeInputTypeEnum.TEXTAREA.getValue()));
        INPUT_TEMPLATE_TEXT_QUOTE.setLabel(I18nUtil.t("document_quote"));
        INPUT_TEMPLATE_TEXT_QUOTE.setDebugLabel(I18nUtil.t("document_quote"));
        INPUT_TEMPLATE_TEXT_QUOTE.setDescription(I18nUtil.t("document_quote_tip"));
        INPUT_TEMPLATE_TEXT_QUOTE.setValueType(WorkflowIOValueTypeEnum.STRING.getValue());

        // INPUT_TEMPLATE_FILE_LINK
        INPUT_TEMPLATE_FILE_LINK = new FlowNodeInputItemType();
        INPUT_TEMPLATE_FILE_LINK.setKey(NodeInputKeyEnum.FILE_URL_LIST.getValue());
        INPUT_TEMPLATE_FILE_LINK.setRenderTypeList(List.of(FlowNodeInputTypeEnum.REFERENCE.getValue(), FlowNodeInputTypeEnum.INPUT.getValue()));
        INPUT_TEMPLATE_FILE_LINK.setLabel(I18nUtil.t("workflow.user_file_input"));
        INPUT_TEMPLATE_FILE_LINK.setDebugLabel(I18nUtil.t("workflow.user_file_input"));
        INPUT_TEMPLATE_FILE_LINK.setDescription(I18nUtil.t("workflow.user_file_input_desc"));
        INPUT_TEMPLATE_FILE_LINK.setValueType(WorkflowIOValueTypeEnum.ARRAY_STRING.getValue());

        // INPUT_TEMPLATE_CHILDREN_NODE_LIST
        INPUT_TEMPLATE_CHILDREN_NODE_LIST = new FlowNodeInputItemType();
        INPUT_TEMPLATE_CHILDREN_NODE_LIST.setKey(NodeInputKeyEnum.CHILDREN_NODE_ID_LIST.getValue());
        INPUT_TEMPLATE_CHILDREN_NODE_LIST.setRenderTypeList(List.of(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        INPUT_TEMPLATE_CHILDREN_NODE_LIST.setValueType(WorkflowIOValueTypeEnum.ARRAY_STRING.getValue());
        INPUT_TEMPLATE_CHILDREN_NODE_LIST.setLabel("");
        INPUT_TEMPLATE_CHILDREN_NODE_LIST.setValue(List.of());

        // INPUT_TEMPLATE_NODE_WIDTH
        INPUT_TEMPLATE_NODE_WIDTH = new FlowNodeInputItemType();
        INPUT_TEMPLATE_NODE_WIDTH.setKey(NodeInputKeyEnum.NODE_WIDTH.getValue());
        INPUT_TEMPLATE_NODE_WIDTH.setRenderTypeList(List.of(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        INPUT_TEMPLATE_NODE_WIDTH.setValueType(WorkflowIOValueTypeEnum.NUMBER.getValue());
        INPUT_TEMPLATE_NODE_WIDTH.setLabel("");
        INPUT_TEMPLATE_NODE_WIDTH.setValue(900);

        // INPUT_TEMPLATE_NODE_HEIGHT
        INPUT_TEMPLATE_NODE_HEIGHT = new FlowNodeInputItemType();
        INPUT_TEMPLATE_NODE_HEIGHT.setKey(NodeInputKeyEnum.NODE_HEIGHT.getValue());
        INPUT_TEMPLATE_NODE_HEIGHT.setRenderTypeList(List.of(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        INPUT_TEMPLATE_NODE_HEIGHT.setValueType(WorkflowIOValueTypeEnum.NUMBER.getValue());
        INPUT_TEMPLATE_NODE_HEIGHT.setLabel("");
        INPUT_TEMPLATE_NODE_HEIGHT.setValue(600);

        // INPUT_TEMPLATE_LOOP_NODE_OFFSET
        INPUT_TEMPLATE_LOOP_NODE_OFFSET = new FlowNodeInputItemType();
        INPUT_TEMPLATE_LOOP_NODE_OFFSET.setKey(NodeInputKeyEnum.LOOP_NODE_INPUT_HEIGHT.getValue());
        INPUT_TEMPLATE_LOOP_NODE_OFFSET.setRenderTypeList(List.of(FlowNodeInputTypeEnum.HIDDEN.getValue()));
        INPUT_TEMPLATE_LOOP_NODE_OFFSET.setValueType(WorkflowIOValueTypeEnum.NUMBER.getValue());
        INPUT_TEMPLATE_LOOP_NODE_OFFSET.setLabel("");
        INPUT_TEMPLATE_LOOP_NODE_OFFSET.setValue(320);

        // INPUT_TEMPLATE_STREAM_MODE
        INPUT_TEMPLATE_STREAM_MODE = new FlowNodeInputItemType();
        INPUT_TEMPLATE_STREAM_MODE.setKey(NodeInputKeyEnum.FORBID_STREAM.getValue());
        INPUT_TEMPLATE_STREAM_MODE.setRenderTypeList(List.of(FlowNodeInputTypeEnum.SWITCH.getValue()));
        INPUT_TEMPLATE_STREAM_MODE.setValueType(WorkflowIOValueTypeEnum.BOOLEAN.getValue());
        INPUT_TEMPLATE_STREAM_MODE.setLabel(I18nUtil.t("template.forbid_stream"));
        INPUT_TEMPLATE_STREAM_MODE.setDescription(I18nUtil.t("template.forbid_stream_desc"));
        INPUT_TEMPLATE_STREAM_MODE.setValue(false);
    }

}
