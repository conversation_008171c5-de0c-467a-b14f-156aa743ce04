package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatHistoryItemResType;
import com.sinitek.mind.core.dataset.model.DebugResponse;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
@Builder
public class DispatchFlowResponse {

    /**
     * 流程响应列表
     */
    private List<ChatHistoryItemResType> flowResponses;

    /**
     * 流程使用情况列表
     */
    private List<ChatNodeUsageType> flowUsage;

    /**
     * 调试响应
     */
    private DebugResponse debugResponse;

    /**
     * 工作流交互响应
     */
    private WorkflowInteractiveResponseType workflowInteractiveResponse;

    /**
     * 工具响应
     */
    private Object toolResponses;

    /**
     * 助手响应列表
     */
    private List<AIChatItemValueItemType> assistantResponses;
    /**
     * 运行次数
     */
    private Integer runTimes;

    /**
     * 系统记忆
     */
    private Map<String, Object> system_memories;

    /**
     * 新变量
     */
    private Map<String, Object> newVariables;

    /**
     * 持续时间（秒）
     */
    private double durationSeconds;

}
