package com.sinitek.mind.core.dataset.model;

import com.sinitek.mind.core.workflow.model.RuntimeEdgeItemType;
import com.sinitek.mind.core.workflow.model.RuntimeNodeItemType;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class DebugResponse {
    private List<RuntimeNodeItemType> finishedNodes;

    private List<RuntimeEdgeItemType> finishedEdges;

    private List<RuntimeNodeItemType> nextStepRunNodes;
}