package com.sinitek.mind.core.workflow.dispatch;

import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

@Service
public class NodeDispatcherFactory {
    private static final String DISPATCHER_SUFFIX = "Dispatcher";
    
    private final ApplicationContext applicationContext;

    public NodeDispatcherFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    public NodeDispatcher getDispatcher(String nodeType) {
        String beanName = nodeType + DISPATCHER_SUFFIX;
        try {
            return applicationContext.getBean(beanName, NodeDispatcher.class);
        } catch (NoSuchBeanDefinitionException e) {
            throw new IllegalArgumentException("Unsupported node type: " + nodeType);
        }
    }
}