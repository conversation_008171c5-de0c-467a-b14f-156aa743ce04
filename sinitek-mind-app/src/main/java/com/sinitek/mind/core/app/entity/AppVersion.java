package com.sinitek.mind.core.app.entity;

import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.workflow.model.StoreEdgeItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.List;

@Data
@Document(collection = "app_versions")
public class AppVersion {

    @Id
    private String id;

    private String tmbId;

    @Field(targetType = FieldType.OBJECT_ID)
    private String appId;

    private List<StoreNodeItemType> nodes;

    private List<StoreEdgeItemType> edges;

    private AppChatConfigType chatConfig;

    private String versionName;

    private String username;

    private String avatar;

    private Boolean isPublish;

    private Date createTime;

    private Date time;

    private Date updateTime;
}
