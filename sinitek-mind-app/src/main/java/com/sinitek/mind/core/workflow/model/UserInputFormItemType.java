package com.sinitek.mind.core.workflow.model;

import lombok.Data;

import java.util.List;

@Data
public class UserInputFormItemType {
    private String type; // FlowNodeInputTypeEnum
    private String key;
    private String label;
    private Object value;
    private String valueType; // WorkflowIOValueTypeEnum
    private String description;
    private Object defaultValue;
    private boolean required;
    private Integer maxLength;
    private Integer max;
    private Integer min;
    private List<ListItem> list;

    @Data
    public static class ListItem {
        private String label;
        private String value;
    }
}