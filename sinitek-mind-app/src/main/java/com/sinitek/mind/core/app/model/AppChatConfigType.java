package com.sinitek.mind.core.app.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.sinitek.mind.core.chat.model.ChatInputGuideConfigType;
import lombok.Data;

import java.util.List;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AppChatConfigType {

    private String welcomeText;

    private List<VariableItemType> variables;

    private AppAutoExecuteConfigType autoExecute;

    private AppQGConfigType questionGuide;
    private AppTTSConfigType ttsConfig;
    private AppWhisperConfigType whisperConfig;
    private AppScheduledTriggerConfigType scheduledTriggerConfig;
    private ChatInputGuideConfigType chatInputGuide;
    private AppFileSelectConfigType fileSelectConfig;
    private String instruction;

    private String _id;
}
