package com.sinitek.mind.core.workflow.dispatch.loop;

import com.sinitek.mind.core.workflow.dispatch.NodeDispatcher;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.model.DispatchNodeResponseType;
import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

/**
 * 循环开始节点
 * <AUTHOR>
 * @date 2025/7/17
 */
@Service
public class LoopNodeStartDispatcher implements NodeDispatcher {

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        Map<String, Object> params = dispatchData.getParams();

        Object loopStartInput = params.get(
            NodeInputKeyEnum.LOOP_START_INPUT.getValue());

        Map<String, Object> result = new HashMap<>();

        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), DispatchNodeResponseType.builder()
            .loopInputValue(loopStartInput)
            .build());
        result.put(NodeOutputKeyEnum.LOOP_START_INPUT.getValue(), loopStartInput);
        result.put(NodeOutputKeyEnum.LOOP_START_INDEX.getValue(), params.get(
            NodeInputKeyEnum.LOOP_START_INDEX.getValue()));
        return result;
    }
}
