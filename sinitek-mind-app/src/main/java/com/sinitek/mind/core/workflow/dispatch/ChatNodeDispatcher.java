package com.sinitek.mind.core.workflow.dispatch;

import com.sinitek.mind.core.chat.adapter.ChatAdaptor;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemTextInfo;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.dataset.model.SearchDataResponseItemType;
import com.sinitek.mind.core.workflow.dispatch.util.DispatchUtil;
import com.sinitek.mind.core.workflow.enumerate.DispatchNodeResponseKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeInputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.NodeOutputKeyEnum;
import com.sinitek.mind.core.workflow.enumerate.SseResponseEventEnum;
import com.sinitek.mind.core.workflow.model.*;
import com.sinitek.mind.core.workflow.model.sse.GptResponse;
import com.sinitek.mind.core.workflow.util.WorkflowUtil;
import com.sinitek.mind.model.core.llm.LLMChatModelFactory;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import reactor.core.publisher.Flux;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.sinitek.mind.core.app.util.TimeUtils.formatTime2YMDHM;

@Slf4j
@Component("chatNodeDispatcher")
public class ChatNodeDispatcher implements NodeDispatcher {

    @Autowired
    private LLMChatModelFactory llmChatModelFactory;

    @Autowired
    private ISystemModelService systemModelService;

    @Override
    public Map<String, Object> dispatch(ModuleDispatchProps dispatchData) {
        try {
            return dispatchChatCompletion(dispatchData);
        } catch (Exception e) {
            throw new BussinessException("Chat completion failed: " + e.getMessage(), e);
        }
    }

    /**
     * 执行聊天完成逻辑
     * 对应TypeScript中的dispatchChatCompletion函数
     */
    private Map<String, Object> dispatchChatCompletion(ModuleDispatchProps props) {
        // 1. 提取参数
        RuntimeNodeItemType node = props.getNode();
        Map<String, Object> params = props.getParams();
        List<ChatItemType> histories = props.getHistories();
        List<ChatItemValueItemType> query = props.getQuery();

        // 2. 获取节点输入参数
        String model = getStringParam(params, "model", "");
        Double temperature = getDoubleParam(params, "temperature", 0.7);
        Integer maxToken = getIntegerParam(params, "maxToken", 2000);
        Integer history = getIntegerParam(params, "history", 6);
        String systemPrompt = getStringParam(params, "systemPrompt", "");
        String aiChatQuoteRole = getStringParam(params, "aiChatQuoteRole", "system");
        String quoteTemplate = getStringParam(params, "quoteTemplate", "");
        String quotePrompt = getStringParam(params, "quotePrompt", "");
        Boolean aiChatVision = getBooleanParam(params, "aiChatVision", false);
        Boolean aiChatReasoning = getBooleanParam(params, "aiChatReasoning", true);
        Double aiChatTopP = getDoubleParam(params, "aiChatTopP", 1.0);
        List<String> aiChatStopSign = getListParam(params, "aiChatStopSign");
        String aiChatResponseFormat = getStringParam(params, "aiChatResponseFormat", "text");
        Object aiChatJsonSchema = params.get("aiChatJsonSchema");
        List<String> fileUrlList = getListParam(params, "fileUrlList");
        String stringQuoteText = getStringParam(params, "stringQuoteText", "");

        // 3. 验证模型
        if (!StringUtils.hasText(model)) {
            throw new IllegalArgumentException("Model is required, you need to select a chat model.");
        }

        // 4. 获取模型配置
        SystemModelDTO modelConfig = getModelConfig(model);
        if (modelConfig == null) {
            throw new IllegalArgumentException("Model " + model + " is undefined, you need to select a chat model.");
        }

        // 5. 处理文件输入
        RuntimePromptType runtimePromptType = ChatAdaptor.chatValue2RuntimePrompt(query);
        String userChatInput = runtimePromptType.getText();
        List<ChatItemValueItemFileInfo> inputFiles = runtimePromptType.getFiles();

        // 视觉
        aiChatVision = modelConfig.getVision() && aiChatVision;

        // 推理
        aiChatReasoning = aiChatReasoning && modelConfig.getReasoning();

        // 文件URL输入
        List<FlowNodeInputItemType> fileUrlInput = props.getNode().getInputs()
                .stream()
                .filter(item -> item.getKey().equals(NodeInputKeyEnum.FILE_URL_LIST.getValue())).toList();
        if (fileUrlInput.isEmpty() || fileUrlInput.get(0).getValue() == null ) {
            fileUrlList = new ArrayList<>();
        }

        // 6. 处理历史记录
        List<ChatItemType> chatHistories = DispatchUtil.getHistories(history, histories);

        // 7. 处理引用数据
        List<SearchDataResponseItemType> quoteQA = getListParam(params, NodeInputKeyEnum.AI_CHAT_DATASET_QUOTE.getValue()).stream()
                .filter(SearchDataResponseItemType.class::isInstance)
                .map(SearchDataResponseItemType.class::cast)
                .collect(Collectors.toList());
        quoteQA = DispatchUtil.checkQuoteQAValue(quoteQA);
        String datasetQuoteText = filterDatasetQuote(quoteQA, modelConfig, quoteTemplate);

        // 8. 处理多输入（文件、文档等）
//        MultiInputResult multiInputResult = getMultiInput(
//                chatHistories, inputFiles, fileUrlList, stringQuoteText, props
//        );
//        String documentQuoteText = multiInputResult.documentQuoteText();
//        List<ChatItemValueItemFileInfo> userFiles = multiInputResult.userFiles();
        List<ChatItemValueItemFileInfo> userFiles = new ArrayList<>();

        // 9. 验证输入
//        if (!StringUtils.hasText(userChatInput) &&
//                !StringUtils.hasText(documentQuoteText) &&
//                (userFiles == null || userFiles.isEmpty())) {
//            throw new IllegalArgumentException("AI input is empty");
//        }

        // 10. 计算最大token
        int maxTokens = computeMaxToken(modelConfig, maxToken);

        // 11. 构建聊天消息
        List<Message> filterMessages = getChatMessages(
                modelConfig, maxTokens, aiChatQuoteRole, quotePrompt, datasetQuoteText,
                !quoteQA.isEmpty(), chatHistories, systemPrompt,
                userChatInput, userFiles, ""
        );

        // 12. 创建聊天模型
        ChatModel chatModel = llmChatModelFactory.createChatModel(modelConfig);

        // 13. 构建聊天选项
        OpenAiChatOptions chatOptions = OpenAiChatOptions.builder()
                .model(modelConfig.getModel())
                .temperature(temperature)
                .maxTokens(maxTokens)
                .topP(aiChatTopP)
                .stop(aiChatStopSign)
                .build();

        // 14. 创建聊天提示
        Prompt prompt = new Prompt(filterMessages, chatOptions);
//        ChatResponse response = chatModel.call(prompt);

        // 15. 检查是否启用流式响应
        boolean isStreamResponse = props.isStream() && props.getWorkflowStreamResponse() != null;

        String answerText;
        String reasoningText;
        String finishReason = "stop";

        if (isStreamResponse) {
            // 流式响应处理
            StreamResponseResult streamResult = handleStreamResponse(
                    chatModel, prompt, props, modelConfig, aiChatReasoning
            );
            answerText = streamResult.answerText();
            reasoningText = streamResult.reasoningText();
            finishReason = streamResult.finishReason();
        } else {
            // 同步聊天请求
            ChatResponse response = chatModel.call(prompt);

            answerText = response.getResult().getOutput().getText();
            reasoningText = extractReasoningText(response, aiChatReasoning);
        }

        if (!StringUtils.hasText(answerText) && !StringUtils.hasText(reasoningText)) {
            throw new RuntimeException("Empty response from AI model");
        }

        // 16. 计算token使用量
        int inputTokens = calculateInputTokens(filterMessages);
        int outputTokens = calculateOutputTokens(answerText, reasoningText);

        // 17. 构建完整消息历史
        List<Message> completeMessages = new ArrayList<>(filterMessages);
        completeMessages.add(new AssistantMessage(answerText));
        List<ChatItemType> chatCompleteMessages = convertToChatHistory(completeMessages);

        // 18. 构建返回结果
        Map<String, Object> result = new HashMap<>();
        result.put(NodeOutputKeyEnum.ANSWER_TEXT.getValue(), answerText.trim());
        if (StringUtils.hasText(reasoningText)) {
            result.put(NodeOutputKeyEnum.REASONING_TEXT.getValue(), reasoningText);
        }
        result.put(NodeOutputKeyEnum.HISTORY.getValue(), chatCompleteMessages);

        // 19. 构建节点响应
        Map<String, Object> nodeResponse = new HashMap<>();
        nodeResponse.put("totalPoints", 0); // 暂时设为0
        nodeResponse.put("model", modelConfig.getModel());
        nodeResponse.put("inputTokens", inputTokens);
        nodeResponse.put("outputTokens", outputTokens);
        nodeResponse.put("query", userChatInput);
        nodeResponse.put("maxToken", maxToken);
        if (StringUtils.hasText(reasoningText)) {
            nodeResponse.put("reasoningText", reasoningText);
        }
        nodeResponse.put("contextTotalLen", completeMessages.size());
        nodeResponse.put("finishReason", finishReason);

        result.put(DispatchNodeResponseKeyEnum.NODE_RESPONSE.getValue(), nodeResponse);

        // 20. 构建节点使用量
        Map<String, Object> nodeUsage = new HashMap<>();
        nodeUsage.put("moduleName", node.getName());
        nodeUsage.put("totalPoints", 0);
        nodeUsage.put("model", modelConfig.getModel());
        nodeUsage.put("inputTokens", inputTokens);
        nodeUsage.put("outputTokens", outputTokens);

        result.put(DispatchNodeResponseKeyEnum.NODE_DISPATCH_USAGES.getValue(), List.of(nodeUsage));
        result.put(DispatchNodeResponseKeyEnum.TOOL_RESPONSES.getValue(), answerText);

        return result;
    }

    // ==================== 辅助方法 ====================

    /**
     * 流式响应结果记录
     */
    private record StreamResponseResult(String answerText, String reasoningText, String finishReason) {}

    /**
     * 处理流式响应
     */
    private StreamResponseResult handleStreamResponse(
            ChatModel chatModel,
            Prompt prompt,
            ModuleDispatchProps props,
            SystemModelDTO modelConfig,
            boolean aiChatReasoning) {

        StringBuilder answerBuilder = new StringBuilder();
        StringBuilder reasoningBuilder = new StringBuilder();
        String finishReason = "stop";

        try {
            // 使用 ChatModel 的流式调用
            Flux<ChatResponse> streamResponse = chatModel.stream(prompt);

            streamResponse
                    .doOnNext(response -> {
                        try {
                            // 解析流式响应
                            String content = response.getResult().getOutput().getText();
                            String reasoningContent = extractReasoningContent(response);

                            if (StringUtils.hasText(reasoningContent)) {
                                reasoningBuilder.append(reasoningContent);
                                // 发送推理内容
                                if (aiChatReasoning) {
                                    sendStreamResponse(props, SseResponseEventEnum.ANSWER,
                                            createReasoningResponse(reasoningContent));
                                }
                            }

                            if (StringUtils.hasText(content)) {
                                answerBuilder.append(content);
                                // 发送回答内容
                                sendStreamResponse(props, SseResponseEventEnum.ANSWER,
                                        createAnswerResponse(content));
                            }
                        } catch (Exception e) {
                            log.error("处理流式响应片段失败", e);
                        }
                    })
                    .doOnComplete(() -> {
                        log.debug("流式响应完成");
                    })
                    .doOnError(error -> {
                        log.error("流式响应错误", error);
                        // TODO 应该在节点响应中记录错误信息
//                        sendStreamResponse(props, SseResponseEventEnum.ERROR,
//                                Map.of("message", error.getMessage()));
                    })
                    .blockLast(); // 等待流式响应完成

        } catch (Exception e) {
            log.error("流式响应处理失败", e);
            finishReason = "error";
        }

        return new StreamResponseResult(
                answerBuilder.toString().trim(),
                reasoningBuilder.toString().trim(),
                finishReason
        );
    }

    /**
     * 发送流式响应
     */
    private void sendStreamResponse(ModuleDispatchProps props, SseResponseEventEnum event, GptResponse data) {
        if (props.getWorkflowStreamResponse() != null) {
            WorkflowStreamResponse streamResponse = WorkflowStreamResponse.builder()
                    .event(event.getValue())
                    .data(data)
                    .build();
            props.getWorkflowStreamResponse().accept(streamResponse);
        }
    }


    /**
     * 创建推理内容响应
     */
    private GptResponse createReasoningResponse(String reasoningContent) {
        return WorkflowUtil.textAdaptGptResponse(
                TextAdaptGptResponseParams.builder()
                        .reasoningContent(reasoningContent)
                        .build());
    }

    /**
     * 创建回答内容响应
     */
    private GptResponse createAnswerResponse(String content) {
        return WorkflowUtil.textAdaptGptResponse(
                TextAdaptGptResponseParams.builder()
                        .text(content)
                        .build());
    }

    /**
     * 提取推理内容
     */
    private String extractReasoningContent(ChatResponse response) {
        // 根据模型响应格式提取推理内容
        try {
            Object reasoningContent = response.getMetadata().get("reasoning_content");
            if (reasoningContent != null) {
                return reasoningContent.toString();
            }

            // 尝试从其他可能的字段获取推理内容
            Object reasoning = response.getMetadata().get("reasoning");
            if (reasoning != null) {
                return reasoning.toString();
            }
        } catch (Exception e) {
            log.debug("提取推理内容失败", e);
        }
        return "";
    }

    /**
     * 获取字符串参数
     */
    private String getStringParam(Map<String, Object> params, String key, String defaultValue) {
        Object value = params.get(key);
        return value != null ? value.toString() : defaultValue;
    }

    /**
     * 获取Double参数
     */
    private Double getDoubleParam(Map<String, Object> params, String key, Double defaultValue) {
        Object value = params.get(key);
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        return defaultValue;
    }

    /**
     * 获取Integer参数
     */
    private Integer getIntegerParam(Map<String, Object> params, String key, Integer defaultValue) {
        Object value = params.get(key);
        if (value instanceof Number) {
            if (Double.isNaN(((Number) value).doubleValue())) {
                return defaultValue;
            }
            return ((Number) value).intValue();
        }
        return defaultValue;
    }

    /**
     * 获取Boolean参数
     */
    private Boolean getBooleanParam(Map<String, Object> params, String key, Boolean defaultValue) {
        Object value = params.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        return defaultValue;
    }

    /**
     * 获取List参数
     */
    @SuppressWarnings("unchecked")
    private <T> List<T> getListParam(Map<String, Object> params, String key) {
        Object value = params.get(key);
        if (value instanceof List) {
            return (List<T>) value;
        }
        return new ArrayList<>();
    }

    /**
     * 获取模型配置
     */
    private SystemModelDTO getModelConfig(String model) {
        // 这里应该从模型服务获取模型配置
        return systemModelService.findModelByModelId(model);
    }

//    /**
//     * 提取输入文件
//     */
//    private List<ChatItemValueItemFileInfo> extractInputFiles(List<ChatItemValueItemType> query) {
//        // 从query中提取文件信息
//        List<ChatItemValueItemFileInfo> files = new ArrayList<>();
//        if (query != null && query.containsKey("files")) {
//            Object filesObj = query.get("files");
//            if (filesObj instanceof List) {
//                @SuppressWarnings("unchecked")
//                List<Object> filesList = (List<Object>) filesObj;
//                for (Object fileObj : filesList) {
//                    if (fileObj instanceof Map) {
//                        @SuppressWarnings("unchecked")
//                        Map<String, Object> fileMap = (Map<String, Object>) fileObj;
//                        ChatItemValueItemFileInfo fileInfo = new ChatItemValueItemFileInfo();
//                        fileInfo.setUrl(getStringParam(fileMap, "url", ""));
//                        fileInfo.setName(getStringParam(fileMap, "name", ""));
//                        fileInfo.setType(getStringParam(fileMap, "type", ""));
//                        files.add(fileInfo);
//                    }
//                }
//            }
//        }
//        return files;
//    }

    /**
     * 过滤数据集引用
     */
    private String filterDatasetQuote(List<SearchDataResponseItemType> quoteQA, SystemModelDTO modelConfig, String quoteTemplate) {
        if (quoteQA == null || quoteQA.isEmpty()) {
            return "";
        }

        // 按最大字符数过滤搜索结果
        List<SearchDataResponseItemType> filterQuoteQA =
                WorkflowUtil.filterSearchResultsByMaxChars(quoteQA, modelConfig.getQuoteMaxToken());

        String datasetQuoteText;
        if (!filterQuoteQA.isEmpty()) {
            datasetQuoteText = IntStream.range(0, filterQuoteQA.size())
                    .mapToObj(index -> {
                        SearchDataResponseItemType item = filterQuoteQA.get(index);
                        return getValue(item, index, quoteTemplate).trim();
                    })
                    .collect(Collectors.joining("\n------\n"));
        } else {
            datasetQuoteText = "";
        }

        return datasetQuoteText;
    }

    /**
     * 获取格式化的引用值
     * 对应 TypeScript 中的内部 getValue 函数
     *
     * @param item 搜索数据响应项
     * @param index 索引
     * @param quoteTemplate 引用模板
     * @return 格式化后的引用文本
     */
    private static String getValue(SearchDataResponseItemType item, int index, String quoteTemplate) {
        Map<String, Object> variables = new HashMap<>();
        variables.put("id", item.getId());
        variables.put("q", item.getQ());
        variables.put("a", item.getA() != null ? item.getA() : "");
        variables.put("updateTime", formatTime2YMDHM(item.getUpdateTime()));
        variables.put("source", item.getSourceName());
        variables.put("sourceId", item.getSourceId() != null ? item.getSourceId() : "");
        variables.put("index", index + 1);

        return WorkflowUtil.replaceVariable(quoteTemplate, variables).toString();
    }

    /**
     * 多输入结果类
     */
    private record MultiInputResult(String documentQuoteText, List<ChatItemValueItemFileInfo> userFiles) {

    }

    /**
     * 获取多输入
     */
    private MultiInputResult getMultiInput(
            List<ChatItemType> histories,
            List<ChatItemValueItemFileInfo> inputFiles,
            List<String> fileLinks,
            String stringQuoteText,
            ModuleDispatchProps props) {

        // 旧版本适配
        if (StringUtils.hasText(stringQuoteText)) {
            return new MultiInputResult(stringQuoteText, inputFiles != null ? inputFiles : new ArrayList<>());
        }

        // 没有引用文件参考
        if (fileLinks == null || fileLinks.isEmpty()) {
            return new MultiInputResult("", inputFiles != null ? inputFiles : new ArrayList<>());
        }

        // 处理文件链接
        String documentText = processFileLinks(fileLinks, props);
        List<ChatItemValueItemFileInfo> userFiles = fileLinks.stream()
                .map(this::parseUrlToFileType)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        return new MultiInputResult(documentText, userFiles);
    }

    /**
     * 处理文件链接
     */
    private String processFileLinks(List<String> fileLinks, ModuleDispatchProps props) {
        // 这里应该实现文件内容读取逻辑
        // 暂时返回空字符串
        return "";
    }

    /**
     * 解析URL为文件类型
     */
    private ChatItemValueItemFileInfo parseUrlToFileType(String url) {
        if (!StringUtils.hasText(url)) {
            return null;
        }

        ChatItemValueItemFileInfo fileInfo = new ChatItemValueItemFileInfo();
        fileInfo.setUrl(url);

        // 从URL提取文件名和类型
        String fileName = url.substring(url.lastIndexOf('/') + 1);
        fileInfo.setName(fileName);

        if (fileName.contains(".")) {
            String extension = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
            fileInfo.setType(extension);
        }

        return fileInfo;
    }

    /**
     * 计算最大token
     */
    private int computeMaxToken(SystemModelDTO modelConfig, Integer maxToken) {
        int configMaxContext = modelConfig.getMaxContext() != null ? modelConfig.getMaxContext() : 4096;
        int requestMaxToken = maxToken != null ? maxToken : 2000;

        // 确保不超过模型的最大上下文
        return Math.min(requestMaxToken, configMaxContext / 2);
    }

    /**
     * 获取聊天消息
     */
    private List<Message> getChatMessages(
            SystemModelDTO modelConfig,
            int maxTokens,
            String aiChatQuoteRole,
            String quotePrompt,
            String datasetQuoteText,
            boolean useDatasetQuote,
            List<ChatItemType> histories,
            String systemPrompt,
            String userChatInput,
            List<ChatItemValueItemFileInfo> userFiles,
            String documentQuoteText) {

        List<Message> messages = new ArrayList<>();

        // 构建系统提示
        StringBuilder systemPromptBuilder = new StringBuilder();

        // 添加默认系统提示
        if (modelConfig.getDefaultSystemChatPrompt() != null) {
            systemPromptBuilder.append(modelConfig.getDefaultSystemChatPrompt());
        }

        // 添加自定义系统提示
        if (StringUtils.hasText(systemPrompt)) {
            if (!systemPromptBuilder.isEmpty()) {
                systemPromptBuilder.append("\n\n===---===---===\n\n");
            }
            systemPromptBuilder.append(systemPrompt);
        }

        // 添加数据集引用（如果是system角色）
        if (useDatasetQuote && "system".equals(aiChatQuoteRole) && StringUtils.hasText(datasetQuoteText)) {
            if (!systemPromptBuilder.isEmpty()) {
                systemPromptBuilder.append("\n\n===---===---===\n\n");
            }

            String quoteTemplate = StringUtils.hasText(quotePrompt) ? quotePrompt :
                    "请根据以下参考信息回答问题：\n{{quote}}";
            systemPromptBuilder.append(quoteTemplate.replace("{{quote}}", datasetQuoteText));
        }

        // 添加文档引用
        if (StringUtils.hasText(documentQuoteText)) {
            if (!systemPromptBuilder.isEmpty()) {
                systemPromptBuilder.append("\n\n===---===---===\n\n");
            }
            systemPromptBuilder.append("参考文档：\n").append(documentQuoteText);
        }

        // 添加系统消息
        if (!systemPromptBuilder.isEmpty()) {
            messages.add(new SystemMessage(systemPromptBuilder.toString()));
        }

        // 添加历史消息
        if (histories != null) {
            for (ChatItemType history : histories) {
                if (ChatRoleEnum.HUMAN.equals(history.getObj())) {
                    String content = extractTextFromChatValue(history.getValue());
                    if (StringUtils.hasText(content)) {
                        messages.add(new UserMessage(content));
                    }
                } else if (ChatRoleEnum.AI.equals(history.getObj())) {
                    String content = extractTextFromChatValue(history.getValue());
                    if (StringUtils.hasText(content)) {
                        messages.add(new AssistantMessage(content));
                    }
                }
            }
        }

        // 构建用户输入
        String finalUserInput = userChatInput;

        // 如果数据集引用是user角色，添加到用户输入中
        if (useDatasetQuote && "user".equals(aiChatQuoteRole) && StringUtils.hasText(datasetQuoteText)) {
            String quoteTemplate = StringUtils.hasText(quotePrompt) ? quotePrompt :
                    "参考信息：\n{{quote}}\n\n问题：{{question}}";
            finalUserInput = quoteTemplate
                    .replace("{{quote}}", datasetQuoteText)
                    .replace("{{question}}", userChatInput);
        }

        // 添加当前用户消息
        if (StringUtils.hasText(finalUserInput)) {
            messages.add(new UserMessage(finalUserInput));
        }

        return messages;
    }

    /**
     * 从聊天值中提取文本
     */
    private String extractTextFromChatValue(Object value) {
        if (value == null) {
            return "";
        }

        if (value instanceof String) {
            return (String) value;
        }

        if (value instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> valueList = (List<Object>) value;
            StringBuilder text = new StringBuilder();

            for (Object item : valueList) {
                if (item instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> itemMap = (Map<String, Object>) item;
                    String type = getStringParam(itemMap, "type", "");

                    if ("text".equals(type)) {
                        String textContent = getStringParam(itemMap, "text", "");
                        if (StringUtils.hasText(textContent)) {
                            if (text.length() > 0) {
                                text.append("\n");
                            }
                            text.append(textContent);
                        }
                    }
                }
            }

            return text.toString();
        }

        return value.toString();
    }

    /**
     * 提取推理文本
     */
    private String extractReasoningText(ChatResponse response, Boolean aiChatReasoning) {
        if (!Boolean.TRUE.equals(aiChatReasoning)) {
            return null;
        }

        // 这里应该从响应中提取推理内容
        // Spring AI可能不直接支持推理内容，需要根据具体实现调整
        return null;
    }

    /**
     * 计算输入token数
     */
    private int calculateInputTokens(List<Message> messages) {
        // 简单估算：每个字符约0.75个token
        int totalChars = messages.stream()
                .mapToInt(msg -> msg.getText().length())
                .sum();
        return (int) (totalChars * 0.75);
    }

    /**
     * 计算输出token数
     */
    private int calculateOutputTokens(String answerText, String reasoningText) {
        int totalChars = 0;
        if (StringUtils.hasText(answerText)) {
            totalChars += answerText.length();
        }
        if (StringUtils.hasText(reasoningText)) {
            totalChars += reasoningText.length();
        }
        return (int) (totalChars * 0.75);
    }

    /**
     * 转换为聊天历史
     */
    private List<ChatItemType> convertToChatHistory(List<Message> messages) {
        List<ChatItemType> chatHistory = new ArrayList<>();

        for (Message message : messages) {
            ChatItemType item = new ChatItemType();

            if (message instanceof SystemMessage) {
                // 系统消息通常不加入历史记录
                continue;
            } else if (message instanceof UserMessage) {
                item.setObj(ChatRoleEnum.HUMAN);
            } else if (message instanceof AssistantMessage) {
                item.setObj(ChatRoleEnum.AI);
            }

            // 构建聊天值
            List<ChatItemValueItemType> valueItems = new ArrayList<>();
            ChatItemValueItemType textItem = new ChatItemValueItemType();
            textItem.setType("text");
            ChatItemValueItemTextInfo info = new ChatItemValueItemTextInfo(message.getText());
            textItem.setText(info);
            valueItems.add(textItem);

            item.setValue(valueItems);
            chatHistory.add(item);
        }

        return chatHistory;
    }
}
