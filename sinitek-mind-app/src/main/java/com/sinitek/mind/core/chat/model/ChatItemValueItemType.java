package com.sinitek.mind.core.chat.model;

import com.sinitek.mind.core.workflow.model.WorkflowInteractiveResponseType;
import lombok.Data;

import java.util.List;

@Data
public class ChatItemValueItemType {

    private String type = "text";

    private ChatItemValueItemTextInfo text;

    private ChatItemValueItemFileInfo file;

    private ChatItemValueItemReasoningInfo reasoning;

    private List<ToolModuleResponseItemType> tools;

    private WorkflowInteractiveResponseType interactive;

}
