package com.sinitek.mind.core.ai.enumerate;

import lombok.Getter;

/**
 * 聊天消息类型枚举
 * 对应 TypeScript 中的 ChatMessageTypeEnum
 */
@Getter
public enum ChatMessageTypeEnum {
    TEXT("text"),
    IMAGE_URL("image_url");
    
    private final String value;
    
    ChatMessageTypeEnum(String value) {
        this.value = value;
    }

    public static ChatMessageTypeEnum fromValue(String value) {
        for (ChatMessageTypeEnum type : values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        throw new IllegalArgumentException("Unknown ChatMessageTypeEnum value: " + value);
    }
}