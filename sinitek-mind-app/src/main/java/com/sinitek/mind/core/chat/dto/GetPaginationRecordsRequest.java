package com.sinitek.mind.core.chat.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * Get Pagination Chat Records Request DTO
 */
@Data
@Schema(description = "Get Pagination Chat Records Request")
public class GetPaginationRecordsRequest {

    @Schema(description = "Application ID", example = "app_123456789", required = true)
    @NotBlank(message = "Application ID cannot be empty")
    private String appId;

    @Schema(description = "Chat Session ID", example = "chat_987654321")
    private String chatId;

    @Schema(description = "Records per page", example = "10", required = true)
    @NotNull(message = "Page size cannot be null")
    @Min(value = 1, message = "Page size must be at least 1")
    @Max(value = 100, message = "Page size cannot exceed 100")
    private Integer pageSize = 10;

    @Schema(description = "Offset, choose one between offset and pageNum", example = "0")
    @Min(value = 0, message = "Offset must be non-negative")
    private Integer offset;

    @Schema(description = "Page number, choose one between offset and pageNum", example = "1")
    @Min(value = 1, message = "Page number must be at least 1")
    private Integer pageNum;

    @Schema(description = "Whether to load custom feedbacks", example = "false")
    private Boolean loadCustomFeedbacks = false;

    @Schema(description = "Chat type", example = "normal", allowableValues = {"normal", "outLink", "team"})
    private String type = "normal";

    @Schema(description = "Share link ID (required for outLink chat)", example = "share_123456789")
    private String shareId;

    @Schema(description = "OutLink user ID (required for outLink chat)", example = "outlink_user_123")
    private String outLinkUid;

    @Schema(description = "Team ID (required for team chat)", example = "team_123456789")
    private String teamId;

    @Schema(description = "Team access token (required for team chat)", example = "team_token_xxx")
    private String teamToken;
}