package com.sinitek.mind.core.chat.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.util.TikTokenUtil;
import com.sinitek.mind.core.ai.model.*;
import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.model.AppChatConfigType;
import com.sinitek.mind.core.app.util.ImageUtils;
import com.sinitek.mind.core.chat.entity.Chat;
import com.sinitek.mind.core.chat.enumerate.ChatCompletionRequestMessageRoleEnum;
import com.sinitek.mind.core.chat.enumerate.ChatItemValueType;
import com.sinitek.mind.core.chat.enumerate.ChatRoleEnum;
import com.sinitek.mind.core.chat.model.AIChatItemValueItemType;
import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemType;
import com.sinitek.mind.core.workflow.model.FlowNodeInputItemType;
import com.sinitek.mind.core.workflow.model.StoreNodeItemType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 聊天初始化工具类
 */
@Slf4j
@Component
public class ChatUtil {

    @Value("${multiple.data.to.base64:false}")
    private static boolean multipleDataToBase64;

    private static RestTemplate restTemplate = null;
    private final ObjectMapper objectMapper = new ObjectMapper();

    public ChatUtil(RestTemplate restTemplate) {
        ChatUtil.restTemplate = restTemplate;
    }

    /**
     * 获取应用聊天配置
     * 类似于TypeScript中的getAppChatConfig函数
     */
    public static AppChatConfigType getAppChatConfig(App app, Chat chat) {
        if (app == null) {
            return null;
        }

        AppChatConfigType chatConfig = app.getChatConfig();
        if (chatConfig == null) {
            chatConfig = new AppChatConfigType();
        }

        // 如果存在聊天记录，使用聊天记录中的配置覆盖应用配置
        if (chat != null) {
            // 使用聊天中存储的变量列表
            if (!CollectionUtils.isEmpty(chat.getVariableList())) {
                // 这里可以根据需要处理变量列表
                log.debug("使用聊天中的变量列表: {}", chat.getVariableList().size());
            }
            
            // 使用聊天中存储的欢迎文本
            if (StringUtils.hasText(chat.getWelcomeText())) {
                // 这里可以根据需要设置欢迎文本
                log.debug("使用聊天中的欢迎文本: {}", chat.getWelcomeText());
            }
        }

        return chatConfig;
    }

    /**
     * 合并聊天历史记录
     * 将两个聊天历史记录列表合并，并按照系统消息优先的规则排序
     *
     * @param histories1 第一个聊天历史记录列表
     * @param histories2 第二个聊天历史记录列表
     * @return 合并并排序后的聊天历史记录列表
     */
    public static List<ChatItemType> concatHistories(List<ChatItemType> histories1, List<ChatItemType> histories2) {
        List<ChatItemType> newHistories = new ArrayList<>();

        if (histories1 != null) {
            newHistories.addAll(histories1);
        }

        if (histories2 != null) {
            newHistories.addAll(histories2);
        }

        // 使用Lambda表达式进行排序
        newHistories.sort((a, b) -> {
            if (a.getObj() == ChatRoleEnum.SYSTEM) {
                return -1;
            }
            return 1;
        });

        return newHistories;
    }

    /**
     * 从应用模块中获取聊天模型名称列表
     * 类似于TypeScript中的getChatModelNameListByModules函数
     */
    public static List<String> getChatModelNameListByModules(List<StoreNodeItemType> modules) {
        List<String> modelNames = new ArrayList<>();
        
        if (CollectionUtils.isEmpty(modules)) {
            return modelNames;
        }

        // 遍历模块，查找聊天相关的节点
        for (StoreNodeItemType module : modules) {
            if (module == null || module.getInputs() == null) {
                continue;
            }

            // 查找模型相关的输入
            for (FlowNodeInputItemType input : module.getInputs()) {
                if (input != null && "model".equals(input.getKey()) && input.getValue() != null) {
                    String modelName = input.getValue().toString();
                    if (StringUtils.hasText(modelName) && !modelNames.contains(modelName)) {
                        modelNames.add(modelName);
                    }
                }
            }
        }

        return modelNames;
    }

    /**
     * 获取引导模块
     * 类似于TypeScript中的getGuideModule函数
     */
    public static StoreNodeItemType getGuideModule(List<StoreNodeItemType> modules) {
        if (CollectionUtils.isEmpty(modules)) {
            return null;
        }

        return modules.stream()
                .filter(module -> "systemConfig".equals(module.getFlowNodeType()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取插件输入
     */
    public static List<FlowNodeInputItemType> getPluginInputs(App app, Chat chat) {
        List<FlowNodeInputItemType> pluginInputs = new ArrayList<>();
        
        // 优先使用聊天中的插件输入
        if (chat != null && chat.getPluginInputs() != null) {
            pluginInputs.addAll(chat.getPluginInputs());
            return pluginInputs;
        }

        // 从应用模块中查找插件输入节点
        if (app != null && !CollectionUtils.isEmpty(app.getModules())) {
            StoreNodeItemType pluginInputNode = app.getModules().stream()
                    .filter(node -> "pluginInput".equals(node.getFlowNodeType()))
                    .findFirst()
                    .orElse(null);
            
            if (pluginInputNode != null && !CollectionUtils.isEmpty(pluginInputNode.getInputs())) {
                pluginInputs.addAll(pluginInputNode.getInputs());
            }
        }
        
        return pluginInputs;
    }

    /**
     * 验证聊天权限
     */
    public static boolean validateChatPermission(App app, Chat chat, String tmbId) {
        if (chat == null) {
            return true; // 没有聊天记录，允许访问
        }

        // 如果应用有管理权限，允许访问
        if (app != null && app.getDefaultPermission() != null && app.getDefaultPermission() >= 30) {
            return true;
        }

        // 检查是否是聊天创建者
        return tmbId != null && tmbId.equals(chat.getTmbId());
    }

    /**
     * 移除空的用户输入项
     * @param input 用户聊天项值列表
     * @return 过滤后的非空用户聊天项值列表
     */
    public static List<ChatItemValueItemType> removeEmptyUserInput(List<ChatItemValueItemType> input) {
        if (input == null) {
            return new ArrayList<>();
        }

        return input.stream()
                .filter(item -> {
                    // 如果是文本类型且内容为空或只有空白字符，则过滤掉
                    if (ChatItemValueType.TEXT.getValue().equals(item.getType())
                            && (item.getText() == null
                            || item.getText().getContent() == null
                            || item.getText().getContent().trim().isEmpty())) {
                        return false;
                    }

                    // 如果是文件类型且没有URL，则过滤掉
                    if (ChatItemValueType.FILE.getValue().equals(item.getType())
                            && (item.getFile() == null
                            || item.getFile().getUrl() == null
                            || item.getFile().getUrl().trim().isEmpty())) {
                        return false;
                    }

                    return true;
                })
                .toList();
    }


    /**
     * 从聊天消息中获取聊天标题
     */
    public static String getChatTitleFromChatMessage(ChatItemType message) {
        return getChatTitleFromChatMessage(message, "新对话");
    }

    /**
     * 从聊天消息中获取聊天标题
     */
    public static String getChatTitleFromChatMessage(ChatItemType message, String defaultValue) {
        if (message == null || message.getValue() == null) {
            return defaultValue;
        }

        // 查找文本类型的消息
        for (ChatItemValueItemType item : message.getValue()) {
            if (ChatItemValueType.TEXT.getValue().equals(item.getType()) &&
                    item.getText() != null &&
                    StringUtils.hasText(item.getText().getContent())) {
                String content = item.getText().getContent();
                // 截取前20个字符作为标题
                return content.length() > 20 ? content.substring(0, 20) : content;
            }
        }

        return defaultValue;
    }

    /**
     * 移除数据集中的引用文本
     * @param text 输入的文本
     * @param retainDatasetCite 是否保留数据集引用
     * @return 处理后的文本
     */
    public static String removeDatasetCiteText(String text, boolean retainDatasetCite) {
        if (text == null) {
            return null;
        }
        if (retainDatasetCite) {
            // 移除 [id](CITE) 或 【id】(CITE) 格式的引用
            return text.replaceAll("[\\[【]id[\\]】]\\(CITE\\)", "");
        } else {
            // 移除 [24位十六进制字符](任意内容) 或 【24位十六进制字符】(任意内容) 格式的引用
            text = text.replaceAll("[\\[【]([a-f0-9]{24})[\\]】](?:\\([^)]*\\)?)?", "");
            // 移除 [id](CITE) 或 【id】(CITE) 格式的引用
            return text.replaceAll("[\\[【]id[\\]】]\\(CITE\\)", "");
        }
    }

    /**
     * 返回值可能为List<AIChatItemValueItemType>或者String
     * @param value List<AIChatItemValueItemType>或者String
     * @param retainCite
     * @return
     */
    public static Object removeAIResponseCite(Object value, boolean retainCite) {
        if (retainCite) {
            return value;
        }

        if (value instanceof String) {
            return removeDatasetCiteText((String) value, false);
        } else if (value instanceof List) {
            List<AIChatItemValueItemType> list = (List<AIChatItemValueItemType>) value;
            List<AIChatItemValueItemType> newList = new ArrayList<>();
            for (AIChatItemValueItemType item : list) {
                AIChatItemValueItemType newItem = item;
                if (item.getText() != null && item.getText().getContent() != null) {
                    String newContent = removeDatasetCiteText(item.getText().getContent(), false);
                    newItem.getText().setContent(newContent);
                } else if (item.getReasoning() != null && item.getReasoning().getContent() != null) {
                    String newContent = removeDatasetCiteText(item.getReasoning().getContent(), false);
                    newItem.getReasoning().setContent(newContent);
                }
                newList.add(newItem);
            }
            return newList;
        }
        return value;
    }

    /**
     * 加载请求消息
     *
     * @param messages 消息列表
     * @param useVision 是否使用视觉功能，默认true
     * @param origin 原始域名
     * @return 处理后的消息列表
     */
    public static CompletableFuture<List<ChatCompletionMessageParam>> loadRequestMessages(
            List<ChatCompletionMessageParam> messages,
            Boolean useVision,
            String origin) {

        if (useVision == null) {
            useVision = true;
        }

        if (messages == null || messages.isEmpty()) {
            return CompletableFuture.failedFuture(
                    new IllegalArgumentException("消息列表不能为空")
            );
        }

        Boolean finalUseVision1 = useVision;
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 合并相邻相同角色的消息
                List<ChatCompletionMessageParam> mergedMessages = mergeMessages(messages);

                // 处理每个消息
                List<CompletableFuture<ChatCompletionMessageParam>> futures = new ArrayList<>();

                for (int i = 0; i < mergedMessages.size(); i++) {
                    final int index = i;
                    final ChatCompletionMessageParam item = mergedMessages.get(i);
                    final Boolean finalUseVision = finalUseVision1;

                    CompletableFuture<ChatCompletionMessageParam> future = CompletableFuture.supplyAsync(() -> {
                        try {
                            return processMessage(item, index, mergedMessages, finalUseVision, origin);
                        } catch (Exception e) {
                            log.error("处理消息时发生错误", e);
                            return null;
                        }
                    });

                    futures.add(future);
                }

                // 等待所有异步任务完成
                List<ChatCompletionMessageParam> loadMessages = futures.stream()
                        .map(CompletableFuture::join)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                return loadMessages;

            } catch (Exception e) {
                log.error("加载消息时发生错误", e);
                throw new RuntimeException("加载消息失败", e);
            }
        });
    }

    /**
     * 处理单个消息
     */
    private static ChatCompletionMessageParam processMessage(
            ChatCompletionMessageParam item,
            int index,
            List<ChatCompletionMessageParam> mergedMessages,
            boolean useVision,
            String origin) {

        String role = item.getRole();

        if (ChatCompletionRequestMessageRoleEnum.SYSTEM.getValue().equals(role)) {
            return (ChatCompletionMessageParam) parseSystemMessage(item.getContent());
        } else if (ChatCompletionRequestMessageRoleEnum.USER.getValue().equals(role)) {
            return (ChatCompletionMessageParam) parseUserContent(item.getContent(), useVision, origin);
        } else if (ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue().equals(role)) {
            String parseContent = parseAssistantContent(item.getContent());
            // 如果内容为空，且前后不再是 assistant，需要补充成 null，避免丢失 user-assistant 的交互
            ChatCompletionMessageParam lastItem = mergedMessages.get(index - 1);
            ChatCompletionMessageParam nextItem = mergedMessages.get(index + 1);
            String formatContent = "";
            if (parseContent.isEmpty() && lastItem.getRole().equals(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue()) ||
                    nextItem.getRole().equals(ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue())) {
                formatContent = "";
            } else {
                formatContent = parseContent;
            }
            if (formatContent.isEmpty()) {
                return null;
            }
            ChatCompletionMessageParam chatCompletionMessageParam = formatAssistantItem(item);
            chatCompletionMessageParam.setContent(formatContent);
            return chatCompletionMessageParam;

        } else {
            return item;
        }
    }

    /**
     * 解析系统消息内容
     */
    private static Object parseSystemMessage(Object content) {
        if (content instanceof String strContent) {
            if (!StringUtils.hasText(strContent)) {
                return null;
            }
            return ImageUtils.addEndpointToImageUrl(strContent);
        }

        if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listContent = (List<Object>) content;

            String arrayContent = listContent.stream()
                    .filter(item -> item instanceof Map)
                    .map(item -> {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> map = (Map<String, Object>) item;
                        Object text = map.get("text");
                        return text != null ? ImageUtils.addEndpointToImageUrl(text.toString()) : "";
                    })
                    .filter(StringUtils::hasText)
                    .collect(Collectors.joining("\n"));

            return StringUtils.hasText(arrayContent) ? arrayContent : null;
        }

        return content;
    }

    /**
     * 解析用户内容
     */
    private static List<ChatCompletionContentPart> parseUserContent(
            Object content,
            boolean useVision,
            String origin) {

        if (content == null) {
            return null;
        }

        if (content instanceof String strContent) {
            if (!StringUtils.hasText(strContent)) {
                return null;
            }

            List<ChatCompletionContentPart> imageContent = loadUserContentImage(
                    parseStringWithImages(strContent, useVision), origin);

            return imageContent.isEmpty() ? null : imageContent;
        }

        if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listContent = (List<Object>) content;

            List<ChatCompletionContentPart> result = listContent.stream()
                    .map(item1 -> convertToContentPart(item1))
                    .filter(Objects::nonNull)
                    .filter(item -> {
                        if ("text".equals(item.getType())) {
                            ChatCompletionContentPartText textPart = (ChatCompletionContentPartText) item;
                            return StringUtils.hasText(textPart.getText());
                        }
                        if ("file_url".equals(item.getType())) {
                            return false; // LLM不支持file_url
                        }
                        if ("image_url".equals(item.getType())) {
                            if (!useVision) {
                                return false; // 关闭视觉功能，移除image_url
                            }
                            ChatCompletionContentPartImage imagePart = (ChatCompletionContentPartImage) item;
                            return imagePart.getImageUrl() != null &&
                                    StringUtils.hasText(imagePart.getImageUrl().getUrl());
                        }
                        return true;
                    })
                    .collect(Collectors.toList());

            List<ChatCompletionContentPart> imageContent = loadUserContentImage(result, origin);
            return imageContent.isEmpty() ? null : imageContent;
        }

        return null;
    }

    /**
     * 加载用户内容图片
     */
    private static List<ChatCompletionContentPart> loadUserContentImage(
            List<ChatCompletionContentPart> content,
            String origin) {

        return content.stream()
                .map(item -> {
                    if ("image_url".equals(item.getType())) {
                        ChatCompletionContentPartImage imagePart = (ChatCompletionContentPartImage) item;
                        return processImageUrl(imagePart, origin);
                    }
                    return item;
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 处理图片URL
     */
    private static ChatCompletionContentPartImage processImageUrl(
            ChatCompletionContentPartImage imagePart,
            String origin) {

        ChatCompletionContentPartImage.ImageUrl imageUrl = imagePart.getImageUrl();
        if (imageUrl == null || !StringUtils.hasText(imageUrl.getUrl())) {
            return null;
        }

        // 移除URL原始域名
        String imgUrl = imageUrl.getUrl();
        if (StringUtils.hasText(origin) && imgUrl.startsWith(origin)) {
            imgUrl = imgUrl.replace(origin, "");
        }

        // base64图片
        if (imgUrl.startsWith("data:image/")) {
            return imagePart;
        }

        try {
            // 如果imgUrl是本地路径，从本地加载图片，并设置url为base64
            if (imgUrl.startsWith("/") || multipleDataToBase64) {
                String base64 = getImageBase64(imgUrl);
                if (StringUtils.hasText(base64)) {
                    ChatCompletionContentPartImage.ImageUrl newImageUrl =
                            new ChatCompletionContentPartImage.ImageUrl(base64, imageUrl.getDetail());
                    return new ChatCompletionContentPartImage(newImageUrl);
                }
            }

            // 检查图片是否可以被访问
            if (isImageAccessible(imgUrl)) {
                return imagePart;
            } else {
                log.info("过滤无效图片: {}", imgUrl);
                return null;
            }

        } catch (Exception e) {
            log.warn("过滤无效图片: {}", imgUrl, e);
            return null;
        }
    }

    /**
     * 检查图片是否可访问
     */
    private static boolean isImageAccessible(String imgUrl) {
        try {
            ResponseEntity<Void> response = restTemplate.exchange(
                    imgUrl, HttpMethod.HEAD, null, Void.class);

            return response.getStatusCode().is2xxSuccessful();

        } catch (Exception e) {
            // 如果是405错误（方法不允许），认为图片可访问
            if (e.getMessage() != null && e.getMessage().contains("405")) {
                return true;
            }
            return false;
        }
    }

    /**
     * 获取图片的base64编码
     */
    private static String getImageBase64(String url) {
        try {
            log.debug("加载图片到base64: {}", url);

            ResponseEntity<byte[]> response = restTemplate.exchange(
                    url, HttpMethod.GET, null, byte[].class);

            if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
                byte[] imageBytes = response.getBody();
                String base64 = Base64.getEncoder().encodeToString(imageBytes);

                // 猜测图片类型
                String imageType = guessImageType(base64);

                return String.format("data:%s;base64,%s", imageType, base64);
            }

        } catch (Exception e) {
            log.error("获取图片base64失败: {}", url, e);
        }

        return null;
    }

    /**
     * 猜测图片类型
     */
    private static String guessImageType(String base64) {
        if (base64.startsWith("/")) {
            return "image/jpeg";
        } else if (base64.startsWith("i")) {
            return "image/png";
        } else if (base64.startsWith("R")) {
            return "image/gif";
        }
        return "image/jpeg"; // 默认
    }

    /**
     * 解析字符串中的图片
     */
    private static List<ChatCompletionContentPart> parseStringWithImages(String input, boolean useVision) {
        if (!useVision || input.length() > 500) {
            return List.of(new ChatCompletionContentPartText(input));
        }

        // 正则表达式匹配图片URL
        Pattern imageRegex = Pattern.compile(
                "(https?://[^\\s/$.?#].[^\\s]*\\.(?:png|jpe?g|gif|webp|bmp|tiff?|svg|ico|heic|avif))",
                Pattern.CASE_INSENSITIVE
        );

        List<ChatCompletionContentPart> result = new ArrayList<>();

        // 提取所有HTTPS图片URL并添加到result开头
        Matcher matcher = imageRegex.matcher(input);
        Set<String> httpsImages = new LinkedHashSet<>();
        while (matcher.find()) {
            httpsImages.add(matcher.group(1));
        }

        for (String url : httpsImages) {
            ChatCompletionContentPartImage.ImageUrl imageUrl =
                    new ChatCompletionContentPartImage.ImageUrl(url);
            result.add(new ChatCompletionContentPartImage(imageUrl));
        }

        // 图片太多返回文本
        if (httpsImages.size() > 4) {
            return List.of(new ChatCompletionContentPartText(input));
        }

        // 添加原始input作为文本
        result.add(new ChatCompletionContentPartText(input));
        return result;
    }

    /**
     * 格式化助手项目
     */
    private static ChatCompletionMessageParam formatAssistantItem(ChatCompletionMessageParam item) {
        ChatCompletionMessageParam result = new ChatCompletionMessageParam();
        result.setRole(item.getRole());
        result.setContent(item.getContent());
        result.setFunctionCall(item.getFunctionCall());
        result.setName(item.getName());
        // TODO result.setRefusal(item.getRefusal());
        result.setToolCalls(item.getToolCalls());
        return result;
    }

    /**
     * 将对象转换为ChatCompletionContentPart
     */
    private static ChatCompletionContentPart convertToContentPart(Object item) {
        if (!(item instanceof Map)) {
            return null;
        }

        @SuppressWarnings("unchecked")
        Map<String, Object> map = (Map<String, Object>) item;
        String type = (String) map.get("type");

        if ("text".equals(type)) {
            String text = (String) map.get("text");
            return new ChatCompletionContentPartText(text);
        } else if ("image_url".equals(type)) {
            @SuppressWarnings("unchecked")
            Map<String, Object> imageUrlMap = (Map<String, Object>) map.get("image_url");
            if (imageUrlMap != null) {
                String url = (String) imageUrlMap.get("url");
                String detail = (String) imageUrlMap.get("detail");
                ChatCompletionContentPartImage.ImageUrl imageUrl =
                        new ChatCompletionContentPartImage.ImageUrl(url, detail);
                return new ChatCompletionContentPartImage(imageUrl);
            }
        } else if ("file_url".equals(type)) {
            String name = (String) map.get("name");
            String url = (String) map.get("url");
            return new ChatCompletionContentPartFile(name, url);
        }

        return null;
    }

    /**
     * 解析助手内容
     */
    private static String parseAssistantContent(Object content) {
        if (content instanceof String) {
            return StringUtils.hasText((String) content) ? (String) content : "";
        }

        if (content == null) {
            return "";
        }

        if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listContent = (List<Object>) content;

            List<String> textResults = listContent.stream()
                    .filter(item -> item instanceof Map)
                    .map(item -> {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> map = (Map<String, Object>) item;
                        if ("text".equals(map.get("type"))) {
                            Object text = map.get("text");
                            return text != null ? text.toString() : null;
                        }
                        return null;
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            return textResults.isEmpty() ? "" : String.join("\n", textResults);
        }

        return "";
    }

    /**
     * 合并相邻相同角色的消息，只保留一个 role， content 变成数组。 assistant 的话，工具调用不合并。
     */
    private static List<ChatCompletionMessageParam> mergeMessages(List<ChatCompletionMessageParam> messages) {
        List<ChatCompletionMessageParam> mergedMessages = new ArrayList<>();

        for (ChatCompletionMessageParam currentMessage : messages) {
            if (mergedMessages.isEmpty()) {
                mergedMessages.add(currentMessage);
                continue;
            }

            ChatCompletionMessageParam lastMessage = mergedMessages.get(mergedMessages.size() - 1);

            if (canMergeMessages(lastMessage, currentMessage)) {
                mergeMessageContent(lastMessage, currentMessage);
            } else {
                mergedMessages.add(currentMessage);
            }
        }

        return mergedMessages;
    }

    /**
     * 判断是否可以合并消息
     */
    private static boolean canMergeMessages(ChatCompletionMessageParam lastMessage, ChatCompletionMessageParam currentMessage) {
        String lastRole = lastMessage.getRole();
        String currentRole = currentMessage.getRole();

        if (!lastRole.equals(currentRole)) {
            return false;
        }

        // System消息可以合并
        if (ChatCompletionRequestMessageRoleEnum.SYSTEM.getValue().equals(lastRole)) {
            return true;
        }

        // User消息可以合并
        if (ChatCompletionRequestMessageRoleEnum.USER.getValue().equals(lastRole)) {
            return true;
        }

        // Assistant消息在特定条件下可以合并
        if (ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue().equals(lastRole)) {
            return canMergeAssistantMessages(lastMessage, currentMessage);
        }

        return false;
    }

    /**
     * 判断是否可以合并助手消息
     */
    private static boolean canMergeAssistantMessages(ChatCompletionMessageParam lastMessage, ChatCompletionMessageParam currentMessage) {
        // Content不为空的对象，或者是交互节点
        boolean lastHasContent = hasValidContent(lastMessage) || lastMessage.getInteractive() != null;
        boolean currentHasContent = hasValidContent(currentMessage) || currentMessage.getInteractive() != null;

        return lastHasContent && currentHasContent;
    }

    /**
     * 检查消息是否有有效内容
     */
    private static boolean hasValidContent(ChatCompletionMessageParam message) {
        Object content = message.getContent();
        return content instanceof String || content instanceof List;
    }

    /**
     * 合并消息内容
     */
    private static void mergeMessageContent(ChatCompletionMessageParam lastMessage, ChatCompletionMessageParam currentMessage) {
        String role = lastMessage.getRole();

        if (ChatCompletionRequestMessageRoleEnum.SYSTEM.getValue().equals(role)) {
            mergeSystemMessageContent(lastMessage, currentMessage);
        } else if (ChatCompletionRequestMessageRoleEnum.USER.getValue().equals(role)) {
            mergeUserMessageContent(lastMessage, currentMessage);
        } else if (ChatCompletionRequestMessageRoleEnum.ASSISTANT.getValue().equals(role)) {
            mergeAssistantMessageContent(lastMessage, currentMessage);
        }
    }

    /**
     * 合并系统消息内容
     */
    private static void mergeSystemMessageContent(ChatCompletionMessageParam lastMessage, ChatCompletionMessageParam currentMessage) {
        List<Map<String, Object>> lastContent = convertToTextContentList(lastMessage.getContent());
        List<Map<String, Object>> currentContent = convertToTextContentList(currentMessage.getContent());

        lastContent.addAll(currentContent);
        lastMessage.setContent(lastContent);
    }

    /**
     * 合并用户消息内容
     */
    private static void mergeUserMessageContent(ChatCompletionMessageParam lastMessage, ChatCompletionMessageParam currentMessage) {
        List<Object> lastContent = convertToContentList(lastMessage.getContent());
        List<Object> currentContent = convertToContentList(currentMessage.getContent());

        lastContent.addAll(currentContent);
        lastMessage.setContent(lastContent);
    }

    /**
     * 合并助手消息内容
     */
    private static void mergeAssistantMessageContent(ChatCompletionMessageParam lastMessage, ChatCompletionMessageParam currentMessage) {
        List<Map<String, Object>> lastContent = convertToTextContentList(lastMessage.getContent());
        List<Map<String, Object>> currentContent = convertToTextContentList(currentMessage.getContent());

        lastContent.addAll(currentContent);
        lastMessage.setContent(lastContent);
    }

    /**
     * 转换为文本内容列表
     */
    private static List<Map<String, Object>> convertToTextContentList(Object content) {
        List<Map<String, Object>> result = new ArrayList<>();

        if (content instanceof String) {
            Map<String, Object> textItem = new HashMap<>();
            textItem.put("type", "text");
            textItem.put("text", content);
            result.add(textItem);
        } else if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listContent = (List<Object>) content;
            for (Object item : listContent) {
                if (item instanceof Map) {
                    @SuppressWarnings("unchecked")
                    Map<String, Object> map = (Map<String, Object>) item;
                    result.add(map);
                } else {
                    Map<String, Object> textItem = new HashMap<>();
                    textItem.put("type", "text");
                    textItem.put("text", item.toString());
                    result.add(textItem);
                }
            }
        }

        return result;
    }

    /**
     * 转换为内容列表
     */
    private static List<Object> convertToContentList(Object content) {
        List<Object> result = new ArrayList<>();

        if (content instanceof String) {
            Map<String, Object> textItem = new HashMap<>();
            textItem.put("type", "text");
            textItem.put("text", content);
            result.add(textItem);
        } else if (content instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listContent = (List<Object>) content;
            result.addAll(listContent);
        }

        return result;
    }

    public static List<ChatCompletionMessageParam> filterGPTMessageByMaxContext(List<ChatCompletionMessageParam> messages, Integer maxContext) {
        // 输入验证
        if (messages == null || messages.isEmpty()) {
            return new ArrayList<>();
        }
        // 如果消息数量少于4条，无需计算直接返回
        if (messages.size() < 4) {
            return new ArrayList<>(messages);
        }

        // 分离系统提示和聊天消息
        int chatStartIndex = -1;
        for (int i = 0; i < messages.size(); i++) {
            if (!"system".equals(messages.get(i).getRole())) {
                chatStartIndex = i;
                break;
            }
        }

        if (chatStartIndex == -1) {
            // 全部都是系统消息
            return new ArrayList<>(messages);
        }

        List<ChatCompletionMessageParam> systemPrompts = new ArrayList<>(messages.subList(0, chatStartIndex));
        List<ChatCompletionMessageParam> chatPrompts = new ArrayList<>(messages.subList(chatStartIndex, messages.size()));

        if (chatPrompts.isEmpty()) {
            return systemPrompts;
        }

        // 减去系统提示占用的token数量
        int systemTokens = TikTokenUtil.countGptMessagesTokens(systemPrompts, null, null);
        maxContext -= systemTokens;

        // 保存最终的聊天消息和临时消息
        List<ChatCompletionMessageParam> chats = new ArrayList<>();
        List<ChatCompletionMessageParam> tmpChats = new ArrayList<>();

        // 从后往前截取对话内容，每次到user则认为是一组完整信息
        while (!chatPrompts.isEmpty()) {
            ChatCompletionMessageParam lastMessage = chatPrompts.remove(chatPrompts.size() - 1);

            // 遇到user，说明到了一轮完整信息，可以开始判断是否需要保留
            if ("user".equals(lastMessage.getRole())) {
                List<ChatCompletionMessageParam> currentRound = new ArrayList<>();
                currentRound.add(lastMessage);
                currentRound.addAll(tmpChats);

                int tokens = TikTokenUtil.countGptMessagesTokens(currentRound, null, null);
                maxContext -= tokens;

                // 该轮信息整体tokens超出范围，这段数据不要了。但是至少保证一组。
                if (maxContext < 0 && !chats.isEmpty()) {
                    break;
                }

                // 将当前轮次添加到结果前面
                List<ChatCompletionMessageParam> newChats = new ArrayList<>();
                newChats.add(lastMessage);
                newChats.addAll(tmpChats);
                newChats.addAll(chats);
                chats = newChats;

                tmpChats.clear();
            } else {
                // 非user消息，添加到临时列表开头
                tmpChats.add(0, lastMessage);
            }
        }

        // 合并系统提示和聊天消息
        List<ChatCompletionMessageParam> result = new ArrayList<>();
        result.addAll(systemPrompts);
        result.addAll(chats);

        return result;

    }
}