package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.model.dto.SystemModelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * contentExtract的ActionProps
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class ActionPropsCE extends ModuleDispatchProps{

    private SystemModelDTO extractModel;

    private Map<String, Object> lastMemory;
}
