package com.sinitek.mind.core.ai.model;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 文件内容部分
 * 对应 TypeScript 中扩展的 ChatCompletionContentPartFile 类型
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChatCompletionContentPartFile extends ChatCompletionContentPart {
    private String name;
    private String url;
    
    public ChatCompletionContentPartFile() {
        super("file_url");
    }
    
    public ChatCompletionContentPartFile(String name, String url) {
        super("file_url");
        this.name = name;
        this.url = url;
    }

}