package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.app.model.ListInfo;
import lombok.Data;

import java.util.List;

@Data
public class InputComponentPropsType {

    private String referencePlaceholder;

    private String placeholder;

    private Integer maxLength;

    private List<ListInfo> list;

    private List<ListInfo> markList;

    private Integer step;

    private Integer max;

    private Integer min;

    private String defaultValue;

    private String llmModelType; // LLMModelTypeEnum

    private CustomFieldConfigType customInputConfig;
}
