package com.sinitek.mind.core.ai.model;

import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionChunk;

import java.util.Iterator;

public interface StreamChatType extends Iterator<ChatCompletionChunk> {


    /**
     * 检查是否还有更多数据
     */
    @Override
    boolean hasNext();

    /**
     * 获取下一个聊天完成块
     */
    @Override
    ChatCompletionChunk next();

    /**
     * 关闭流
     */
    void close();
}
