package com.sinitek.mind.core.dataset.enumerate;

import lombok.Getter;

/**
 * 数据集搜索模式枚举
 */
@Getter
public enum DatasetSearchModeEnum {

    /**
     * 向量搜索
     */
    EMBEDDING("embedding"),

    /**
     * 全文搜索
     */
    FULL_TEXT_RECALL("fullTextRecall"),

    /**
     * 混合搜索
     */
    MIXED_RECALL("mixedRecall");

    private final String value;

    DatasetSearchModeEnum(String value) {
        this.value = value;
    }

    /**
     * 根据值获取枚举
     */
    public static DatasetSearchModeEnum fromValue(String value) {
        for (DatasetSearchModeEnum mode : DatasetSearchModeEnum.values()) {
            if (mode.getValue().equals(value)) {
                return mode;
            }
        }
        return EMBEDDING; // 默认返回向量搜索
    }

    @Override
    public String toString() {
        return this.value;
    }

}