package com.sinitek.mind.core.workflow.model;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class LoopInteractive extends InteractiveNodeType {
    private String type = "loopInteractive";
    private Params params;

    @Data
    @SuperBuilder
    public static class Params {
        private List<Object> loopResult; // 可根据实际类型替换
        private WorkflowInteractiveResponseType childrenResponse;
        private int currentIndex;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public Params getParam() {
        return params;
    }
}