package com.sinitek.mind.core.workflow.dispatch;

import com.sinitek.mind.core.workflow.model.ModuleDispatchProps;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class NodeService {
    private final NodeDispatcherFactory dispatcherFactory;

    public NodeService(NodeDispatcherFactory dispatcherFactory) {
        this.dispatcherFactory = dispatcherFactory;
    }

    public Map<String, Object> processNode(String nodeType, ModuleDispatchProps dispatchData) {
        NodeDispatcher dispatcher = dispatcherFactory.getDispatcher(nodeType);
        return dispatcher.dispatch(dispatchData);
    }
}