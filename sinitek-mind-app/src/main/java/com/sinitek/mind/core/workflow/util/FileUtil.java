package com.sinitek.mind.core.workflow.util;

import com.mongodb.client.gridfs.model.GridFSFile;
import com.sinitek.mind.core.chat.enumerate.ChatFileType;
import com.sinitek.mind.core.chat.model.ChatItemValueItemFileInfo;
import com.sinitek.mind.dataset.core.reader.VectorFileReaderCore;
import com.sinitek.mind.support.file.constant.FileConstant;
import com.sinitek.mind.support.file.model.ReadFileResponse;
import com.sinitek.sirm.common.spring.SpringFactory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.mozilla.universalchardet.UniversalDetector;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;

import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 文件工具类
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
@Slf4j
public class FileUtil {

    /**
     * 从URL解析文件扩展名
     * 
     * @param url URL字符串
     * @return 文件扩展名（小写）
     */
    public static String parseFileExtensionFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }
        
        // 移除查询参数
        String urlWithoutQuery = url.split("\\?")[0];
        
        // 获取文件名
        String[] pathParts = urlWithoutQuery.split("/");
        String fileName = pathParts.length > 0 ? pathParts[pathParts.length - 1] : "";
        
        // 获取文件扩展名
        String[] nameParts = fileName.split("\\.");
        String extension = nameParts.length > 1 ? nameParts[nameParts.length - 1] : "";
        
        return extension.toLowerCase();
    }

    /**
     * 解析URL到文件类型
     * 
     * @param url URL字符串
     * @return 用户聊天项值类型的文件信息，如果无法解析则返回null
     */
    public static ChatItemValueItemFileInfo parseUrlToFileType(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }

        // 处理base64图片
        if (url.startsWith("data:")) {
            Pattern pattern = Pattern.compile("^data:([^;]+);base64,");
            Matcher matcher = pattern.matcher(url);
            if (!matcher.find()) {
                return null;
            }

            String mimeType = matcher.group(1).toLowerCase();
            if (!mimeType.startsWith("image/")) {
                return null;
            }

            String extension = mimeType.split("/")[1];
            return new ChatItemValueItemFileInfo(
                    ChatFileType.IMAGE.getValue(),
                    "image." + extension,
                    url
            );
        }

        try {
            URL parseUrl = new URL(url);
            
            // 从URL获取文件名
            String filename = null;
            String queryString = parseUrl.getQuery();
            if (StringUtils.isNotBlank(queryString)) {
                // 尝试从查询参数中获取filename
                Pattern filenamePattern = Pattern.compile("[?&]filename=([^&]*)");
                Matcher filenameMatcher = filenamePattern.matcher("?" + queryString);
                if (filenameMatcher.find()) {
                    filename = URLDecoder.decode(filenameMatcher.group(1), StandardCharsets.UTF_8);
                }
            }
            
            if (StringUtils.isBlank(filename)) {
                String[] pathParts = parseUrl.getPath().split("/");
                filename = pathParts.length > 0 ? pathParts[pathParts.length - 1] : "";
            }
            
            String extension = "";
            if (StringUtils.isNotBlank(filename)) {
                String[] nameParts = filename.split("\\.");
                extension = nameParts.length > 1 ? nameParts[nameParts.length - 1].toLowerCase() : "";
            }

            // 如果是文档类型，返回文件类型，否则当作图片处理
            if (StringUtils.isNotBlank(extension) && FileConstant.DOCUMENT_FILE_TYPES.contains(extension)) {
                return new ChatItemValueItemFileInfo(
                        ChatFileType.FILE.getValue(),
                        StringUtils.isNotBlank(filename) ? filename : "null",
                        url
                );
            }

            // 默认当作图片类型处理
            return new ChatItemValueItemFileInfo(
                    ChatFileType.IMAGE.getValue(),
                    StringUtils.isNotBlank(filename) ? filename : "null.png",
                    url
            );
                    
        } catch (MalformedURLException e) {
            return new ChatItemValueItemFileInfo(
                    ChatFileType.IMAGE.getValue(),
                    "invalid.png",
                    url
            );
        }
    }

    /**
     * 格式化响应对象
     * 
     * @param filename 文件名
     * @param url URL
     * @param content 内容
     * @return 格式化后的响应对象
     */
    public static FileResponseObject formatResponseObject(String filename, String url, String content) {
        String text = String.format("File: %s\n<Content>\n%s\n</Content>", filename, content);
        
        String previewContent = content.length() > 100 ? 
            content.substring(0, 100) + "......" : content;
        String nodeResponsePreviewText = String.format("File: %s\n<Content>\n%s\n</Content>", 
            filename, previewContent);
        
        return FileResponseObject.builder()
                .filename(filename)
                .url(url)
                .text(text)
                .nodeResponsePreviewText(nodeResponsePreviewText)
                .build();
    }



    /**
     * 检测文件编码
     *
     * @param buffer 文件字节数组
     * @return 检测到的编码，如果无法检测则返回UTF-8
     */
    public static String detectFileEncoding(byte[] buffer) {
        if (buffer == null || buffer.length == 0) {
            log.warn("Buffer is null or empty, returning UTF-8 as default");
            return "utf-8";
        }

        try {
            // 使用juniversalchardet检测编码
            UniversalDetector detector = new UniversalDetector(null);
            detector.handleData(buffer, 0, buffer.length);
            detector.dataEnd();

            String encoding = detector.getDetectedCharset();
            detector.reset();

            if (encoding != null) {
                // 将检测到的编码名称转换为小写，保持一致性
                String normalizedEncoding = encoding.toLowerCase();
                log.debug("Detected file encoding: {}", normalizedEncoding);
                return normalizedEncoding;
            } else {
                log.debug("Could not detect file encoding, returning UTF-8 as default");
                return "utf-8";
            }
        } catch (Exception e) {
            log.warn("Error detecting file encoding: {}, returning UTF-8 as default", e.getMessage());
            return "utf-8";
        }
    }

    /**
     * 从链接获取文件内容
     *
     * @param urls URL列表
     * @param requestOrigin 请求来源
     * @param maxFiles 最大文件数
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @param customPdfParse 是否使用自定义PDF解析
     * @return 文件内容结果
     */
    public static CompletableFuture<FileContentResult> getFileContentFromLinks(
            List<String> urls,
            String requestOrigin,
            int maxFiles,
            String teamId,
            String tmbId,
            boolean customPdfParse) {
        
        return CompletableFuture.supplyAsync(() -> {
            // 过滤和处理URL列表
            List<String> parseUrlList = urls.stream()
                    // 移除无效的URL
                    .filter(url -> {
                        if (StringUtils.isBlank(url)) {
                            return false;
                        }
                        
                        // 检查相对路径
                        return FileConstant.VALID_URL_PREFIXES.stream()
                                .anyMatch(url::startsWith);
                    })
                    // 只获取文档类型文件
                    .filter(url -> {
                        ChatItemValueItemFileInfo fileInfo = parseUrlToFileType(url);
                        return fileInfo != null && ChatFileType.FILE.getValue().equals(fileInfo.getType());
                    })
                    .map(url -> {
                        try {
                            // 检查是否为系统上传文件
                            if (url.startsWith("/") || (StringUtils.isNotBlank(requestOrigin) && url.startsWith(requestOrigin))) {
                                // 移除origin（直接进行内网请求）
                                if (StringUtils.isNotBlank(requestOrigin) && url.startsWith(requestOrigin)) {
                                    url = url.replace(requestOrigin, "");
                                }
                            }
                            return url;
                        } catch (Exception e) {
                            log.warn("Parse url error: {}", e.getMessage());
                            return "";
                        }
                    })
                    .filter(StringUtils::isNotBlank)
                    .limit(maxFiles)
                    .toList();

            // 并行处理文件读取
            List<CompletableFuture<FileResponseObject>> futures = parseUrlList.stream()
                    .map(url -> processFileUrl(url, teamId, tmbId, customPdfParse))
                    .toList();

            // 等待所有任务完成
            List<FileResponseObject> readFilesResult = futures.stream()
                    .map(CompletableFuture::join)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            // 合并所有文件内容
            String text = readFilesResult.stream()
                    .map(FileResponseObject::getText)
                    .filter(Objects::nonNull)
                    .collect(Collectors.joining("\n******\n"));

            return FileContentResult.builder()
                    .text(text)
                    .readFilesResult(readFilesResult)
                    .build();
        });
    }

    /**
     * 处理单个文件URL
     */
    private static CompletableFuture<FileResponseObject> processFileUrl(String url, String teamId, String tmbId, boolean customPdfParse) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // 从缓冲获取
                RawTextBufferResult fileBuffer = getRawTextBuffer(url);
                if (fileBuffer != null) {
                    return formatResponseObject(
                            StringUtils.isNotBlank(fileBuffer.getSourceName()) ? fileBuffer.getSourceName() : url,
                            url,
                            fileBuffer.getText()
                    );
                }

                // 实现HTTP请求获取文件数据
                return fetchFileFromHttp(url, teamId, tmbId, customPdfParse);

            } catch (Exception e) {
                log.error("Error processing file URL: {}", url, e);
                return formatResponseObject("", url, "Load file error: " + e.getMessage());
            }
        });
    }

    /**
     * 从HTTP URL获取文件内容
     */
    private static FileResponseObject fetchFileFromHttp(String url, String teamId, String tmbId, boolean customPdfParse) {
        try {
            log.debug("Fetching file from HTTP URL: {}", url);

            // 使用SpringFactory获取RestTemplate
            org.springframework.web.client.RestTemplate restTemplate = SpringFactory.getBean(org.springframework.web.client.RestTemplate.class);

            // 使用RestTemplate下载文件
            byte[] fileBytes = restTemplate.getForObject(url, byte[].class);
            if (fileBytes == null || fileBytes.length == 0) {
                log.warn("Downloaded file is empty for URL: {}", url);
                return formatResponseObject("", url, "File is empty");
            }

            // 检测文件编码
            String encoding = detectFileEncoding(fileBytes);

            // 从URL解析文件名和扩展名
            String fileName = extractFileNameFromUrl(url);
            String extension = parseFileExtensionFromUrl(url);

            // 读取文件内容
            CompletableFuture<ReadFileResponse> readResult = readRawContentByFileBuffer(
                teamId, tmbId, extension, fileBytes, encoding, customPdfParse, false
            );

            ReadFileResponse response = readResult.join();
            String content = response.getRawText();

            if (StringUtils.isBlank(content)) {
                content = "Failed to read file content";
            }

            log.debug("Successfully fetched and processed file from URL: {}", url);
            return formatResponseObject(fileName, url, content);

        } catch (Exception e) {
            log.error("Error fetching file from HTTP URL: {}", url, e);
            return formatResponseObject("", url, "HTTP fetch error: " + e.getMessage());
        }
    }

    /**
     * 从URL中提取文件名
     */
    private static String extractFileNameFromUrl(String url) {
        try {
            URL parsedUrl = new URL(url);
            String path = parsedUrl.getPath();
            if (StringUtils.isNotBlank(path)) {
                String[] pathParts = path.split("/");
                if (pathParts.length > 0) {
                    String fileName = pathParts[pathParts.length - 1];
                    if (StringUtils.isNotBlank(fileName)) {
                        return URLDecoder.decode(fileName, StandardCharsets.UTF_8);
                    }
                }
            }
            return "downloaded_file";
        } catch (Exception e) {
            log.warn("Error extracting filename from URL: {}", url, e);
            return "downloaded_file";
        }
    }

    /**
     * 获取原始文本缓冲
     *
     * @param sourceId 源ID
     * @return 原始文本缓冲结果
     */
    public static RawTextBufferResult getRawTextBuffer(String sourceId) {
        try {
            if (StringUtils.isBlank(sourceId)) {
                log.warn("sourceId is blank, returning null");
                return null;
            }

            // 使用SpringFactory获取MongoTemplate
            MongoTemplate mongoTemplate = SpringFactory.getBean(MongoTemplate.class);

            // 使用专门的GridFS bucket查询原始文本缓冲
            GridFsTemplate rawTextGridFsTemplate = new GridFsTemplate(
                mongoTemplate.getMongoDatabaseFactory(),
                mongoTemplate.getConverter(),
                FileConstant.RAW_TEXT_BUFFER_BUCKET
            );

            // 根据文件名（sourceId）查询
            Query query = new Query(Criteria.where("filename").is(sourceId));
            GridFSFile gridFSFile = rawTextGridFsTemplate.findOne(query);

            // 检查是否过期
            Document metadata = gridFSFile.getMetadata();
            if (metadata != null && metadata.containsKey("expiredTime")) {
                Date expiredTime = metadata.getDate("expiredTime");
                if (expiredTime != null && expiredTime.before(new Date())) {
                    log.debug("Raw text buffer expired for sourceId: {}", sourceId);
                    // 删除过期的文件
                    rawTextGridFsTemplate.delete(query);
                    return null;
                }
            }

            // 读取文件内容
            GridFsResource resource = rawTextGridFsTemplate.getResource(gridFSFile);
            try (InputStream inputStream = resource.getInputStream()) {
                String text = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
                String sourceName = metadata != null ? metadata.getString("sourceName") : "";

                log.debug("Successfully retrieved raw text buffer for sourceId: {}", sourceId);
                return RawTextBufferResult.builder()
                        .text(text)
                        .sourceName(sourceName)
                        .build();
            }

        } catch (Exception e) {
            log.error("Error getting raw text buffer for sourceId: {}", sourceId, e);
            return null;
        }
    }

    /**
     * 从文件缓冲读取原始内容
     *
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @param extension 文件扩展名
     * @param buffer 文件缓冲
     * @param encoding 编码
     * @param customPdfParse 是否使用自定义PDF解析
     * @param getFormatText 是否获取格式化文本
     * @return 读取文件响应
     */
    public static CompletableFuture<ReadFileResponse> readRawContentByFileBuffer(
            String teamId, String tmbId, String extension, byte[] buffer,
            String encoding, boolean customPdfParse, boolean getFormatText) {

        if (StringUtils.isBlank(extension)) {
            log.warn("File extension is blank, treating as text");
            extension = "txt";
        }

        String finalExtension = extension;
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (buffer == null || buffer.length == 0) {
                    log.warn("Buffer is null or empty");
                    return ReadFileResponse.builder()
                            .rawText("")
                            .build();
                }

                // 如果是自定义PDF解析且为PDF文件，可以在这里调用自定义服务
                if (customPdfParse && "pdf".equalsIgnoreCase(finalExtension)) {
                    // TODO: 实现自定义PDF解析服务调用
                    log.debug("Custom PDF parse requested but not implemented, using default parser");
                }

                // 创建临时文件
                File tempFile = createTempFileFromBuffer(buffer, finalExtension, encoding);
                if (tempFile == null) {
                    return ReadFileResponse.builder()
                            .rawText("Failed to create temp file")
                            .build();
                }

                try {
                    // 使用SpringFactory获取VectorFileReaderCore
                    VectorFileReaderCore vectorFileReaderCore = SpringFactory.getBean(VectorFileReaderCore.class);

                    // 使用向量文件解析器解析文件
                    List<org.springframework.ai.document.Document> documents = vectorFileReaderCore.readFile(tempFile);

                    if (documents == null || documents.isEmpty()) {
                        log.warn("No documents parsed from file with extension: {}", finalExtension);
                        return ReadFileResponse.builder()
                                .rawText("")
                                .build();
                    }

                    // 合并所有文档内容
                    StringBuilder rawTextBuilder = new StringBuilder();
                    for (org.springframework.ai.document.Document document : documents) {
                        if (StringUtils.isNotBlank(document.getText())) {
                            if (rawTextBuilder.length() > 0) {
                                rawTextBuilder.append("\n\n");
                            }
                            rawTextBuilder.append(document.getText());
                        }
                    }

                    String rawText = rawTextBuilder.toString();
                    log.debug("Successfully parsed file content, length: {}", rawText.length());

                    return ReadFileResponse.builder()
                            .rawText(rawText)
                            .formatText(getFormatText ? rawText : null)
                            .build();

                } finally {
                    // 清理临时文件
                    if (tempFile.exists()) {
                        tempFile.delete();
                    }
                }

            } catch (Exception e) {
                log.error("Error reading file content from buffer", e);
                return ReadFileResponse.builder()
                        .rawText("Error reading file: " + e.getMessage())
                        .build();
            }
        });
    }

    /**
     * 从字节数组创建临时文件
     */
    private static File createTempFileFromBuffer(byte[] buffer, String extension, String encoding) {
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("fileutil_", "." + extension);

            // 如果是文本文件且指定了编码，需要进行编码转换
            if (isTextFile(extension) && StringUtils.isNotBlank(encoding) && !"utf-8".equalsIgnoreCase(encoding)) {
                try {
                    // 将字节数组按指定编码解码为字符串，再按UTF-8编码写入文件
                    String content = new String(buffer, encoding);
                    try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                        fos.write(content.getBytes(StandardCharsets.UTF_8));
                    }
                } catch (Exception e) {
                    log.warn("Failed to convert encoding from {} to UTF-8, using original bytes", encoding, e);
                    // 编码转换失败，直接写入原始字节
                    try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                        fos.write(buffer);
                    }
                }
            } else {
                // 直接写入字节数组
                try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                    fos.write(buffer);
                }
            }

            return tempFile;

        } catch (IOException e) {
            log.error("Failed to create temp file from buffer", e);
            return null;
        }
    }

    /**
     * 判断是否为文本文件
     */
    private static boolean isTextFile(String extension) {
        if (StringUtils.isBlank(extension)) {
            return false;
        }

        String lowerExt = extension.toLowerCase();
        return lowerExt.equals("txt") || lowerExt.equals("md") ||
               lowerExt.equals("csv") || lowerExt.equals("html") ||
               lowerExt.equals("xml") || lowerExt.equals("json");
    }

    /**
     * 添加原始文本缓冲
     *
     * @param sourceId 源ID
     * @param sourceName 源名称
     * @param text 文本内容
     * @param expiredTime 过期时间
     * @return 操作结果
     */
    public static CompletableFuture<String> addRawTextBuffer(String sourceId, String sourceName, String text, Date expiredTime) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                if (StringUtils.isBlank(sourceId) || StringUtils.isBlank(text)) {
                    log.warn("sourceId or text is blank, skipping addRawTextBuffer");
                    return "";
                }

                // 使用SpringFactory获取MongoTemplate
                MongoTemplate mongoTemplate = SpringFactory.getBean(MongoTemplate.class);

                // 准备元数据
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("sourceId", sourceId);
                metadata.put("sourceName", StringUtils.isNotBlank(sourceName) ? sourceName : "");
                if (expiredTime != null) {
                    metadata.put("expiredTime", expiredTime);
                }
                metadata.put("createTime", new Date());

                // 将文本转换为输入流
                byte[] textBytes = text.getBytes(StandardCharsets.UTF_8);
                InputStream inputStream = new ByteArrayInputStream(textBytes);

                // 使用专门的GridFS bucket存储原始文本缓冲
                GridFsTemplate rawTextGridFsTemplate = new GridFsTemplate(
                    mongoTemplate.getMongoDatabaseFactory(),
                    mongoTemplate.getConverter(),
                    FileConstant.RAW_TEXT_BUFFER_BUCKET
                );

                // 存储到GridFS
                ObjectId fileId = rawTextGridFsTemplate.store(
                    inputStream,
                    sourceId,
                    "text/plain",
                    metadata
                );

                log.debug("Successfully added raw text buffer for sourceId: {}, fileId: {}", sourceId, fileId.toString());
                return fileId.toString();

            } catch (Exception e) {
                log.error("Error adding raw text buffer for sourceId: {}", sourceId, e);
                return "";
            }
        });
    }

    /**
     * 文件响应对象
     * 对应TypeScript中formatResponseObject函数的返回类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileResponseObject {
        private String filename;
        private String url;
        private String text;
        private String nodeResponsePreviewText;
    }

    /**
     * 文件内容结果
     * 对应TypeScript中getFileContentFromLinks函数的返回类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FileContentResult {
        private String text;
        private List<FileResponseObject> readFilesResult;
    }

    /**
     * 原始文本缓冲结果
     * 对应TypeScript中getRawTextBuffer函数的返回类型
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RawTextBufferResult {
        private String text;
        private String sourceName;
    }
}
