package com.sinitek.mind.core.workflow.model;

import com.sinitek.mind.core.chat.model.NodeOutputItemType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
public abstract class InteractiveNodeType {
    private List<String> entryNodeIds;
    private List<RuntimeEdgeItemType> memoryEdges;
    private List<NodeOutputItemType> nodeOutputs;

    public abstract String getType();

    public abstract Object getParam();
}