package com.sinitek.mind.core.app.dto;

import com.sinitek.mind.common.support.PageParam;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 版本列表请求参数
 * 对应原始的versionListBody类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper=true)
public class VersionListRequest extends PageParam {
    
    /**
     * 应用ID
     */
    @NotBlank(message = "应用ID不能为空")
    private String appId;
    
    /**
     * 是否已发布（可选）
     * 如果为null，则查询所有版本
     * 如果为true，则只查询已发布的版本
     * 如果为false，则只查询未发布的版本
     */
    private Boolean isPublish;
}