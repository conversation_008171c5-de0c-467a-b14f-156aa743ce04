package com.sinitek.mind.core.ai.enumerate;

import lombok.Getter;

/**
 * 完成原因枚举
 * 对应 TypeScript 中的 CompletionFinishReason
 */
@Getter
public enum CompletionFinishReason {
    CLOSE("close"),
    STOP("stop"),
    LENGTH("length"),
    TOOL_CALLS("tool_calls"),
    CONTENT_FILTER("content_filter"),
    FUNCTION_CALL("function_call"),
    NULL(null);
    
    private final String value;
    
    CompletionFinishReason(String value) {
        this.value = value;
    }

    public static CompletionFinishReason fromValue(String value) {
        for (CompletionFinishReason reason : values()) {
            if ((reason.value == null && value == null) || 
                (reason.value != null && reason.value.equals(value))) {
                return reason;
            }
        }
        throw new IllegalArgumentException("Unknown CompletionFinishReason value: " + value);
    }
}