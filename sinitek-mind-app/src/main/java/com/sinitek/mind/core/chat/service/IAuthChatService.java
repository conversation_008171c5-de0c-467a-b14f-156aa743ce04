package com.sinitek.mind.core.chat.service;

import com.sinitek.mind.core.chat.dto.AuthChatCrudParams;

/**
 * 应用相关的权限校验
 */
public interface IAuthChatService {


    /**
     * 检查chat的权限：
     * 1. 无 chatId，仅校验 cookie、shareChat、teamChat 秘钥是否合法
     * 2. 有 chatId，校验用户是否有权限操作该 chat
     * <p>
     * cookie + appId 校验
     * shareId + outLinkUid 校验
     * teamId + teamToken + appId 校验
     * <p>
     * Chat没有读写的权限之分，鉴权过了，都可以操作。
     *
     * @return 校验后的权限结果
     */
    void authChatCrud(AuthChatCrudParams params);

}
