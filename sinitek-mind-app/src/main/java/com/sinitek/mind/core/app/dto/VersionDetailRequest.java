package com.sinitek.mind.core.app.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 版本详情请求DTO
 * 对应原始接口的Props类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class VersionDetailRequest {
    
    /**
     * 版本ID
     */
    @NotNull(message = "版本ID不能为空")
    @NotBlank(message = "版本ID不能为空字符串")
    private String versionId;
    
    /**
     * 应用ID
     */
    @NotNull(message = "应用ID不能为空")
    @NotBlank(message = "应用ID不能为空字符串")
    private String appId;
}