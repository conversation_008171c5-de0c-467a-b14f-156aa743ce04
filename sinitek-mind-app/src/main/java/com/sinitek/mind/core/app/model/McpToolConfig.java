package com.sinitek.mind.core.app.model;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MCP工具配置
 * 对应原始的 McpToolConfigType
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class McpToolConfig {
    
    /**
     * 工具名称
     */
    @NotBlank(message = "工具名称不能为空")
    private String name;
    
    /**
     * 工具描述
     */
    @NotBlank(message = "工具描述不能为空")
    private String description;
    
    /**
     * 输入参数的JSON Schema
     */
    @NotNull(message = "输入参数Schema不能为空")
    private JSONSchemaInputType inputSchema;
}