package com.sinitek.mind.core.dataset.model;

import com.sinitek.mind.core.chat.model.ChatItemType;
import com.sinitek.mind.model.dto.SystemModelDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@SuperBuilder
public class SearchDatasetDataProps {

    private List<ChatItemType> histories;

    private String teamId;

    private String reRankQuery;

    private List<String> queries;

    private String model;

    private Double similarity;

    private Integer limit;

    private List<String> datasetIds;

    private String searchMode;

    private Double embeddingWeight;

    private Boolean usingReRank;

    private SystemModelDTO rerankModel;

    private Double rerankWeight;

    private String collectionFilterMatch;
}
