package com.sinitek.mind.core.workflow.model;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 工具调用内容
 */
@Data
@Builder
public class ToolCallContent {
    /**
     * 工具ID
     */
    private String id;
    
    /**
     * 工具名称
     */
    private String name;
    
    /**
     * 工具类型
     */
    private String type;
    
    /**
     * 参数
     */
    private Map<String, Object> arguments;
    
    /**
     * 结果
     */
    private String result;
    
    /**
     * 状态
     */
    private String status;
}
