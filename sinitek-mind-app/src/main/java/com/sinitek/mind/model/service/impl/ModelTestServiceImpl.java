package com.sinitek.mind.model.service.impl;

import com.sinitek.mind.model.core.embedding.IModelEmbeddingService;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import com.sinitek.mind.model.core.rerank.IModelRerankService;
import com.sinitek.mind.model.core.stt.service.ISpeechToTextService;
import com.sinitek.mind.model.core.tts.service.ITextToSpeechService;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.IModelTestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;
import org.springframework.util.FileCopyUtils;

import java.io.IOException;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型测试服务实现类
 *
 * <AUTHOR>
 * date 2025-07-10
 * 描述：负责执行不同类型模型的测试实现
 */
@Slf4j
@Service
public class ModelTestServiceImpl implements IModelTestService {

    @Autowired
    private IModelChatService modelChatService;
    
    @Autowired
    private IModelEmbeddingService modelEmbeddingService;
    
    @Autowired
    private IModelRerankService modelRerankService;
    
    @Autowired
    private ITextToSpeechService textToSpeechService;
    
    @Autowired
    private ISpeechToTextService speechToTextService;
    
    /**
     * 测试模型
     */
    @Override
    public Object testModel(SystemModelDTO model, Map<String, String> headers) throws Exception {
        String modelType = model.getType();
        String modelId = model.getModel();
        
        log.debug("测试模型: {}, 类型: {}", modelId, modelType);
        
        switch (modelType) {
            case "llm":
                return testLLMModel(modelId, headers);
            case "embedding":
                return testEmbeddingModel(modelId, headers);
            case "tts":
                return testTTSModel(model, headers);
            case "stt":
                return testSTTModel(model, headers);
            case "rerank":
                return testReRankModel(modelId, headers);
            default:
                throw new IllegalArgumentException("不支持的模型类型: " + modelType);
        }
    }
    
    /**
     * 测试 LLM 模型
     */
    @Override
    public Object testLLMModel(String modelId, Map<String, String> headers) throws Exception {
        try {
            // 使用简单的问候消息测试模型
            String testMessage = "不需要任何思考，请最快速度回答一个yes";
            
            // 调用实际的LLM服务
            return modelChatService.chat(testMessage, modelId);
        } catch (Exception e) {
            log.error("测试LLM模型失败: " + e.getMessage(), e);
            throw new Exception("测试LLM模型失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 测试 Embedding 模型
     */
    @Override
    public Object testEmbeddingModel(String modelId, Map<String, String> headers) throws Exception {
        try {
            // 使用简单的文本测试embedding模型
            String testText = "这是一个测试文本";
            
            // 调用实际的Embedding服务
            float[] embedding = modelEmbeddingService.embed(testText, modelId);
            
            // 为了展示友好，只返回向量的一部分
            Map<String, Object> result = new HashMap<>();
            result.put("embedding_size", embedding.length);
            result.put("embedding_sample", embedding.length > 10 ? Arrays.copyOfRange(embedding, 0, 10) : embedding);
            result.put("message", "Embedding模型测试成功，生成了" + embedding.length + "维的向量");
            
            return result;
        } catch (Exception e) {
            log.error("测试Embedding模型失败: " + e.getMessage(), e);
            throw new Exception("测试Embedding模型失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 测试 TTS 模型
     */
    @Override
    public Object testTTSModel(SystemModelDTO model, Map<String, String> headers) throws Exception {
        try {
            // 使用简单的测试文本
            String testText = "这是一个文本转语音的测试";
            String modelId = model.getModel();
            // 使用默认语音ID
            String voiceId = "";
            
            // 调用TTS服务生成语音
            byte[] audioData = textToSpeechService.textToSpeech(testText, modelId, voiceId);
            
            // 返回测试结果
            Map<String, Object> result = new HashMap<>();
            result.put("audio_size", audioData.length);
            result.put("message", "TTS模型测试成功，生成了" + audioData.length + "字节的音频数据");
            
            return result;
        } catch (Exception e) {
            log.error("测试TTS模型失败: " + e.getMessage(), e);
            throw new Exception("测试TTS模型失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 测试 STT 模型
     */
    @Override
    public Object testSTTModel(SystemModelDTO model, Map<String, String> headers) throws Exception {
        try {
            // 获取模型ID
            String modelId = model.getModel();
            
            // 加载测试音频文件（需要在项目资源目录中准备一个测试音频文件）
            byte[] audioData = loadTestAudioFile();
            
            if (audioData == null || audioData.length == 0) {
                // 如果没有测试音频，使用TTS服务生成一个
                String testText = "这是一个语音识别的测试";
                // 使用第一个可用的TTS模型来生成测试音频
                try {
                    audioData = textToSpeechService.textToSpeech(testText, model.getModel(), "");
                } catch (Exception e) {
                    log.warn("无法使用TTS生成测试音频: {}", e.getMessage());
                    throw new Exception("测试STT模型失败: 无法获取测试音频数据");
                }
            }
            
            // 调用STT服务进行语音识别
            String recognizedText = speechToTextService.speechToText(audioData, modelId);
            
            // 返回测试结果
            Map<String, Object> result = new HashMap<>();
            result.put("recognized_text", recognizedText);
            result.put("audio_size", audioData.length);
            result.put("message", "STT模型测试成功");
            
            return result;
        } catch (Exception e) {
            log.error("测试STT模型失败: " + e.getMessage(), e);
            throw new Exception("测试STT模型失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 加载测试音频文件
     * 
     * @return 音频文件的字节数组
     */
    private byte[] loadTestAudioFile() {
        try {
            // 从资源目录加载测试音频文件
            ClassPathResource resource = new ClassPathResource("test/test_audio.mp3");
            if (resource.exists()) {
                return FileCopyUtils.copyToByteArray(resource.getInputStream());
            }
            return null;
        } catch (IOException e) {
            log.warn("加载测试音频文件失败: {}", e.getMessage());
            return null;
        }
    }
    
    /**
     * 测试 Rerank 模型
     */
    @Override
    public Object testReRankModel(String modelId, Map<String, String> headers) throws Exception {
        try {
            // 测试重排序功能
            String query = "机器学习的应用";
            List<String> documents = Arrays.asList(
                "机器学习是人工智能的一个分支，它专注于开发能够从数据中学习的算法和模型",
                "深度学习是机器学习的一个子领域，它使用神经网络进行学习",
                "自然语言处理是机器学习在文本分析中的应用",
                "计算机视觉是机器学习在图像分析中的应用"
            );
            
            // 调用实际的Rerank服务
            List<Map<String, Object>> rerankResults = modelRerankService.rerank(query, documents, modelId);
            
            return rerankResults;
        } catch (Exception e) {
            log.error("测试Rerank模型失败: " + e.getMessage(), e);
            throw new Exception("测试Rerank模型失败: " + e.getMessage(), e);
        }
    }
} 