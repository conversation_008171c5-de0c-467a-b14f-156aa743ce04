package com.sinitek.mind.model.core.embedding;

import org.springframework.ai.embedding.EmbeddingRequest;
import org.springframework.ai.embedding.EmbeddingResponse;

import java.util.List;

/**
 * 系统索引模型服务层
 *
 * <AUTHOR>
 * date 2025-07-07
 */
public interface IModelEmbeddingService {

    /**
     * 文本向量嵌入
     * @param text 待嵌入文本
     * @param model 模型标识
     * @return 向量数组
     */
    float[] embed(String text, String model);

    /**
     * 批量文本向量嵌入
     * @param texts 待嵌入文本列表
     * @param model 模型标识
     * @return 嵌入响应
     */
    EmbeddingResponse embedBatch(List<String> texts, String model);

    /**
     * 使用EmbeddingRequest进行文本向量嵌入
     * @param request 嵌入请求
     * @param model 模型标识
     * @return 嵌入响应
     */
    EmbeddingResponse embed(EmbeddingRequest request, String model);

}