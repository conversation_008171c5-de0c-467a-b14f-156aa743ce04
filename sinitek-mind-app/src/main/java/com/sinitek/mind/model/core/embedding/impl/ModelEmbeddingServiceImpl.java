package com.sinitek.mind.model.core.embedding.impl;

import com.sinitek.mind.model.core.embedding.EmbeddingModelFactory;
import com.sinitek.mind.model.core.embedding.IModelEmbeddingService;
import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.enumerate.ModelTypeEnum;
import com.sinitek.mind.model.service.impl.AbstractModelInvokeLogService;
import org.springframework.ai.embedding.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 系统索引模型服务层实现
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Service
public class ModelEmbeddingServiceImpl extends AbstractModelInvokeLogService implements IModelEmbeddingService {

    @Autowired
    private EmbeddingModelFactory embeddingModelFactory;

    /**
     * 复用embedBatch
     * @param text 待嵌入文本
     * @param model 模型标识
     * @return
     */
    @Override
    public float[] embed(String text, String model) {
        EmbeddingResponse embeddingResponse = this.embedBatch(Collections.singletonList(text), model);
        return embeddingResponse.getResult().getOutput();
    }

    /**
     * 复用embed
     * @param texts 待嵌入文本列表
     * @param model 模型标识
     * @return
     */
    @Override
    public EmbeddingResponse embedBatch(List<String> texts, String model) {
        EmbeddingOptions embeddingOptions = EmbeddingOptionsBuilder.builder().withModel(model).build();
        EmbeddingRequest embeddingRequest = new EmbeddingRequest(texts, embeddingOptions);
        return this.embed(embeddingRequest, model);
    }

    /**
     * 最终的实现
     * @param request 嵌入请求
     * @param model 模型标识
     * @return
     */
    @Override
    public EmbeddingResponse embed(EmbeddingRequest request, String model) {
        ModelInvokeLog modelInvokeLog = super.createModelInvokeLog(model);
        try {
            EmbeddingModel embeddingModel = embeddingModelFactory.embeddingModel(model);
            EmbeddingResponse embeddingResponse = embeddingModel.call(request);
            super.buildUsage(embeddingResponse.getMetadata().getUsage(), modelInvokeLog);

            return embeddingResponse;
        } catch (Exception e) {
            super.saveFailModelInvokeLog(modelInvokeLog, e.getMessage());
            throw e;
        } finally {
            super.saveSuccessModelInvokeLog(modelInvokeLog);
        }
    }

    @Override
    public ModelTypeEnum getCurrentModelTypeEnum() {
        return ModelTypeEnum.EMBEDDING;
    }
}