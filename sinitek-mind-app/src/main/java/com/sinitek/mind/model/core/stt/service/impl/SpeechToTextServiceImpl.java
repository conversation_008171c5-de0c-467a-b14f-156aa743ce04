package com.sinitek.mind.model.core.stt.service.impl;

import com.sinitek.mind.model.core.stt.STTModelFactory;
import com.sinitek.mind.model.core.stt.service.ISpeechToTextService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

/**
 * 语音转文本服务实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Service
public class SpeechToTextServiceImpl implements ISpeechToTextService {

    @Autowired
    private STTModelFactory sttModelFactory;

    @Override
    public String speechToText(byte[] audioData, String model) {
        return sttModelFactory.recognizeSpeech(audioData, model);
    }

    @Override
    public String speechToText(InputStream audioStream, String model) {
        return sttModelFactory.recognizeSpeech(audioStream, model);
    }

    @Override
    public CompletableFuture<String> speechToTextAsync(byte[] audioData, String model) {
        return CompletableFuture.supplyAsync(() -> speechToText(audioData, model));
    }
}