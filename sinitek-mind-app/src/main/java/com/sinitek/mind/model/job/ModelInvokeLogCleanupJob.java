package com.sinitek.mind.model.job;

import com.sinitek.mind.model.service.IModelInvokeLogService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 模型调用日志清理定时任务
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
@EnableScheduling
public class ModelInvokeLogCleanupJob implements ApplicationRunner {

    private static final Logger logger = LoggerFactory.getLogger(ModelInvokeLogCleanupJob.class);

    @Autowired
    private IModelInvokeLogService modelInvokeLogService;

    /**
     * 定时清理30天前的日志数据，每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void cleanupOldLogs() {
        try {
            logger.info("开始清理过期的模型调用日志...");

            // 计算30天前的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -30);
            Date expirationDate = calendar.getTime();

            // 删除过期日志
            long deletedCount = modelInvokeLogService.deleteLogsBefore(expirationDate);
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            logger.info("已成功清理{}条{}之前的过期模型调用日志", deletedCount, sdf.format(expirationDate));
        } catch (Exception e) {
            logger.error("清理过期模型调用日志失败", e);
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("系统启动，初始化模型调用日志清理任务");
    }
} 