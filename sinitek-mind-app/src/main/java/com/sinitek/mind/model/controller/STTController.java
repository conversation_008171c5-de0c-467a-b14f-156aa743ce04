package com.sinitek.mind.model.controller;

import com.sinitek.mind.model.core.stt.service.ISpeechToTextService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 语音转文本控制器
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@RestController
@RequestMapping("/mind/api/model/stt")
public class STTController {

    @Autowired
    private ISpeechToTextService speechToTextService;

    /**
     * 语音转文本
     * @param audioFile 音频文件
     * @param model 模型标识
     * @return 识别结果
     * @throws IOException IO异常
     */
    @PostMapping(value = "/{model}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public Map<String, Object> speechToText(
            @RequestParam("file") MultipartFile audioFile,
            @PathVariable String model) throws IOException {
        
        byte[] audioData = audioFile.getBytes();
        String text = speechToTextService.speechToText(audioData, model);
        
        Map<String, Object> result = new HashMap<>();
        result.put("text", text);
        
        return result;
    }

    /**
     * 异步语音转文本
     * @param audioFile 音频文件
     * @param model 模型标识
     * @return 异步识别结果
     * @throws IOException IO异常
     */
    @PostMapping(value = "/async/{model}", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
    public CompletableFuture<Map<String, Object>> speechToTextAsync(
            @RequestParam("file") MultipartFile audioFile,
            @PathVariable String model) throws IOException {
        
        byte[] audioData = audioFile.getBytes();
        return speechToTextService.speechToTextAsync(audioData, model)
                .thenApply(text -> {
                    Map<String, Object> result = new HashMap<>();
                    result.put("text", text);
                    return result;
                });
    }
} 