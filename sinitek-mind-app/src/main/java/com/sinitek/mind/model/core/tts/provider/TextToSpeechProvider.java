package com.sinitek.mind.model.core.tts.provider;

import com.sinitek.mind.model.dto.SystemModelDTO;

import java.io.OutputStream;

/**
 * 文本转语音模型提供商接口
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public interface TextToSpeechProvider {

    /**
     * 获取支持的提供商类型
     * @return 提供商类型
     */
    String getProviderType();

    /**
     * 将文本转换为语音
     * @param text 要转换的文本
     * @param modelDTO 模型信息
     * @param voiceId 语音ID
     * @return 语音数据字节数组
     */
    byte[] generateSpeech(String text, SystemModelDTO modelDTO, String voiceId);

    /**
     * 将文本转换为语音并写入输出流
     * @param text 要转换的文本
     * @param modelDTO 模型信息
     * @param voiceId 语音ID
     * @param outputStream 输出流
     */
    void generateSpeechToStream(String text, SystemModelDTO modelDTO, String voiceId, OutputStream outputStream);

    /**
     * 是否支持该提供商
     * @param providerType 提供商类型
     * @return 是否支持
     */
    default boolean supports(String providerType) {
        return getProviderType().equalsIgnoreCase(providerType);
    }
} 