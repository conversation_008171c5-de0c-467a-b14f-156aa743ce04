package com.sinitek.mind.model.core.llm.provider;

import com.sinitek.mind.model.dto.SystemModelDTO;
import org.springframework.ai.chat.model.ChatModel;

/**
 * 聊天模型提供商接口
 *
 * <AUTHOR>
 * date 2025-07-07
 */
public interface ChatModelProvider {

    /**
     * 获取支持的提供商类型
     * @return 提供商类型
     */
    String getProviderType();

    /**
     * 创建聊天模型
     * @param modelDTO 模型详情
     * @return 聊天模型实例
     */
    ChatModel createChatModel(SystemModelDTO modelDTO);

    /**
     * 是否支持该提供商
     * @param providerType 提供商类型
     * @return 是否支持
     */
    default boolean supports(String providerType) {
        return getProviderType().equalsIgnoreCase(providerType);
    }
} 