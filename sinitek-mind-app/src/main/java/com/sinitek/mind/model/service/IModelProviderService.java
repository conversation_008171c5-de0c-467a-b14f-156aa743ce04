package com.sinitek.mind.model.service;

import com.sinitek.mind.model.dto.ModelProviderInfoDTO;

import java.util.List;

/**
 * 模型提供商服务接口
 *
 * <AUTHOR>
 * date 2025-07-07
 */
public interface IModelProviderService {

    /**
     * 获取所有模型提供商信息
     * @return 提供商信息列表
     */
    List<ModelProviderInfoDTO> getAllProviders();

    /**
     * 根据提供商代码获取提供商信息
     * @param providerCode 提供商代码
     * @return 提供商信息
     */
    ModelProviderInfoDTO getProviderByCode(String providerCode);

    /**
     * 获取提供商默认接口地址
     * @param providerCode 提供商代码
     * @return 默认接口地址
     */
    String getProviderDefaultUrl(String providerCode);

    /**
     * 获取提供商默认的completions路径
     * @param providerCode 提供商代码
     * @return 默认completions路径
     */
    String getProviderCompletionsPath(String providerCode);
}