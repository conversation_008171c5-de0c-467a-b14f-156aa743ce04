package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型调用日志Usage DTO
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Data
public class ModelInvokeLogUsageDTO {

    @Schema(description = "输入token数量")
    private Integer input_tokens;

    @Schema(description = "输出token数量")
    private Integer output_tokens;

    @Schema(description = "总token数量")
    private Integer total_tokens;
}