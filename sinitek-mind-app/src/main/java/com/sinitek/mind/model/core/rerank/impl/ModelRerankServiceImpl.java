package com.sinitek.mind.model.core.rerank.impl;

import com.sinitek.mind.model.core.rerank.IModelRerankService;
import com.sinitek.mind.model.core.rerank.RerankModelFactory;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 系统重排模型服务层实现
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Service
public class ModelRerankServiceImpl implements IModelRerankService {

    @Autowired
    private RerankModelFactory rerankModelFactory;

    @Autowired
    private ISystemModelService systemModelService;

    @Override
    public List<Map<String, Object>> rerank(String query, List<String> documents, String model) {
        Object rerankModel = rerankModelFactory.rerankModel(model);
        SystemModelDTO modelDTO = systemModelService.getModelDetail(model);
        
        // 由于Spring AI目前还没有标准的重排模型接口，这里使用简单实现
        // 实际使用时需要根据不同模型的特性实现
        
        // 简单计算相似度（仅作示例）
        List<Map<String, Object>> results = new ArrayList<>();
        for (int i = 0; i < documents.size(); i++) {
            String document = documents.get(i);
            Map<String, Object> result = new HashMap<>();
            result.put("document", document);
            result.put("index", i);
            
            // 简单计算相似度，实际应该使用模型计算
            double score = calculateSimpleScore(query, document);
            result.put("score", score);
            
            results.add(result);
        }
        
        // 按分数降序排序
        results.sort((a, b) -> Double.compare((Double) b.get("score"), (Double) a.get("score")));
        
        return results;
    }

    @Override
    public CompletableFuture<List<Map<String, Object>>> rerankAsync(String query, List<String> documents, String model) {
        return CompletableFuture.supplyAsync(() -> rerank(query, documents, model));
    }
    
    @Override
    public List<Map<String, Object>> rerank(Prompt prompt, List<String> documents, String model) {
        // 使用ChatModel实现高级重排序功能
        ChatModel chatModel = rerankModelFactory.getChatModel(model);
        
        List<Map<String, Object>> results = new ArrayList<>();
        for (int i = 0; i < documents.size(); i++) {
            String document = documents.get(i);
            Map<String, Object> result = new HashMap<>();
            result.put("document", document);
            result.put("index", i);
            
            // 构建包含文档的提示
            String promptWithDocument = String.format(
                "请评估以下文档与查询的相关性得分，返回0到100之间的数字，数字越高表示相关性越高。\n\n查询: %s\n\n文档: %s\n\n相关性得分:",
                prompt.getInstructions(),
                document
            );
            
            // 调用大模型评估相关性
            String scoreText = chatModel.call(promptWithDocument);
            double score;
            try {
                // 提取得分
                score = extractScore(scoreText);
            } catch (Exception e) {
                // 如果解析失败，使用简单计算
                score = calculateSimpleScore(prompt.getInstructions().stream().toString(), document);
            }
            
            result.put("score", score);
            results.add(result);
        }
        
        // 按分数降序排序
        results.sort((a, b) -> Double.compare((Double) b.get("score"), (Double) a.get("score")));
        
        return results;
    }

    @Override
    public CompletableFuture<List<Map<String, Object>>> rerankAsync(Prompt prompt, List<String> documents, String model) {
        return CompletableFuture.supplyAsync(() -> rerank(prompt, documents, model));
    }
    
    /**
     * 从模型输出中提取分数
     */
    private double extractScore(String text) {
        // 尝试直接解析数字
        try {
            return Double.parseDouble(text.trim());
        } catch (NumberFormatException e) {
            // 如果不是纯数字，尝试正则表达式匹配
            String regex = "\\b(\\d{1,3}(\\.\\d+)?)\\b";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
            java.util.regex.Matcher matcher = pattern.matcher(text);
            
            if (matcher.find()) {
                return Double.parseDouble(matcher.group(1));
            }
            
            // 如果仍然找不到，返回默认值
            return 50.0; // 默认中等相关性
        }
    }
    
    /**
     * 简单相似度计算，仅作示例
     */
    private double calculateSimpleScore(String query, String document) {
        // 简化实现，实际应使用模型计算
        // 这里使用简单的词重合度计算
        Set<String> queryWords = Arrays.stream(query.toLowerCase().split("\\W+"))
                .collect(Collectors.toSet());
        
        Set<String> documentWords = Arrays.stream(document.toLowerCase().split("\\W+"))
                .collect(Collectors.toSet());
        
        // 交集大小
        Set<String> intersection = new HashSet<>(queryWords);
        intersection.retainAll(documentWords);
        
        // 简单的Jaccard相似度
        return (double) intersection.size() / (queryWords.size() + documentWords.size() - intersection.size());
    }
} 