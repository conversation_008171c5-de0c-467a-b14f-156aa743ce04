package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * 模型调用日志DTO
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Data
public class ModelInvokeLogDTO {

    @Schema(description = "记录ID")
    private String id;

    @Schema(description = "请求ID")
    private String request_id;

    @Schema(description = "模型名称")
    private String model;

    @Schema(description = "调用的接口地址")
    private String endpoint;

    @Schema(description = "状态码")
    private Integer code;

    @Schema(description = "模型使用情况")
    private ModelInvokeLogUsageDTO usage;

    @Schema(description = "客户端IP")
    private String ip;

    @Schema(description = "错误时的异常信息")
    private String content;

    @Schema(description = "请求结束时间")
    private Date created_at;

    @Schema(description = "请求开始时间")
    private Date request_at;
} 