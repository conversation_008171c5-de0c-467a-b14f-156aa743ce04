package com.sinitek.mind.model.controller;

import com.sinitek.mind.model.core.tts.service.ITextToSpeechService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 文本转语音控制器
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@RestController
@RequestMapping("/mind/api/model/tts")
public class TTSController {

    @Autowired
    private ITextToSpeechService textToSpeechService;

    /**
     * 文本转语音
     * @param params 请求参数，包含text(文本)、voiceId(可选)
     * @param model 模型标识
     * @return 音频数据
     */
    @PostMapping("/{model}")
    public ResponseEntity<byte[]> textToSpeech(
            @RequestBody Map<String, Object> params,
            @PathVariable String model) {
        
        String text = (String) params.getOrDefault("text", "");
        String voiceId = (String) params.getOrDefault("voiceId", "");
        
        byte[] audioData = textToSpeechService.textToSpeech(text, model, voiceId);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType("audio/mpeg"));
        headers.setContentLength(audioData.length);
        
        return new ResponseEntity<>(audioData, headers, HttpStatus.OK);
    }

    /**
     * 异步文本转语音
     * @param params 请求参数，包含text(文本)、voiceId(可选)
     * @param model 模型标识
     * @return 异步响应
     */
    @PostMapping("/async/{model}")
    public CompletableFuture<ResponseEntity<byte[]>> textToSpeechAsync(
            @RequestBody Map<String, Object> params,
            @PathVariable String model) {
        
        String text = (String) params.getOrDefault("text", "");
        String voiceId = (String) params.getOrDefault("voiceId", "");
        
        return textToSpeechService.textToSpeechAsync(text, model, voiceId)
                .thenApply(audioData -> {
                    HttpHeaders headers = new HttpHeaders();
                    headers.setContentType(MediaType.parseMediaType("audio/mpeg"));
                    headers.setContentLength(audioData.length);
                    
                    return new ResponseEntity<>(audioData, headers, HttpStatus.OK);
                });
    }
} 