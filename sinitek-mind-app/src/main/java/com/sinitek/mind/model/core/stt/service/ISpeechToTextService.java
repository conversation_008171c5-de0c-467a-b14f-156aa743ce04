package com.sinitek.mind.model.core.stt.service;

import java.io.InputStream;
import java.util.concurrent.CompletableFuture;

/**
 * 系统语音转文本服务接口
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public interface ISpeechToTextService {

    /**
     * 将语音转换为文本
     * @param audioData 音频数据字节数组
     * @param model 模型标识
     * @return 识别后的文本
     */
    String speechToText(byte[] audioData, String model);

    /**
     * 将语音流转换为文本
     * @param audioStream 音频数据流
     * @param model 模型标识
     * @return 识别后的文本
     */
    String speechToText(InputStream audioStream, String model);
    
    /**
     * 异步将语音转换为文本
     * @param audioData 音频数据字节数组
     * @param model 模型标识
     * @return 异步响应结果
     */
    CompletableFuture<String> speechToTextAsync(byte[] audioData, String model);
} 