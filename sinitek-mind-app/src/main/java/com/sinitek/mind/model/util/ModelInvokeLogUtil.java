package com.sinitek.mind.model.util;

import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.service.IModelInvokeLogService;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 模型调用日志工具类
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public class ModelInvokeLogUtil {

    /**
     * 执行模型调用并记录日志（同步方法）
     * 
     * @param modelName 模型名称
     * @param inputContent 输入内容
     * @param serviceType 服务类型
     * @param logService 日志服务
     * @param modelCall 模型调用函数
     * @param <T> 返回类型
     * @return 模型调用结果
     */
    public static <T> T executeWithLog(
            String modelName, 
            String inputContent, 
            String serviceType,
            IModelInvokeLogService logService, 
            Function<Void, T> modelCall) {
        
        // 获取请求信息
        String endpoint = getEndpoint(serviceType);
        String userId = getUserId();
        
        // 创建日志记录
        ModelInvokeLog log = logService.createLog(modelName, endpoint, userId);
        String requestId = log.getRequestId();
        
        try {
            // 执行模型调用
            T result = modelCall.apply(null);
            
            // 更新日志
            updateLogWithResult(requestId, inputContent, result, logService);
            
            return result;
        } catch (Exception e) {
            // 记录异常
            logService.updateLogResponse(requestId, 500, 0, 0);
            throw e;
        }
    }
    
    /**
     * 执行模型调用并记录日志（异步方法）
     * 
     * @param modelName 模型名称
     * @param inputContent 输入内容
     * @param serviceType 服务类型
     * @param logService 日志服务
     * @param asyncModelCall 异步模型调用函数
     * @param <T> 返回类型
     * @return 异步模型调用结果
     */
    public static <T> CompletableFuture<T> executeWithLogAsync(
            String modelName, 
            String inputContent, 
            String serviceType,
            IModelInvokeLogService logService, 
            Supplier<CompletableFuture<T>> asyncModelCall) {
        
        // 获取请求信息
        String endpoint = getEndpoint(serviceType);
        String userId = getUserId();
        
        // 创建日志记录
        ModelInvokeLog log = logService.createLog(modelName, endpoint, userId);
        String requestId = log.getRequestId();
        
        return asyncModelCall.get().thenApply(result -> {
            // 更新日志
            updateLogWithResult(requestId, inputContent, result, logService);
            return result;
        }).exceptionally(e -> {
            // 记录异常
            logService.updateLogResponse(requestId, 500, 0, 0);
            throw new RuntimeException(e);
        });
    }
    
    /**
     * 根据不同类型的结果更新日志
     */
    @SuppressWarnings("unchecked")
    private static void updateLogWithResult(String requestId, String inputContent, Object result, IModelInvokeLogService logService) {
        int inputTokens = TokenCalculatorUtil.estimateTokens(inputContent);
        int outputTokens = 0;
        
        if (result instanceof String) {
            // 字符串结果
            outputTokens = TokenCalculatorUtil.estimateTokens((String) result);
        } else if (result instanceof List) {
            // List结果
            List<?> list = (List<?>) result;
            if (list.isEmpty()) {
                outputTokens = 0;
            } else if (list.get(0) instanceof Double) {
                // 嵌入向量
                outputTokens = list.size() / 10;
            } else if (list.get(0) instanceof List) {
                // 批量嵌入向量
                List<List<Double>> embeddings = (List<List<Double>>) result;
                outputTokens = embeddings.size() * (embeddings.isEmpty() ? 0 : 
                             (embeddings.get(0).isEmpty() ? 0 : embeddings.get(0).size())) / 10;
            } else if (list.get(0) instanceof Map) {
                // 重排序结果
                outputTokens = list.size() * 5;
            }
        } else if (result instanceof EmbeddingResponse) {
            // 嵌入响应
            EmbeddingResponse response = (EmbeddingResponse) result;
            int dimensions = 0;
            if (!response.getResults().isEmpty() && !(response.getResults().get(0).getIndex() == null)) {
                dimensions = response.getResults().get(0).getIndex();
            }
            outputTokens = response.getResults().size() * dimensions / 10;
        }
        
        logService.updateLogResponse(requestId, 200, inputTokens, outputTokens);
    }
    
    /**
     * 从Prompt对象提取内容
     */
    public static String getPromptContent(Prompt prompt) {
        return prompt.getContents().toString();
    }
    
    /**
     * 获取当前请求的接口地址
     */
    private static String getEndpoint(String defaultType) {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
            return request != null ? request.getRequestURI() : defaultType + "/direct";
        } catch (Exception e) {
            return defaultType + "/direct";
        }
    }
    
    /**
     * 获取当前用户ID
     */
    private static String getUserId() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            HttpServletRequest request = attributes != null ? attributes.getRequest() : null;
            // 这里应根据实际的用户认证方式获取用户ID
            return request != null ? request.getHeader("X-User-Id") : "system";
        } catch (Exception e) {
            return "system";
        }
    }
} 