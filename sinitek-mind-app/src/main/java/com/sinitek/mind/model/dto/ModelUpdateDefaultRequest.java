package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新默认模型请求 DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Data
@Schema(description = "更新默认模型请求")
public class ModelUpdateDefaultRequest {

    @Schema(description = "默认的 LLM 模型")
    private String llm;

    @Schema(description = "默认的 Embedding 模型")
    private String embedding;

    @Schema(description = "默认的 TTS 模型")
    private String tts;

    @Schema(description = "默认的 STT 模型")
    private String stt;

    @Schema(description = "默认的 Rerank 模型")
    private String rerank;

    @Schema(description = "默认的数据集文本 LLM 模型")
    private String datasetTextLLM;

    @Schema(description = "默认的数据集图像 LLM 模型")
    private String datasetImageLLM;
} 