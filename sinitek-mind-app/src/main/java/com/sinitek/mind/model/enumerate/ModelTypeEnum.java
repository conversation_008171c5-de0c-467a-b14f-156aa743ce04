package com.sinitek.mind.model.enumerate;

import com.sinitek.mind.model.constant.ModelTypeConstant;

/**
 * 模型类型枚举
 *
 * <AUTHOR>
 * date 2025-07-01
 */
public enum ModelTypeEnum {

    LLM(ModelTypeConstant.LLM_CODE, "语言模型", "/v1/chat/completions"),

    EMBEDDING(ModelTypeConstant.EMBEDDING_CODE, "嵌入模型", "/v1/embeddings"),

    TTS(ModelTypeConstant.TTS_CODE, "文本转语音模型", "/v1/audio/speech"),

    STT(ModelTypeConstant.STT_CODE, "语音转文本模型", "/v1/audio/transcriptions"),

    RERANK(ModelTypeConstant.RERANK_CODE, "重排序模型", "/v1/rerank");

    private final String code;
    private final String desc;
    private final String endpoint;

    ModelTypeEnum(String code, String desc, String endpoint) {
        this.code = code;
        this.desc = desc;
        this.endpoint = endpoint;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getEndpoint() {
        return endpoint;
    }
} 