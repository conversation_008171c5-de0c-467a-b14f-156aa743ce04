package com.sinitek.mind.model.service.impl;

import com.sinitek.mind.model.dto.ModelInvokeLogSearchDTO;
import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.enumerate.ModelInvokeStatus;
import com.sinitek.mind.model.repository.ModelInvokeLogRepository;
import com.sinitek.mind.model.service.IModelInvokeLogService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 模型调用日志服务实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Service
public class ModelInvokeLogServiceImpl implements IModelInvokeLogService {

    @Autowired
    private ModelInvokeLogRepository modelInvokeLogRepository;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Override
    public ModelInvokeLog saveLog(ModelInvokeLog log) {
        if (StringUtils.isBlank(log.getRequestId())) {
            log.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        return modelInvokeLogRepository.save(log);
    }

    @Override
    public ModelInvokeLog createLog(String modelName, String endpoint, String userId) {
        ModelInvokeLog log = new ModelInvokeLog();
        log.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        log.setModelName(modelName);
        log.setEndpoint(endpoint);
        log.setUserId(userId);
        log.setRequestTime(new Date());
        
        return modelInvokeLogRepository.save(log);
    }

    @Override
    public ModelInvokeLog updateLogResponse(String requestId, Integer status, Integer inputTokens, Integer outputTokens) {
        ModelInvokeLog log = modelInvokeLogRepository.findByRequestId(requestId);
        
        if (log != null) {
            log.setStatus(status);
            log.setInputTokens(inputTokens);
            log.setOutputTokens(outputTokens);
            log.setResponseTime(new Date());
            
            return modelInvokeLogRepository.save(log);
        }
        
        return null;
    }

    @Override
    public ModelInvokeLog findByRequestId(String requestId) {
        return modelInvokeLogRepository.findByRequestId(requestId);
    }

    @Override
    public Page<ModelInvokeLog> queryLogs(ModelInvokeLogSearchDTO queryDTO) {
        Query query = new Query();

        Date startTime = queryDTO.getStart_timestamp() != null ? new Date(queryDTO.getStart_timestamp()) : null;
        Date endTime = queryDTO.getEnd_timestamp() != null ? new Date(queryDTO.getEnd_timestamp()) : null;
        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("requestTime").gte(startTime).lte(endTime));
        }

        String modelName = queryDTO.getModel_name();
        if (StringUtils.isNotBlank(modelName)) {
            query.addCriteria(Criteria.where("modelName").is(modelName));
        }

        String requestId = queryDTO.getRequest_id();
        if (StringUtils.isNotBlank(requestId)) {
            query.addCriteria(Criteria.where("requestId").regex(requestId));
        }

        String codeType = queryDTO.getCode_type();
        if (codeType != null && !"all".equals(codeType)) {
            if (StringUtils.equals("success", codeType)) {
                query.addCriteria(Criteria.where("status").is(ModelInvokeStatus.SUCCESS.getCode()));
            } else {
                query.addCriteria(Criteria.where("status").ne(ModelInvokeStatus.SUCCESS.getCode()));
            }
        }

        Pageable pageable = PageRequest.of(queryDTO.getP() - 1, queryDTO.getPer_page());
        long count = mongoTemplate.count(query, ModelInvokeLog.class);
        List<ModelInvokeLog> logs = mongoTemplate.find(query.with(pageable), ModelInvokeLog.class);

        return new PageImpl<>(logs, pageable, count);
    }

    @Override
    public long deleteLogsBefore(Date date) {
        Query query = new Query();
        query.addCriteria(Criteria.where("requestTime").lt(date));
        return mongoTemplate.remove(query, ModelInvokeLog.class).getDeletedCount();
    }
}