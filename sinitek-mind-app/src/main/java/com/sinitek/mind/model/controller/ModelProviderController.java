package com.sinitek.mind.model.controller;

import com.sinitek.mind.model.dto.ModelProviderInfoDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 模型提供商控制器
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Tag(name = "模型提供商接口", description = "模型提供商相关操作接口")
@RestController
@RequestMapping("/mind/api/model/provider")
public class ModelProviderController {

    @Autowired
    private IModelProviderService modelProviderService;

    /**
     * 获取所有模型提供商信息
     */
    @Operation(summary = "获取所有模型提供商", description = "获取系统支持的所有模型提供商信息")
    @GetMapping
    public ResponseEntity<List<ModelProviderInfoDTO>> getAllProviders() {
        return ResponseEntity.ok(modelProviderService.getAllProviders());
    }

    /**
     * 根据代码获取特定模型提供商信息
     */
    @Operation(summary = "获取指定提供商", description = "根据提供商代码获取特定模型提供商信息")
    @GetMapping("/{code}")
    public ResponseEntity<ModelProviderInfoDTO> getProviderByCode(@PathVariable String code) {
        ModelProviderInfoDTO info = modelProviderService.getProviderByCode(code);
        if (info == null) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(info);
    }

    /**
     * 获取提供商默认URL
     */
    @Operation(summary = "获取提供商默认URL", description = "获取特定提供商的默认API接口地址")
    @GetMapping("/{code}/url")
    public ResponseEntity<String> getProviderDefaultUrl(@PathVariable String code) {
        String url = modelProviderService.getProviderDefaultUrl(code);
        if (url.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        return ResponseEntity.ok(url);
    }
} 