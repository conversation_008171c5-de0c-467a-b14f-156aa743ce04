package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型提供商信息DTO
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Data
@Schema(description = "模型提供商信息")
public class ModelProviderInfoDTO {
    @Schema(description = "提供商代码")
    private String code;

    @Schema(description = "提供商名称")
    private String name;

    @Schema(description = "基础URL")
    private String baseUrl;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "completions路径")
    private String completionsPath;

    public ModelProviderInfoDTO(String code, String name, String baseUrl, String description, String completionsPath) {
        this.code = code;
        this.name = name;
        this.baseUrl = baseUrl;
        this.description = description;
        this.completionsPath = completionsPath;
    }

    public String getCompletionsPath() {
        return completionsPath;
    }
}