package com.sinitek.mind.model.dto;

import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型列表返回DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Data
@JsonPropertyOrder({
    "type", "name", "avatar", "provider", "model", 
    "charsPointsPrice", "inputPrice", "outputPrice",
    "isActive", "isCustom", 
    "contextToken", "vision", "toolChoice"
})
public class ModelListResponse {

    @Schema(description = "模型类型")
    private String type;

    @Schema(description = "模型名称")
    private String name;

    @Schema(description = "模型头像")
    private String avatar;

    @Schema(description = "模型提供商")
    private String provider;

    @Schema(description = "模型标识")
    private String model;

    @Schema(description = "字符点数价格")
    private Double charsPointsPrice;

    @Schema(description = "输入价格")
    private Double inputPrice;

    @Schema(description = "输出价格")
    private Double outputPrice;

    @Schema(description = "是否激活")
    private Boolean isActive;

    @Schema(description = "是否自定义")
    private Boolean isCustom;

    @Schema(description = "上下文token")
    private Integer contextToken;

    @Schema(description = "是否支持视觉")
    private Boolean vision;

    @Schema(description = "是否支持工具选择")
    private Boolean toolChoice;
} 