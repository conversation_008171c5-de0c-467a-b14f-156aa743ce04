package com.sinitek.mind.model.controller;

import com.sinitek.mind.model.core.embedding.IModelEmbeddingService;
import com.sinitek.mind.model.core.llm.service.IModelChatService;
import com.sinitek.mind.model.core.rerank.IModelRerankService;
import com.sinitek.sirm.framework.annotation.ResponseObjectOnly;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.embedding.EmbeddingResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Flux;

import java.util.List;
import java.util.Map;

/**
 * 模型调用控制器
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Tag(name = "模型调用接口", description = "模型调用相关操作接口")
@RestController
@RequestMapping("/mind/api/core/ai/model-invoke")
public class ModelInvokeController {

    @Autowired
    private IModelChatService modelChatService;

    @Autowired
    private IModelEmbeddingService modelEmbeddingService;

//    @Autowired
//    private IModelTTSService modelTTSService;
//
//    @Autowired
//    private IModelSTTService modelSTTService;

    @Autowired
    private IModelRerankService modelRerankService;

    /**
     * 聊天接口
     */
    @Operation(summary = "模型聊天", description = "与指定模型进行聊天")
    @PostMapping("/chat")
    public ResponseEntity<String> chat(@RequestParam String message, @RequestParam String model) {
        return ResponseEntity.ok(modelChatService.chat(message, model));
    }

    /**
     * 异步聊天接口
     */
    @Operation(summary = "异步模型聊天", description = "与指定模型进行异步聊天")
    @GetMapping(value = "/chat-async", produces = "text/html;charset=UTF-8")
    @ResponseObjectOnly
    public Flux<String> chatAsync(@RequestParam String message, @RequestParam String model) {
        Prompt prompt = new Prompt(new UserMessage(message));
        return modelChatService.chatAsync(prompt, model).map(chatResponse -> chatResponse.getResult().getOutput().getText());
    }

    /**
     * 文本向量嵌入接口
     */
    @Operation(summary = "文本向量嵌入", description = "将文本转换为向量表示")
    @PostMapping("/embed")
    public ResponseEntity<float[]> embed(@RequestParam String text, @RequestParam String model) {
        return ResponseEntity.ok(modelEmbeddingService.embed(text, model));
    }

    /**
     * 批量文本向量嵌入接口
     */
    @Operation(summary = "批量文本向量嵌入", description = "批量将多个文本转换为向量表示")
    @PostMapping("/embed/batch")
    public ResponseEntity<EmbeddingResponse> embedBatch(@RequestBody List<String> texts, @RequestParam String model) {
        return ResponseEntity.ok(modelEmbeddingService.embedBatch(texts, model));
    }

//    /**
//     * 文本转语音接口
//     */
//    @Operation(summary = "文本转语音", description = "将文本转换为语音")
//    @PostMapping("/tts")
//    public ResponseEntity<byte[]> textToSpeech(@RequestParam String text, @RequestParam String model, @RequestParam(required = false) String voice) {
//        return ResponseEntity.ok(modelTTSService.textToSpeech(text, model, voice));
//    }

//    /**
//     * 语音转文本接口
//     */
//    @Operation(summary = "语音转文本", description = "将语音转换为文本")
//    @PostMapping("/stt")
//    public ResponseEntity<String> speechToText(@RequestParam MultipartFile audioFile, @RequestParam String model) throws IOException {
//        return ResponseEntity.ok(modelSTTService.speechToText(audioFile.getBytes(), model));
//    }
//
    /**
     * 文档重排序接口
     */
    @Operation(summary = "文档重排序", description = "对搜索结果进行重排序")
    @PostMapping("/rerank")
    public ResponseEntity<List<Map<String, Object>>> rerank(@RequestParam String query, @RequestBody List<String> documents, @RequestParam String model) {
        return ResponseEntity.ok(modelRerankService.rerank(query, documents, model));
    }
} 