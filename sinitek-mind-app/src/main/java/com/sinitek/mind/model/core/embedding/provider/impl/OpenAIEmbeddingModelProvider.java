package com.sinitek.mind.model.core.embedding.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.core.embedding.provider.EmbeddingModelProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.document.MetadataMode;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.model.NoopApiKey;
import org.springframework.ai.openai.OpenAiEmbeddingModel;
import org.springframework.ai.openai.OpenAiEmbeddingOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * OpenAI嵌入模型提供商实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class OpenAIEmbeddingModelProvider implements EmbeddingModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.OPENAI;
    }

    @Override
    public EmbeddingModel createEmbeddingModel(SystemModelDTO modelDTO) {
        String defaultUrl = modelProviderService.getProviderDefaultUrl(getProviderType());
        String baseUrl = StringUtils.defaultIfBlank(modelDTO.getRequestUrl(), defaultUrl);
        String apiKey = modelDTO.getRequestAuth();

        OpenAiApi openAiApi = createOpenAiApi(baseUrl, apiKey);
        OpenAiEmbeddingOptions embeddingOptions = createEmbeddingOptions(modelDTO);
        return new OpenAiEmbeddingModel(openAiApi, MetadataMode.EMBED, embeddingOptions);
    }

    /**
     * 创建OpenAiApi
     * 子类可以重写此方法自定义OpenAiApi
     */
    protected OpenAiApi createOpenAiApi(String baseUrl, String apiKey) {
        OpenAiApi.Builder builder = OpenAiApi.builder().baseUrl(baseUrl);
        if (StringUtils.isNotBlank(apiKey)) {
            builder.apiKey(apiKey);
        } else {
            builder.apiKey(new NoopApiKey());
        }
        return builder.build();
    }

    /**
     * 创建OpenAiEmbeddingOptions
     *  - 子类可以重写此方法自定义OpenAiEmbeddingOptions
     * @param modelDTO
     * @return
     */
    protected OpenAiEmbeddingOptions createEmbeddingOptions(SystemModelDTO modelDTO) {
        OpenAiEmbeddingOptions options = new OpenAiEmbeddingOptions();
        // 设置模型名称
        options.setModel(modelDTO.getModel());
        return options;
    }
} 