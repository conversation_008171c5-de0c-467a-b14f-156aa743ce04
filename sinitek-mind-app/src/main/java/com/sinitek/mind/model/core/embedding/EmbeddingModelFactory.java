package com.sinitek.mind.model.core.embedding;

import com.sinitek.mind.model.core.embedding.provider.EmbeddingModelProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 索引模型工厂层
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class EmbeddingModelFactory {

    @Autowired
    private ISystemModelService systemModelService;
    
    @Autowired
    private List<EmbeddingModelProvider> embeddingModelProviders;

    /**
     * 根据模型ID创建索引模型
     * @param modelId 模型ID
     * @return 索引模型实例
     */
    public EmbeddingModel embeddingModel(String modelId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new IllegalArgumentException("模型不存在: " + modelId);
        }

        String provider = modelDTO.getProvider();
        
        if (StringUtils.isBlank(provider)) {
            throw new IllegalArgumentException("模型提供商不能为空");
        }
        
        // 查找对应的提供商实现
        for (EmbeddingModelProvider embeddingModelProvider : embeddingModelProviders) {
            if (embeddingModelProvider.supports(provider)) {
                return embeddingModelProvider.createEmbeddingModel(modelDTO);
            }
        }
        
        throw new IllegalArgumentException("不支持的索引模型提供商: " + provider);
    }
} 