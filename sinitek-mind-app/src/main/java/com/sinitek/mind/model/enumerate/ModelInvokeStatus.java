package com.sinitek.mind.model.enumerate;

/**
 * 模型执行状态
 *
 * <AUTHOR>
 * date 2025-07-14
 */
public enum ModelInvokeStatus {

    SUCCESS(200, "成功"),

    FAILURE(500, "失败");

    private final int code;
    private final String description;

    ModelInvokeStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    public int getCode() {
        return code;
    }

    public String getDescription() {
        return description;
    }
} 