package com.sinitek.mind.model.support;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.model.constant.ModelTypeConstant;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.enumerate.ModelTypeEnum;
import com.sinitek.mind.model.repository.SystemModelRepository;
import com.sinitek.mind.model.service.ISystemModelService;
import com.sinitek.mind.system.dto.DefaultModelsDTO;
import com.sinitek.sirm.common.spring.SpringFactory;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.core.io.support.ResourcePatternResolver;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 模型配置管理器
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：负责加载和管理所有模型配置的单例类
 */
@Slf4j
@Component
public class ModelConfigManager {

    @Autowired
    private SystemModelRepository systemModelRepository;
    
    @Autowired
    private ISystemModelService systemModelService;
    
    @Autowired
    private ObjectMapper objectMapper;

    private static final List<SystemModelDTO> systemModelList = new ArrayList<>();
    private static final Map<String, SystemModelDTO> llmModelMap = new ConcurrentHashMap<>();
    private static final Map<String, SystemModelDTO> embeddingModelMap = new ConcurrentHashMap<>();
    private static final Map<String, SystemModelDTO> ttsModelMap = new ConcurrentHashMap<>();
    private static final Map<String, SystemModelDTO> sttModelMap = new ConcurrentHashMap<>();
    private static final Map<String, SystemModelDTO> reRankModelMap = new ConcurrentHashMap<>();
    private static volatile DefaultModelsDTO defaultModels;

    /**
     * 加载系统全部模型
     */
    public void loadAllModels() {
        // 1. 加载系统静态的模型（系统启动后就不会变化）
        List<SystemModelDTO> systemModelDTOList = loadAllSystemModelProvider();
        systemModelList.clear();
        systemModelList.addAll(systemModelDTOList);

        // 2. 加载不同模型分类Map的缓存
        List<SystemModelDTO> systemActiveModelList = getSystemActiveModelList();
        initModelMapCache(systemActiveModelList);
    }

    /**
     * 更新模型的缓存
     */
    public synchronized static void updateModelCache() {
        List<SystemModelDTO> systemActiveModelList = getSystemActiveModelList();
        // 不同模型分类更新
        initModelMapCache(systemActiveModelList);

        // 不同模型的默认模型配置更新
        getLatestDefaultModels(systemActiveModelList);
    }

    public synchronized static Map<String, SystemModelDTO> getLlmModelMap() {
        return llmModelMap;
    }

    public synchronized static Map<String, SystemModelDTO> getEmbeddingModelMap() {
        return embeddingModelMap;
    }

    public synchronized static Map<String, SystemModelDTO> getTtsModelMap() {
        return ttsModelMap;
    }

    public synchronized static Map<String, SystemModelDTO> getSttModelMap() {
        return sttModelMap;
    }

    public synchronized static Map<String, SystemModelDTO> getReRankModelMap() {
        return reRankModelMap;
    }

    /**
     * 获取getDefaultModels
     * @return
     */
    public synchronized static DefaultModelsDTO getDefaultModels() {
        if (ObjectUtils.isEmpty(defaultModels)) {
            getLatestDefaultModels(getSystemActiveModelList());
        }
        return defaultModels;
    }

    /**
     * 获取最新的默认模型配置
     *
     * @param activeModelList
     * @return
     */
    public static DefaultModelsDTO getLatestDefaultModels(List<SystemModelDTO> activeModelList) {
        DefaultModelsDTO tempDefaultModels = new DefaultModelsDTO();
        Map<String, SystemModelDTO> llmModelMap = ModelConfigManager.getLlmModelMap();
        Map<String, SystemModelDTO> embeddingModelMap = ModelConfigManager.getEmbeddingModelMap();

        // 先从标记为默认的模型中设置
        for (SystemModelDTO model : activeModelList) {
            if (ModelTypeEnum.LLM.getCode().equals(model.getType()) && Boolean.TRUE.equals(model.getIsDefault())) {
                tempDefaultModels.setLlm(model);
            } else if (ModelTypeEnum.LLM.getCode().equals(model.getType()) && Boolean.TRUE.equals(model.getIsDefaultDatasetTextModel())) {
                tempDefaultModels.setDatasetTextLLM(model);
            } else if (ModelTypeEnum.LLM.getCode().equals(model.getType()) && Boolean.TRUE.equals(model.getIsDefaultDatasetImageModel())) {
                tempDefaultModels.setDatasetImageLLM(model);
            } else if (ModelTypeEnum.EMBEDDING.getCode().equals(model.getType()) && Boolean.TRUE.equals(model.getIsDefault())) {
                tempDefaultModels.setEmbedding(model);
            }
        }

        // 如果没有设置默认模型，则从列表中选择第一个
        if (tempDefaultModels.getLlm() == null && MapUtils.isNotEmpty(llmModelMap)) {
            tempDefaultModels.setLlm(llmModelMap.values().stream().findFirst().orElse(null));
        }

        if (tempDefaultModels.getDatasetTextLLM() == null && MapUtils.isNotEmpty(llmModelMap)) {
            tempDefaultModels.setDatasetTextLLM(llmModelMap.values().stream()
                    .filter(model -> Boolean.TRUE.equals(model.getDatasetProcess()))
                    .findFirst().orElse(null));
        }

        if (tempDefaultModels.getDatasetImageLLM() == null && MapUtils.isNotEmpty(llmModelMap)) {
            tempDefaultModels.setDatasetImageLLM(llmModelMap.values().stream()
                    .filter(model -> Boolean.TRUE.equals(model.getVision()))
                    .findFirst().orElse(null));
        }

        if (tempDefaultModels.getEmbedding() == null && MapUtils.isNotEmpty(embeddingModelMap)) {
            tempDefaultModels.setEmbedding(embeddingModelMap.values().stream().findFirst().orElse(null));
        }

        if (tempDefaultModels.getReRank() == null && MapUtils.isNotEmpty(reRankModelMap)) {
            tempDefaultModels.setReRank(reRankModelMap.values().stream().findFirst().orElse(null));
        }

        defaultModels = tempDefaultModels;
        return tempDefaultModels;
    }

    public static List<SystemModelDTO> getSystemModelList() {
        return systemModelList;
    }

    /**
     * 获取激活的模型列表，取最新的
     * @return
     */
    public static List<SystemModelDTO> getSystemActiveModelList() {
        return SpringFactory.getBean(ISystemModelService.class).findActiveModelList();
    }

    /**
     * 转换模型的meta属性为SystemModelDTO
     * @param meta
     * @return
     */
    private SystemModelDTO convertMetaToSystemModelDTO(Map<String, Object> meta) {
        SystemModelDTO systemModelDTO = objectMapper.convertValue(meta, SystemModelDTO.class);

        // 将部分字段成null转换为其他格式，防止在前端直接展示为null
        if (CollectionUtils.isEmpty(systemModelDTO.getResponseFormatList())) {
            systemModelDTO.setResponseFormatList(new ArrayList<>());
        }
        if (MapUtils.isEmpty(systemModelDTO.getDefaultConfig())) {
            systemModelDTO.setDefaultConfig(new HashMap<>());
        }

        return systemModelDTO;
    }

    /**
     * 初始化ModelMap的缓存
     */
    public static void initModelMapCache(List<SystemModelDTO> systemActiveModelList) {
        llmModelMap.clear();
        embeddingModelMap.clear();
        ttsModelMap.clear();
        sttModelMap.clear();
        reRankModelMap.clear();
        for (SystemModelDTO systemModelDTO : systemActiveModelList) {
            registerModel(systemModelDTO);
        }
    }


    /**
     * 注册模型到全局变量
     */
    private static void registerModel(SystemModelDTO item) {
        if (Boolean.TRUE.equals(item.getIsActive())) {
            switch (item.getType()) {
                case ModelTypeConstant.LLM_CODE:
                    llmModelMap.put(item.getModel(), item);
                    llmModelMap.put(item.getName(), item);
                    break;
                case ModelTypeConstant.EMBEDDING_CODE:
                    embeddingModelMap.put(item.getModel(), item);
                    embeddingModelMap.put(item.getName(), item);
                    break;
                case ModelTypeConstant.TTS_CODE:
                    ttsModelMap.put(item.getModel(), item);
                    ttsModelMap.put(item.getName(), item);
                    break;
                case ModelTypeConstant.STT_CODE:
                    sttModelMap.put(item.getModel(), item);
                    sttModelMap.put(item.getName(), item);
                    break;
                case ModelTypeConstant.RERANK_CODE:
                    reRankModelMap.put(item.getModel(), item);
                    reRankModelMap.put(item.getName(), item);
                    break;
                default:
                    break;
            }
        }
    }

    /**
     * 获取全部静态的系统模型数据
     *
     * @return
     */
    private List<SystemModelDTO> loadAllSystemModelProvider() {
        List<SystemModelDTO> systemModelList = new ArrayList<>();
        // 再加载本地 provider 目录
         List<Resource> resources;
         try {
             ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
             resources = Arrays.asList(resolver.getResources("classpath*:model/provider/**/*"));
         } catch (IOException e) {
             log.error("加载模型 provider 资源失败", e);
             return new ArrayList<>();
         }

         if (resources.isEmpty()) {
             log.warn("模型 provider 目录不存在或为空");
             return new ArrayList<>();
         }
        for (Resource res : resources) {
            try (InputStream is = res.getInputStream()) {
                JsonNode root = objectMapper.readTree(is);
                String provider = root.get("provider").asText();
                JsonNode listNode = root.get("list");
                if (listNode == null || !listNode.isArray()) {
                    continue;
                }
                List<SystemModelDTO> models = objectMapper.readValue(listNode.toString(), new TypeReference<List<SystemModelDTO>>() {});
                for (SystemModelDTO model : models) {
                    model.setProvider(provider);
                    model.setIsCustom(false);
                    systemModelList.add(model);
                }
            } catch (IOException e) {
                log.error("加载模型文件失败: {}", res.getFilename(), e);
            }
        }
        return systemModelList;
    }

}