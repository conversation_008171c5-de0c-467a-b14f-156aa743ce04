package com.sinitek.mind.model.core.rerank;

import org.springframework.ai.chat.prompt.Prompt;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 系统重排模型服务层
 *
 * <AUTHOR>
 * date 2025-07-07
 */
public interface IModelRerankService {

    /**
     * 文档重排序
     * @param query 查询语句
     * @param documents 待重排序的文档列表
     * @param model 模型标识
     * @return 重排序后的文档得分列表
     */
    List<Map<String, Object>> rerank(String query, List<String> documents, String model);

    /**
     * 异步文档重排序
     * @param query 查询语句
     * @param documents 待重排序的文档列表
     * @param model 模型标识
     * @return 异步重排序后的文档得分列表
     */
    CompletableFuture<List<Map<String, Object>>> rerankAsync(String query, List<String> documents, String model);
    
    /**
     * 使用自定义Prompt进行文档重排序
     * @param prompt 包含查询和上下文的提示对象
     * @param documents 待重排序的文档列表
     * @param model 模型标识
     * @return 重排序后的文档得分列表
     */
    List<Map<String, Object>> rerank(Prompt prompt, List<String> documents, String model);
    
    /**
     * 异步使用自定义Prompt进行文档重排序
     * @param prompt 包含查询和上下文的提示对象
     * @param documents 待重排序的文档列表
     * @param model 模型标识
     * @return 异步重排序后的文档得分列表
     */
    CompletableFuture<List<Map<String, Object>>> rerankAsync(Prompt prompt, List<String> documents, String model);
} 