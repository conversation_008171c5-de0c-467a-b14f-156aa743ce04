package com.sinitek.mind.model.core.llm.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.SystemModelDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.model.NoopApiKey;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

/**
 * Other聊天模型提供商
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class OtherChatModelProvider extends OpenAIChatModelProvider {

    @Override
    public String getProviderType() {
        return ModelProviderConstant.OTHER;
    }

    @Override
    public ChatModel createChatModel(SystemModelDTO modelDTO) {
        if (StringUtils.isBlank(modelDTO.getRequestUrl())) {
            throw new IllegalArgumentException("自定义模型必须提供请求地址");
        }
        
        return super.createChatModel(modelDTO);
    }

    @Override
    protected OpenAiApi createOpenAiApi(String baseUrl, String apiKey, String completionsPath) {
        OpenAiApi.Builder builder = OpenAiApi.builder().baseUrl(baseUrl);
        if (StringUtils.isNotBlank(completionsPath)) {
            builder.completionsPath(completionsPath);
        }

        if (StringUtils.isNotBlank(apiKey)) {
            builder.apiKey(apiKey);
        } else {
            builder.apiKey(new NoopApiKey());
        }

        // 指定restClientBuilder，因为Spring Ai默认的restClientBuilder无法连接Xinference
        SimpleClientHttpRequestFactory simpleClientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        RestClient.Builder requestBuilder = RestClient.builder();
        requestBuilder.requestFactory(simpleClientHttpRequestFactory);
        builder.restClientBuilder(requestBuilder);

        return builder.build();
    }
}