package com.sinitek.mind.model.core.embedding.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.SystemModelDTO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.model.NoopApiKey;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestClient;

/**
 * 自定义嵌入模型提供商实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class OtherEmbeddingModelProvider extends OpenAIEmbeddingModelProvider {

    @Override
    public String getProviderType() {
        return ModelProviderConstant.OTHER;
    }

    @Override
    public EmbeddingModel createEmbeddingModel(SystemModelDTO modelDTO) {
        if (StringUtils.isBlank(modelDTO.getRequestUrl())) {
            throw new IllegalArgumentException("自定义模型必须提供请求地址");
        }

        return super.createEmbeddingModel(modelDTO);
    }

    @Override
    protected OpenAiApi createOpenAiApi(String baseUrl, String apiKey) {
        OpenAiApi.Builder builder = OpenAiApi.builder().baseUrl(baseUrl);
        if (StringUtils.isNotBlank(apiKey)) {
            builder.apiKey(apiKey);
        } else {
            builder.apiKey(new NoopApiKey());
        }

        // 指定restClientBuilder，因为Spring Ai默认的restClientBuilder无法连接Xinference
        SimpleClientHttpRequestFactory simpleClientHttpRequestFactory = new SimpleClientHttpRequestFactory();
        RestClient.Builder requestBuilder = RestClient.builder();
        requestBuilder.requestFactory(simpleClientHttpRequestFactory);
        builder.restClientBuilder(requestBuilder);

        return builder.build();
    }
}