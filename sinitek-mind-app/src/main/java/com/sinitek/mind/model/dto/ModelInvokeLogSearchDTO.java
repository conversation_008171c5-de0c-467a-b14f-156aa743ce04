package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型调用日志查询参数DTO
 *
 * <AUTHOR>
 * date 2025-07-30
 */
@Data
@Schema(description = "模型调用日志查询参数DTO")
public class ModelInvokeLogSearchDTO {

    @Schema(description = "模型名称")
    private String model_name;

    @Schema(description = "状态码")
    private String code_type;

    @Schema(description = "开始时间戳")
    private Long start_timestamp;

    @Schema(description = "结束时间戳")
    private Long end_timestamp;

    @Schema(description = "是否只返回结果")
    private boolean result_only;

    @Schema(description = "请求ID")
    private String request_id;

    @Schema(description = "页码")
    private int p = 1;

    @Schema(description = "每页大小")
    private int per_page = 20;
}