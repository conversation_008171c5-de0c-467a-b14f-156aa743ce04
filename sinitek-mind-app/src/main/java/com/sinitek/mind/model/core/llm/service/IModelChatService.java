package com.sinitek.mind.model.core.llm.service;

import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import reactor.core.publisher.Flux;

import java.util.List;

/**
 * 系统模型Chat服务层
 *
 * <AUTHOR>
 * date 2025-07-04
 */
public interface IModelChatService {

    /**
     * 基础聊天
     * @param message 用户消息
     * @param model 模型标识
     * @return 模型响应内容
     */
    String chat(String message, String model);

    /**
     * 基础聊天
     * @param message 用户消息
     * @param model 模型标识
     * @param datasetIdList 知识库id列表
     * @return 模型响应内容
     */
    String chat(String message, String model, List<String> datasetIdList);

    /**
     * 基础聊天（使用Prompt）
     * @param prompt 提示对象
     * @param model 模型标识
     * @return 模型响应对象，包含完整的响应内容及元数据
     */
    ChatResponse chat(Prompt prompt, String model);

    /**
     * 基础聊天（使用Prompt）
     * @param prompt 提示对象
     * @param model 模型标识
     * @param datasetIdList 知识库id列表
     * @return 模型响应对象，包含完整的响应内容及元数据
     */
    ChatResponse chat(Prompt prompt, String model, List<String> datasetIdList);

    /**
     * 异步聊天（使用Prompt）
     *
     * @param prompt 提示对象
     * @param model  模型标识
     * @return 异步响应结果
     */
    Flux<ChatResponse> chatAsync(Prompt prompt, String model);
}
