package com.sinitek.mind.model.util;

import org.apache.commons.lang3.StringUtils;

/**
 * Token计算工具类
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public class TokenCalculatorUtil {

    /**
     * 英文字符平均token比例（每个单词约占1.3个token）
     */
    private static final double ENGLISH_WORD_TOKEN_RATIO = 1.3;
    
    /**
     * 中文字符平均token比例（每个汉字约占1.5个token）
     */
    private static final double CHINESE_CHAR_TOKEN_RATIO = 1.5;

    /**
     * 估算文本的token数量
     * 注意：这是一个粗略估算，实际token数取决于具体的tokenizer实现
     * 
     * @param text 待计算的文本
     * @return 估算的token数量
     */
    public static int estimateTokens(String text) {
        if (StringUtils.isBlank(text)) {
            return 0;
        }
        
        // 计算英文单词数
        int englishWordCount = countEnglishWords(text);
        
        // 计算中文字符数
        int chineseCharCount = countChineseChars(text);
        
        // 计算其他字符数（标点、数字等）
        int otherCharCount = text.length() - englishWordCount - chineseCharCount;
        
        // 估算token数 = 英文单词数*英文比例 + 中文字符数*中文比例 + 其他字符数
        return (int) Math.ceil(englishWordCount * ENGLISH_WORD_TOKEN_RATIO 
                + chineseCharCount * CHINESE_CHAR_TOKEN_RATIO 
                + otherCharCount);
    }

    /**
     * 统计英文单词数
     * @param text 文本
     * @return 英文单词数
     */
    private static int countEnglishWords(String text) {
        if (StringUtils.isBlank(text)) {
            return 0;
        }
        
        // 使用正则表达式匹配英文单词
        String[] words = text.split("\\s+");
        int wordCount = 0;
        
        for (String word : words) {
            if (word.matches("[a-zA-Z0-9]+")) {
                wordCount++;
            }
        }
        
        return wordCount;
    }

    /**
     * 统计中文字符数
     * @param text 文本
     * @return 中文字符数
     */
    private static int countChineseChars(String text) {
        if (StringUtils.isBlank(text)) {
            return 0;
        }
        
        int count = 0;
        for (char c : text.toCharArray()) {
            if (isChinese(c)) {
                count++;
            }
        }
        
        return count;
    }

    /**
     * 判断字符是否是中文
     * @param c 字符
     * @return 是否是中文
     */
    private static boolean isChinese(char c) {
        Character.UnicodeBlock ub = Character.UnicodeBlock.of(c);
        return ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_COMPATIBILITY_IDEOGRAPHS
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_A
                || ub == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS_EXTENSION_B
                || ub == Character.UnicodeBlock.CJK_SYMBOLS_AND_PUNCTUATION
                || ub == Character.UnicodeBlock.HALFWIDTH_AND_FULLWIDTH_FORMS
                || ub == Character.UnicodeBlock.GENERAL_PUNCTUATION;
    }
} 