package com.sinitek.mind.model.service.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.ModelProviderInfoDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import jakarta.annotation.PostConstruct;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 模型提供商服务实现类
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Service
public class ModelProviderServiceImpl implements IModelProviderService {

    private final Map<String, ModelProviderInfoDTO> providerMap = new HashMap<>();
    private final List<ModelProviderInfoDTO> providerList = new ArrayList<>();

    @PostConstruct
    public void init() {
        // 初始化提供商信息
        addProvider(ModelProviderConstant.OPENAI, "OpenAI", "https://api.openai.com", "OpenAI API接口", "/v1/chat/completions");
        addProvider(ModelProviderConstant.CHATGLM, "智谱ChatGLM", "https://open.bigmodel.cn", "智谱ChatGLM API接口", "/api/paas/v4/chat/completions");
        addProvider(ModelProviderConstant.DEEPSEEK, "DeepSeek", "https://api.deepseek.com", "DeepSeek API接口", "/v1/chat/completions");
        addProvider(ModelProviderConstant.QWEN, "阿里千问", "https://dashscope.aliyuncs.com", "阿里千问API接口", "/api/v1/services/aigc/text-generation/generation");
        addProvider(ModelProviderConstant.ERNIE, "百度文心一言", "https://aip.baidubce.com", "百度文心一言API接口", "/rpc/2.0/ai_custom/v1/wenxinworkshop/chat/completions");
        addProvider(ModelProviderConstant.DOUBAO, "字节豆包", "https://api.doubao.com", "字节豆包API接口", "/api/v1/chat/completions");
        addProvider(ModelProviderConstant.SPARK, "讯飞星火", "https://spark-api.xf-yun.com", "讯飞星火API接口", "/v1.1/chat");
        addProvider(ModelProviderConstant.OLLAMA, "Ollama", "http://localhost:11434", "Ollama本地运行的LLM服务", "/api/chat");
        addProvider(ModelProviderConstant.XINFERENCE, "Xinference", "http://localhost:9997", "Xinference本地运行的LLM服务", "/v1/chat/completions");
        addProvider(ModelProviderConstant.OTHER, "自定义模型", "", "自定义API接口", "");
    }

    private void addProvider(String code, String name, String baseUrl, String description, String completionsPath) {
        ModelProviderInfoDTO info = new ModelProviderInfoDTO(code, name, baseUrl, description, completionsPath);
        providerMap.put(code.toLowerCase(), info);
        providerList.add(info);
    }

    @Override
    public List<ModelProviderInfoDTO> getAllProviders() {
        return new ArrayList<>(providerList);
    }

    @Override
    public ModelProviderInfoDTO getProviderByCode(String providerCode) {
        if (StringUtils.isBlank(providerCode)) {
            return null;
        }
        return providerMap.get(providerCode.toLowerCase());
    }

    @Override
    public String getProviderCompletionsPath(String providerCode) {
        ModelProviderInfoDTO info = getProviderByCode(providerCode);
        return info != null ? info.getCompletionsPath() : "";
    }

    @Override
    public String getProviderDefaultUrl(String providerCode) {
        ModelProviderInfoDTO info = getProviderByCode(providerCode);
        return info != null ? info.getBaseUrl() : "";
    }
}