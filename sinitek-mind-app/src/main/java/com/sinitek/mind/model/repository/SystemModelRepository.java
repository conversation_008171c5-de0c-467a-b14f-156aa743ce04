package com.sinitek.mind.model.repository;

import com.sinitek.mind.model.entity.SystemModel;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 系统模型Repository
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Repository
public interface SystemModelRepository extends MongoRepository<SystemModel, String> {

    List<SystemModel> findAll();
    
    /**
     * 根据模型标识查询模型
     *
     * @param model 模型标识
     * @return 模型实体
     */
    Optional<SystemModel> findByModel(String model);
    
    /**
     * 根据模型标识删除模型
     *
     * @param model 模型标识
     */
    void deleteByModel(String model);
} 