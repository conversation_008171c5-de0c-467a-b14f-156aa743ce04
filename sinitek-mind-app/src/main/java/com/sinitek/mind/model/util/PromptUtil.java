package com.sinitek.mind.model.util;

import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.prompt.Prompt;

import java.util.ArrayList;
import java.util.List;

/**
 * Prompt工具类
 *
 * <AUTHOR>
 * date 2025-07-24
 */
public class PromptUtil {

    /**
     * 给Prompt增加一个Message
     * @param prompt
     * @param message
     */
    public static Prompt addMessage(Prompt prompt, Message message) {
        List<Message> messages = new ArrayList<>(prompt.getInstructions());
        messages.add(0, message);
        return new Prompt(messages);
    }

}
