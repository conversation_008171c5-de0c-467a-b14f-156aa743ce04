package com.sinitek.mind.model.core.rerank;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.core.llm.LLMChatModelFactory;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 重排模型工厂层
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class RerankModelFactory {

    @Autowired
    private ISystemModelService systemModelService;
    
    @Autowired
    private LLMChatModelFactory chatModelFactory;

    /**
     * 创建重排模型
     * @param modelId 模型ID
     * @return 重排模型
     */
    public Object rerankModel(String modelId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new IllegalArgumentException("模型不存在: " + modelId);
        }

        String provider = modelDTO.getProvider();
        
        if (StringUtils.isBlank(provider)) {
            throw new IllegalArgumentException("模型提供商不能为空");
        }
        
        switch (provider.toLowerCase()) {
            case ModelProviderConstant.OPENAI:
                return createOpenAiRerankModel(modelDTO);
            case ModelProviderConstant.OTHER:
                return createCustomRerankModel(modelDTO);
            default:
                throw new IllegalArgumentException("不支持的重排模型提供商: " + provider);
        }
    }

    /**
     * 创建OpenAI重排模型
     */
    private Object createOpenAiRerankModel(SystemModelDTO modelDTO) {
        // 暂时返回模型配置，Spring AI目前还没有标准的重排模型接口
        return modelDTO;
    }

    /**
     * 创建自定义重排模型
     */
    private Object createCustomRerankModel(SystemModelDTO modelDTO) {
        // 返回模型配置信息
        return modelDTO;
    }
    
    /**
     * 获取用于重排序的ChatModel
     * @param modelId 模型ID
     * @return ChatModel实例
     */
    public ChatModel getChatModel(String modelId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new IllegalArgumentException("模型不存在: " + modelId);
        }
        
        // 使用LLMChatModelFactory创建ChatModel
        // 这里我们复用已有的聊天模型进行重排序
        return chatModelFactory.chatModel(modelId);
    }
} 