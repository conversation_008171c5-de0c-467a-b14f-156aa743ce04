package com.sinitek.mind.model.service;

import com.sinitek.mind.model.dto.ModelListResponse;
import com.sinitek.mind.model.dto.ModelUpdateDefaultRequest;
import com.sinitek.mind.model.dto.ModelUpdateRequest;
import com.sinitek.mind.model.dto.SystemModelDTO;

import java.util.List;

/**
 * 系统模型服务接口
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：处理模型管理相关的业务逻辑
 */
public interface ISystemModelService {

    /**
     * 获取所有模型列表
     *
     * @return 模型列表
     */
    List<ModelListResponse> listModels();
    
    /**
     * 获取模型详情
     *
     * @param modelId 模型ID
     * @return 模型详情
     */
    SystemModelDTO getModelDetail(String modelId);
    
    /**
     * 测试模型
     *
     * @param model 模型id
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testModel(String model) throws Exception;
    
    /**
     * 更新模型
     *
     * @param updateRequest 更新请求
     * @throws Exception 更新异常
     */
    void updateModel(ModelUpdateRequest updateRequest) throws Exception;
    
    /**
     * 更新默认模型
     *
     * @param updateRequest 更新默认模型请求
     * @throws Exception 更新异常
     */
    void updateDefaultModel(ModelUpdateDefaultRequest updateRequest) throws Exception;
    
    /**
     * 删除模型
     *
     * @param modelId 模型ID
     * @throws Exception 删除异常
     */
    void deleteModel(String modelId) throws Exception;
    
    /**
     * 根据模型ID查找模型
     *
     * @param modelId 模型ID
     * @return 模型对象，如果找不到则返回null
     */
    SystemModelDTO findModelByModelId(String modelId);

    /**
     * 获取所有活跃的模型列表
     *
     * @return 活跃模型列表
     */
    List<SystemModelDTO> findActiveModelList();

    /**
     * 根据modelId找到SystemModelDTO
     * @param modelId
     * @return
     */
    SystemModelDTO getModelByModelId(String modelId);

    /**
     * 更新配置文件
     * @param config
     */
    void updateWithJson(String configStr);
}