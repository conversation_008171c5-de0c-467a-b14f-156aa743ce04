package com.sinitek.mind.model.service.impl;

import com.sinitek.mind.model.entity.ModelInvokeLog;
import com.sinitek.mind.model.enumerate.ModelInvokeStatus;
import com.sinitek.mind.model.enumerate.ModelTypeEnum;
import com.sinitek.mind.model.service.IModelInvokeLogService;
import com.sinitek.sirm.common.spring.SpringFactory;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.ai.chat.metadata.Usage;

import java.util.Date;
import java.util.UUID;

/**
 * 模型调用日志基础抽象类
 *  - 实现每种模型调用的通用部分
 *
 * <AUTHOR>
 * date 2025-07-14
 */
@Slf4j
public abstract class AbstractModelInvokeLogService {

    /**
     * 指定当前模型的ModelTypeEnum
     * @return
     */
    public abstract ModelTypeEnum getCurrentModelTypeEnum();

    /**
     * 创建基础的调用执行日志
     * @param modelName
     * @return
     */
    public ModelInvokeLog createModelInvokeLog(String modelName) {
        ModelTypeEnum modelTypeEnum = this.getCurrentModelTypeEnum();
        String endpoint = modelTypeEnum.getEndpoint();

        ModelInvokeLog log = new ModelInvokeLog();
        log.setModelName(modelName);
        log.setRequestId(UUID.randomUUID().toString().replace("-", ""));
        log.setEndpoint(endpoint);
        log.setRequestTime(new Date());
        log.setIp(SpringMvcUtil.getRequestRealIp());

        // TODO: 调用日志存储当前登录人id，待用户体系明确后补充
        String userId = "system";
        log.setUserId(userId);
        return log;
    }

    /**
     * 保存成功情况下的
     * @param modelInvokeLog
     */
    public void saveSuccessModelInvokeLog(ModelInvokeLog modelInvokeLog) {
        // 流式响应时，当tokens没结果则先不更新
        if (ObjectUtils.isEmpty(modelInvokeLog.getInputTokens()) && ObjectUtils.isEmpty(modelInvokeLog.getOutputTokens())) {
            return;
        }
        modelInvokeLog.setResponseTime(new Date());
        modelInvokeLog.setStatus(ModelInvokeStatus.SUCCESS.getCode());

        IModelInvokeLogService modelInvokeLogService = SpringFactory.getBean(IModelInvokeLogService.class);
        modelInvokeLogService.saveLog(modelInvokeLog);
    }

    /**
     * 保存失败情况下的
     * @param modelInvokeLog
     * @param errorMessage
     */
    public void saveFailModelInvokeLog(ModelInvokeLog modelInvokeLog, String errorMessage) {
        modelInvokeLog.setResponseTime(new Date());
        modelInvokeLog.setStatus(ModelInvokeStatus.FAILURE.getCode());
        modelInvokeLog.setErrorMessage(errorMessage);

        IModelInvokeLogService modelInvokeLogService = SpringFactory.getBean(IModelInvokeLogService.class);
        modelInvokeLogService.saveLog(modelInvokeLog);
    }


    /**
     * 构建Usage
     * @param usage
     * @param modelInvokeLog
     */
    public void buildUsage(Usage usage, ModelInvokeLog modelInvokeLog) {
        Integer promptTokens = usage.getPromptTokens();
        Integer completionTokens = usage.getCompletionTokens();

        log.info("usage.getPromptTokens(): {}, usage.getTotalTokens(): {}, completionTokens: {}", usage.getPromptTokens(), usage.getTotalTokens(), completionTokens);

        // 设置输入/输出的Tokens
        if (ObjectUtils.isNotEmpty(promptTokens)) {
            modelInvokeLog.setInputTokens(promptTokens);
        }
        if (ObjectUtils.isNotEmpty(completionTokens)) {
            modelInvokeLog.setOutputTokens(completionTokens);
        }
    }
}
