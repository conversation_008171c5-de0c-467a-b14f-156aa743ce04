package com.sinitek.mind.model.repository;

import com.sinitek.mind.model.entity.ModelInvokeLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Date;

/**
 * 模型调用日志Repository
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Repository
public interface ModelInvokeLogRepository extends MongoRepository<ModelInvokeLog, String> {

    /**
     * 根据请求ID查询日志
     * @param requestId 请求ID
     * @return 日志对象
     */
    ModelInvokeLog findByRequestId(String requestId);

    /**
     * 根据时间范围查询日志
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<ModelInvokeLog> findByRequestTimeBetween(Date startTime, Date endTime, Pageable pageable);

    /**
     * 根据模型名称查询日志
     * @param modelName 模型名称
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<ModelInvokeLog> findByModelName(String modelName, Pageable pageable);

    /**
     * 根据状态码查询日志
     * @param status 状态码
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<ModelInvokeLog> findByStatus(Integer status, Pageable pageable);

    /**
     * 组合条件查询
     * @param modelName 模型名称
     * @param status 状态码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页日志列表
     */
    Page<ModelInvokeLog> findByModelNameAndStatusAndRequestTimeBetween(
            String modelName, Integer status, Date startTime, Date endTime, Pageable pageable);
} 