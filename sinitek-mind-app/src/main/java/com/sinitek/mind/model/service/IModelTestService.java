package com.sinitek.mind.model.service;

import com.sinitek.mind.model.dto.SystemModelDTO;

import java.util.Map;

/**
 * 模型测试服务接口
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：负责执行不同类型模型的测试
 */
public interface IModelTestService {

    /**
     * 测试模型
     *
     * @param model   模型数据
     * @param headers 请求头
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testModel(SystemModelDTO model, Map<String, String> headers) throws Exception;
    
    /**
     * 测试 LLM 模型
     * 
     * @param modelId 模型ID
     * @param headers 请求头
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testLLMModel(String modelId, Map<String, String> headers) throws Exception;
    
    /**
     * 测试 Embedding 模型
     * 
     * @param modelId 模型ID
     * @param headers 请求头
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testEmbeddingModel(String modelId, Map<String, String> headers) throws Exception;
    
    /**
     * 测试 TTS 模型
     * 
     * @param model 模型数据
     * @param headers 请求头
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testTTSModel(SystemModelDTO model, Map<String, String> headers) throws Exception;
    
    /**
     * 测试 STT 模型
     * 
     * @param model 模型数据
     * @param headers 请求头
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testSTTModel(SystemModelDTO model, Map<String, String> headers) throws Exception;
    
    /**
     * 测试 Rerank 模型
     * 
     * @param modelId 模型ID
     * @param headers 请求头
     * @return 测试结果
     * @throws Exception 测试异常
     */
    Object testReRankModel(String modelId, Map<String, String> headers) throws Exception;
} 