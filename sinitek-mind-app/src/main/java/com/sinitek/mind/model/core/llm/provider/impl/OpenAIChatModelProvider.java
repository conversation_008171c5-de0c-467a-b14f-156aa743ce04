package com.sinitek.mind.model.core.llm.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.core.llm.provider.ChatModelProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.model.NoopApiKey;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.net.URL;
import java.net.MalformedURLException;

/**
 * OpenAI聊天模型提供商
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class OpenAIChatModelProvider implements ChatModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.OPENAI;
    }

    @Override
    public ChatModel createChatModel(SystemModelDTO modelDTO) {
        String providerType = getProviderType();
        String apiKey = modelDTO.getRequestAuth();
        String requestUrl = modelDTO.getRequestUrl();

        String defaultUrl = modelProviderService.getProviderDefaultUrl(providerType);
        String completionsPath = modelProviderService.getProviderCompletionsPath(providerType);

        // 仅对requestUrl进行URL解析处理
        String baseUrl;
        if (StringUtils.isNotBlank(requestUrl)) {
            String[] urlParts = parseUrl(requestUrl);
            baseUrl = urlParts[0];
            completionsPath = urlParts[1];
        } else {
            baseUrl = defaultUrl;
        }

        OpenAiApi openAiApi = createOpenAiApi(baseUrl, apiKey, completionsPath);
        OpenAiChatOptions options = createChatOptions(modelDTO);
        
        return OpenAiChatModel.builder().openAiApi(openAiApi).defaultOptions(options).build();
    }
    
    /**
     * 创建OpenAI API客户端
     * 子类可以重写此方法自定义API客户端
     */
    protected OpenAiApi createOpenAiApi(String baseUrl, String apiKey, String completionsPath) {
        OpenAiApi.Builder builder = OpenAiApi.builder().baseUrl(baseUrl);
        if (StringUtils.isNotBlank(completionsPath)) {
            builder.completionsPath(completionsPath);
        }
        if (StringUtils.isNotBlank(apiKey)) {
            builder.apiKey(apiKey);
        } else {
            builder.apiKey(new NoopApiKey());
        }
        return builder.build();
    }
    
    /**
     * 创建聊天选项
     * 子类可以重写此方法自定义选项
     */
    protected OpenAiChatOptions createChatOptions(SystemModelDTO modelDTO) {
        OpenAiChatOptions options = new OpenAiChatOptions();
        // 设置模型名称
        options.setModel(modelDTO.getModel());
        
        // 最大上下文，控制模型可处理的输入内容总长度
        if (ObjectUtils.isNotEmpty(modelDTO.getMaxContext())) {
            options.setMaxTokens(modelDTO.getMaxContext());
        }

        // 最大响应tokens，限制模型生成回复的最大长度
        if (ObjectUtils.isNotEmpty(modelDTO.getMaxResponse())) {
            options.setMaxTokens(modelDTO.getMaxResponse());
        }
        
        // 最大温度，控制输出的随机性，值越高创造性越强，值越低回答越确定和一致
        if (ObjectUtils.isNotEmpty(modelDTO.getMaxTemperature())) {
            options.setTemperature(modelDTO.getMaxTemperature().doubleValue());
        }
        
        // Top-p参数，控制模型选择词汇的概率阈值，是temperature的替代方案
//        if (ObjectUtils.isNotEmpty(modelDTO.getTopP())) {
//            options.setTopP(modelDTO.getTopP().doubleValue());
//        }
//
//        // 停止序列，设置模型何时停止生成内容的标记
//        if (CollectionUtils.isNotEmpty(modelDTO.getStopSequences())) {
//            options.setStop(modelDTO.getStopSequences());
//        }
        
        // 响应格式，指定模型回复的格式类型，如text或json_object
//        List<String> responseFormatList = modelDTO.getResponseFormatList();
//        if (CollectionUtils.isNotEmpty(responseFormatList)) {
//            options.setResponseFormat(responseFormatList);
//        }
        
        // 支持工具调用，开启后模型可以调用外部工具
//        if (modelDTO.isSupportTools()) {
//            options.setTools(modelDTO.getTools());
//        }
//
        // 支持图片识别，开启后模型可以处理图片输入
//        Boolean vision = modelDTO.getVision();
//        if (vision) {
//            options.setVision(true);
//        }
        
        // 支持输出思考，开启后模型可以展示思考过程
        Boolean reasoning = modelDTO.getReasoning();
//        if (modelDTO.isSupportThinking()) {
//            // 注意：这个可能需要特定模型支持或自定义实现
//        }
        

        // 默认提示词，系统默认的指令提示
//        String defaultSystemChatPrompt = modelDTO.getDefaultSystemChatPrompt();
//        if (StringUtils.isNotBlank(defaultSystemChatPrompt)) {
//        }
//
        return options;
}

private String[] parseUrl(String url) {
    try {
        URL parsedUrl = new URL(url);
        String baseUrl = parsedUrl.getProtocol() + "://" + parsedUrl.getHost();
        if (parsedUrl.getPort() != -1) {
            baseUrl += ":" + parsedUrl.getPort();
        }
        String path = parsedUrl.getPath();
        if (path == null || path.isEmpty() || path.equals("/")) {
            return new String[]{baseUrl, null};
        } else {
            return new String[]{baseUrl, path};
        }
    } catch (MalformedURLException e) {
        // 处理URL格式错误，返回原始URL和null
        return new String[]{url, null};
    }
}
}