package com.sinitek.mind.model.core.llm.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.service.IModelProviderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 阿里千问聊天模型提供商
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Component
public class QwenChatModelProvider extends OpenAIChatModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.QWEN;
    }
} 