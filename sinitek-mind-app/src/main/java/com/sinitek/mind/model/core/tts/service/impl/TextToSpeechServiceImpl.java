package com.sinitek.mind.model.core.tts.service.impl;

import com.sinitek.mind.model.core.tts.TTSModelFactory;
import com.sinitek.mind.model.core.tts.provider.TextToSpeechProvider;
import com.sinitek.mind.model.core.tts.service.ITextToSpeechService;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.OutputStream;
import java.util.concurrent.CompletableFuture;

/**
 * 文本转语音服务实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Service
public class TextToSpeechServiceImpl implements ITextToSpeechService {

    @Autowired
    private TTSModelFactory ttsModelFactory;
    
    @Autowired
    private ISystemModelService systemModelService;

    @Override
    public byte[] textToSpeech(String text, String model, String voiceId) {
        return ttsModelFactory.generateSpeech(text, model, voiceId);
    }

    @Override
    public void textToSpeechToStream(String text, String model, String voiceId, OutputStream outputStream) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(model);
        TextToSpeechProvider provider = ttsModelFactory.getProvider(model, voiceId);
        provider.generateSpeechToStream(text, modelDTO, voiceId, outputStream);
    }

    @Override
    public CompletableFuture<byte[]> textToSpeechAsync(String text, String model, String voiceId) {
        return CompletableFuture.supplyAsync(() -> textToSpeech(text, model, voiceId));
    }
}