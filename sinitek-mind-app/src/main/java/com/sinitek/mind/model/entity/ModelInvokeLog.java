package com.sinitek.mind.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 模型调用日志实体
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Data
@Document(collection = "model_invoke_log")
public class ModelInvokeLog {

    @Id
    @Schema(description = "记录ID")
    private String id;

    @Schema(description = "请求ID")
    private String requestId;

    @Schema(description = "模型名称")
    private String modelName;

    @Schema(description = "调用的接口地址")
    private String endpoint;

    @Schema(description = "请求时间")
    private Date requestTime;

    @Schema(description = "响应时间")
    private Date responseTime;

    @Schema(description = "状态码")
    private Integer status;

    @Schema(description = "输入token数量")
    private Integer inputTokens;

    @Schema(description = "输出token数量")
    private Integer outputTokens;

    @Schema(description = "调用用户ID")
    private String userId;

    @Schema(description = "失败时的消息")
    private String errorMessage;

    @Schema(description = "客户端IP")
    private String ip;
} 