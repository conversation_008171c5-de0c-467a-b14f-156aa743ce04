package com.sinitek.mind.model.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 系统模型项 DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：兼容 LLM/Embedding/TTS/STT/Rerank 各类模型的字段
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@Schema(description = "系统模型项")
@Data
public class SystemModelDTO {

    @Schema(description = "模型提供商")
    private String provider;

    @Schema(description = "模型标识")
    private String model;

    @Schema(description = "模型名称")
    private String name;

    @Schema(description = "模型类型，如 llm/embedding/tts/stt/rerank")
    private String type;

    @Schema(description = "模型头像")
    private String avatar;

    @Schema(description = "是否激活")
    private Boolean isActive;

    @Schema(description = "是否自定义")
    private Boolean isCustom;

    @Schema(description = "是否默认")
    private Boolean isDefault;

    @Schema(description = "是否默认文本模型")
    private Boolean isDefaultDatasetTextModel;

    @Schema(description = "是否默认图片模型")
    private Boolean isDefaultDatasetImageModel;

    @Schema(description = "请求地址")
    private String requestUrl;

    @Schema(description = "请求认证")
    private String requestAuth;

    // LLM 专属
    @Schema(description = "最大上下文长度")
    private Integer maxContext;

    @Schema(description = "最大回复长度")
    private Integer maxResponse;

    @Schema(description = "最大引用 Token")
    private Integer quoteMaxToken;

    @Schema(description = "最大温度")
    private Double maxTemperature;

    @Schema(description = "是否展示 TopP")
    private Boolean showTopP;

    @Schema(description = "回复格式列表")
    private List<String> responseFormatList;

    @Schema(description = "是否展示停止符号")
    private Boolean showStopSign;

    @Schema(description = "是否审核")
    private Boolean censor;

    @Schema(description = "是否支持视觉")
    private Boolean vision;

    @Schema(description = "是否支持推理")
    private Boolean reasoning;

    @Schema(description = "数据集处理能力")
    private Boolean datasetProcess;

    @Schema(description = "是否用于分类")
    private Boolean usedInClassify;

    @Schema(description = "是否用于字段抽取")
    private Boolean usedInExtractFields;

    @Schema(description = "是否用于工具调用")
    private Boolean usedInToolCall;

    @Schema(description = "函数调用能力")
    private Boolean functionCall;

    @Schema(description = "工具选择能力")
    private Boolean toolChoice;

    @Schema(description = "默认系统提示词")
    private String defaultSystemChatPrompt;

    @Schema(description = "默认配置")
    private Map<String, Object> defaultConfig;

    @Schema(description = "字段映射")
    private Map<String, String> fieldMap;

    // Embedding 专属
    @Schema(description = "默认分词 Token 数")
    private Integer defaultToken;

    @Schema(description = "最大 Token 数")
    private Integer maxToken;

    @Schema(description = "训练权重")
    private Integer weight;

    @Schema(description = "是否隐藏")
    private Boolean hidden;

    @Schema(description = "是否归一化")
    private Boolean normalization;

    @Schema(description = "存储自定义参数")
    private Map<String, Object> dbConfig;

    @Schema(description = "查询自定义参数")
    private Map<String, Object> queryConfig;

    // TTS 专属
    @Schema(description = "语音列表")
    private List<ModelVoiceDTO> voices;

    // 价格相关
    @Schema(description = "每千字符价格")
    private Double charsPointsPrice;

    @Schema(description = "输入价格")
    private Double inputPrice;

    @Schema(description = "输出价格")
    private Double outputPrice;

} 