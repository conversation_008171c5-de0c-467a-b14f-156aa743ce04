package com.sinitek.mind.model.core.embedding.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.IModelProviderService;
import io.micrometer.observation.ObservationRegistry;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.model.tool.ToolCallingManager;
import org.springframework.ai.ollama.OllamaEmbeddingModel;
import org.springframework.ai.ollama.api.OllamaApi;
import org.springframework.ai.ollama.api.OllamaOptions;
import org.springframework.ai.ollama.management.ModelManagementOptions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Ollama嵌入模型提供商
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class OllamaEmbeddingModelProvider extends OpenAIEmbeddingModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.OLLAMA;
    }

    @Override
    public EmbeddingModel createEmbeddingModel(SystemModelDTO modelDTO) {
        String defaultUrl = modelProviderService.getProviderDefaultUrl(getProviderType());
        String baseUrl = StringUtils.defaultIfBlank(modelDTO.getRequestUrl(), defaultUrl);
        String modelName = modelDTO.getName();

        // 构建OllamaApi
        OllamaApi ollamaApi = OllamaApi.builder()
                .baseUrl(baseUrl)
                .build();

        // 构建OllamaOptions
        OllamaOptions options = OllamaOptions.builder()
                .model(modelName)
                .temperature(modelDTO.getMaxTemperature())
                .build();

        // 使用默认的工具调用管理器和观察注册表
        ToolCallingManager toolCallingManager = ToolCallingManager.builder().build();
        ObservationRegistry observationRegistry = ObservationRegistry.NOOP;
        ModelManagementOptions modelManagementOptions = ModelManagementOptions.defaults();

        return new OllamaEmbeddingModel(
                ollamaApi,
                options,
                observationRegistry,
                modelManagementOptions
        );
    }
} 