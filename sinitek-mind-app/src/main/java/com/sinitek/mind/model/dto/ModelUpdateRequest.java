package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * 模型更新请求 DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Data
@Schema(description = "模型更新请求")
public class ModelUpdateRequest {

    @Schema(description = "模型标识", required = true)
    private String model;

    @Schema(description = "模型元数据")
    private Map<String, Object> metadata;
} 