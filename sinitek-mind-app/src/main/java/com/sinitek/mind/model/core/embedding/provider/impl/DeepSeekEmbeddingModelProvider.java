package com.sinitek.mind.model.core.embedding.provider.impl;

import com.sinitek.mind.model.constant.ModelProviderConstant;
import com.sinitek.mind.model.service.IModelProviderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * DeepSeek嵌入模型提供商
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class DeepSeekEmbeddingModelProvider extends OpenAIEmbeddingModelProvider {

    @Autowired
    private IModelProviderService modelProviderService;

    @Override
    public String getProviderType() {
        return ModelProviderConstant.DEEPSEEK;
    }
} 