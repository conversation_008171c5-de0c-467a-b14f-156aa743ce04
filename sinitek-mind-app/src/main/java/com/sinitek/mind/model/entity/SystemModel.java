package com.sinitek.mind.model.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * 系统模型实体
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：用于存储系统模型的MongoDB实体类
 */
@Data
@Document(collection = "system_models")
public class SystemModel {

    @Id
    @Schema(description = "主键ID")
    private String id;

    @Indexed(unique = true)
    @Schema(description = "模型标识")
    private String model;

    @Schema(description = "模型元数据")
    private Map<String, Object> metadata;

} 