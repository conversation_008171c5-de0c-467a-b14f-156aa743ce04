package com.sinitek.mind.model.core.embedding.provider;

/**
 * 嵌入模型提供商接口
 *
 * <AUTHOR>
 * date 2025-07-08
 */
import com.sinitek.mind.model.dto.SystemModelDTO;
import org.springframework.ai.embedding.EmbeddingModel;

public interface EmbeddingModelProvider {

    /**
     * 获取支持的提供商类型
     * @return 提供商类型
     */
    String getProviderType();

    /**
     * 是否支持该提供商
     * @param providerType 提供商类型
     * @return 是否支持
     */
    default boolean supports(String providerType) {
        return getProviderType().equalsIgnoreCase(providerType);
    }

    /**
     * 创建嵌入模型
     *
     * @param modelDTO 模型信息
     * @return 嵌入模型实例
     */
    EmbeddingModel createEmbeddingModel(SystemModelDTO modelDTO);
} 