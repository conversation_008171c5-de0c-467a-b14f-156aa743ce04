package com.sinitek.mind.model.core.tts.service;

import java.io.OutputStream;
import java.util.concurrent.CompletableFuture;

/**
 * 系统文本转语音服务接口
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public interface ITextToSpeechService {

    /**
     * 将文本转换为语音
     * @param text 待转换文本
     * @param model 模型标识
     * @param voiceId 语音ID
     * @return 音频数据的字节数组
     */
    byte[] textToSpeech(String text, String model, String voiceId);

    /**
     * 将文本转换为语音并写入输出流
     * @param text 待转换文本
     * @param model 模型标识
     * @param voiceId 语音ID
     * @param outputStream 输出流
     */
    void textToSpeechToStream(String text, String model, String voiceId, OutputStream outputStream);

    /**
     * 异步将文本转换为语音
     * @param text 待转换文本
     * @param model 模型标识
     * @param voiceId 语音ID
     * @return 异步响应结果
     */
    CompletableFuture<byte[]> textToSpeechAsync(String text, String model, String voiceId);
}