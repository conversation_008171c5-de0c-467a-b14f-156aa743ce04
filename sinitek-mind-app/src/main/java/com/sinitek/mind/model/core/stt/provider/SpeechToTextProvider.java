package com.sinitek.mind.model.core.stt.provider;

import com.sinitek.mind.model.dto.SystemModelDTO;

import java.io.InputStream;

/**
 * 语音转文本模型提供商接口
 *
 * <AUTHOR>
 * date 2025-07-08
 */
public interface SpeechToTextProvider {

    /**
     * 获取支持的提供商类型
     * @return 提供商类型
     */
    String getProviderType();

    /**
     * 将语音数据转换为文本
     * @param audioData 语音数据字节数组
     * @param modelDTO 模型信息
     * @return 识别的文本
     */
    String recognizeSpeech(byte[] audioData, SystemModelDTO modelDTO);

    /**
     * 将语音流转换为文本
     * @param audioStream 语音数据流
     * @param modelDTO 模型信息
     * @return 识别的文本
     */
    String recognizeSpeech(InputStream audioStream, SystemModelDTO modelDTO);

    /**
     * 是否支持该提供商
     * @param providerType 提供商类型
     * @return 是否支持
     */
    default boolean supports(String providerType) {
        return getProviderType().equalsIgnoreCase(providerType);
    }
}