package com.sinitek.mind.model.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 模型语音DTO
 *
 * <AUTHOR>
 * date 2025-07-07
 */
@Data
@Schema(description = "模型语音项")
public class ModelVoiceDTO {

    @Schema(description = "语音ID")
    private String id;

    @Schema(description = "语音名称")
    private String name;

    @Schema(description = "语音类型")
    private String type;

    @Schema(description = "语音语言")
    private String language;

    @Schema(description = "性别")
    private String gender;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "预览音频URL")
    private String previewUrl;

    @Schema(description = "是否默认")
    private Boolean isDefault;
} 