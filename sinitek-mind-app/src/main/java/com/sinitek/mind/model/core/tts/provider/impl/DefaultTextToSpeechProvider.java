package com.sinitek.mind.model.core.tts.provider.impl;

import com.sinitek.mind.model.core.tts.provider.TextToSpeechProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.io.OutputStream;
import java.util.Map;

/**
 * 默认TTS服务提供者实现
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class DefaultTextToSpeechProvider implements TextToSpeechProvider {

    private static final Logger logger = LoggerFactory.getLogger(DefaultTextToSpeechProvider.class);
    private static final String PROVIDER_TYPE = "default";

    @Override
    public String getProviderType() {
        return PROVIDER_TYPE;
    }

    @Override
    public byte[] generateSpeech(String text, SystemModelDTO modelDTO, String voiceId) {
        logger.info("调用默认TTS服务生成语音, 文本长度: {}, 模型: {}, 语音ID: {}", 
                text.length(), modelDTO.getModel(), voiceId);
                
        // 实际实现中，这里会调用第三方API或本地服务
        try {
            // 获取模型自定义配置
            Map<String, Object> config = modelDTO.getDefaultConfig();
            String apiKey = getConfigValue(config, "apiKey", "");
            String apiUrl = StringUtils.isNotBlank(modelDTO.getRequestUrl()) 
                    ? modelDTO.getRequestUrl() 
                    : getConfigValue(config, "apiUrl", "");
                    
            // 实际调用TTS服务的代码
            // 这里只是示例，实际需要根据不同供应商实现对接
            
            // 模拟返回一个空的音频数据
            return new byte[0];
        } catch (Exception e) {
            logger.error("TTS服务调用失败", e);
            throw new RuntimeException("TTS服务调用失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void generateSpeechToStream(String text, SystemModelDTO modelDTO, String voiceId, OutputStream outputStream) {
        try {
            byte[] data = generateSpeech(text, modelDTO, voiceId);
            outputStream.write(data);
            outputStream.flush();
        } catch (IOException e) {
            logger.error("写入TTS数据到输出流失败", e);
            throw new RuntimeException("写入TTS数据到输出流失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 从配置中获取值
     * @param config 配置
     * @param key 键
     * @param defaultValue 默认值
     * @return 配置值
     */
    private String getConfigValue(Map<String, Object> config, String key, String defaultValue) {
        if (config == null) {
            return defaultValue;
        }
        Object value = config.get(key);
        return value != null ? value.toString() : defaultValue;
    }
} 