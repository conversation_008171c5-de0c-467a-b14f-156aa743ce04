package com.sinitek.mind.model.core.tts;

import com.sinitek.mind.model.core.tts.provider.TextToSpeechProvider;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.service.ISystemModelService;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * TTS模型工厂层
 *
 * <AUTHOR>
 * date 2025-07-08
 */
@Component
public class TTSModelFactory {

    @Autowired
    private ISystemModelService systemModelService;
    
    @Autowired
    private List<TextToSpeechProvider> ttsProviders;

    /**
     * 根据模型ID创建TTS服务提供者
     * @param modelId 模型ID
     * @param voiceId 语音ID
     * @return TTS服务提供者实例
     */
    public TextToSpeechProvider getProvider(String modelId, String voiceId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        
        if (ObjectUtils.isEmpty(modelDTO)) {
            throw new IllegalArgumentException("模型不存在: " + modelId);
        }

        String provider = modelDTO.getProvider();
        
        if (StringUtils.isBlank(provider)) {
            throw new IllegalArgumentException("模型提供商不能为空");
        }
        
        // 查找对应的提供商实现
        for (TextToSpeechProvider ttsProvider : ttsProviders) {
            if (ttsProvider.supports(provider)) {
                return ttsProvider;
            }
        }
        
        throw new IllegalArgumentException("不支持的模型提供商: " + provider);
    }
    
    /**
     * 根据模型ID生成语音数据
     * @param text 要转换的文本
     * @param modelId 模型ID
     * @param voiceId 语音ID
     * @return 语音数据字节数组
     */
    public byte[] generateSpeech(String text, String modelId, String voiceId) {
        SystemModelDTO modelDTO = systemModelService.getModelDetail(modelId);
        TextToSpeechProvider provider = getProvider(modelId, voiceId);
        return provider.generateSpeech(text, modelDTO, voiceId);
    }
} 