package com.sinitek.mind.support.team.service;

import com.sinitek.mind.support.team.dto.*;

import java.util.List;

/**
 * 群组服务接口
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public interface IGroupService {
    
    /**
     * 获取群组列表
     *
     * @param request 请求参数
     * @return 群组列表响应
     */
    List<GroupDTO> getGroupList(GroupListRequest request);
    
    /**
     * 创建群组
     *
     * @param request 创建请求
     */
    void createGroup(CreateGroupRequest request);
    
    /**
     * 删除群组
     *
     * @param request 删除请求
     */
    void deleteGroup(DeleteGroupRequest request);
    
    /**
     * 更新群组信息
     *
     * @param request 更新请求
     */
    void updateGroup(UpdateGroupRequest request);
    
    /**
     * 获取群组成员
     *
     * @param groupId 群组id
     * @return 群组成员响应
     */
    List<GroupMemberDTO> findGroupMembers(String groupId);
    
    /**
     * 转让群组所有权
     *
     * @param request 转让群组所有权请求
     */
    void changeGroupOwner(ChangeGroupOwnerRequest request);
} 