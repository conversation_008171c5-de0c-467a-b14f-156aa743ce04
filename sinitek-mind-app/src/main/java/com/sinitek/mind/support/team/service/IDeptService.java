package com.sinitek.mind.support.team.service;

import com.sinitek.mind.support.team.dto.*;

import java.util.List;

/**
 * 组织服务接口
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public interface IDeptService {
    
    /**
     * 获取组织列表
     *
     * @param request 请求参数
     * @return 组织列表响应
     */
    List<DeptDTO> getOrgList(DeptListRequest request);
    
    /**
     * 创建组织
     *
     * @param request 创建请求
     */
    void createOrg(CreateDeptRequest request);
    
    /**
     * 删除组织
     *
     * @param request 删除请求
     */
    void deleteOrg(DeleteDeptRequest request);
    
    /**
     * 移动组织
     *
     * @param request 移动请求
     */
    void moveOrg(MoveDeptRequest request);
    
    /**
     * 更新组织信息
     *
     * @param request 更新请求
     */
    void updateOrg(UpdateDeptRequest request);
    
    /**
     * 更新组织成员
     *
     * @param request 更新组织成员请求
     */
    void updateOrgMembers(DeptOrgMembersRequest request);
    
    /**
     * 获取组织成员
     *
     * @param request 获取组织成员请求
     * @return 组织成员响应
     */
    DeptMembersResponse getOrgMembers(GetDeptMembersRequest request);
    
    /**
     * 删除组织成员
     *
     * @param request 删除组织成员请求
     */
    void deleteOrgMember(DeleteDeptMemberRequest request);

    /**
     * 使用团队成员id，构建成员所在部门的名称
     * @param tmbId 成员id
     * @return 所有直属部门的名称
     */
    List<String> getDeptNameByTmbId(String tmbId);
} 