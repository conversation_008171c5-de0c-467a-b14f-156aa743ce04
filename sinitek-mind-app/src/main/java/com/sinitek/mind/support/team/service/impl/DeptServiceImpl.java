package com.sinitek.mind.support.team.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.sinitek.data.model.tree.enumerate.TypeEnum;
import com.sinitek.mind.support.team.dto.*;
import com.sinitek.mind.support.team.service.IDeptService;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.dto.OrgTreeDTO;
import com.sinitek.sirm.org.entity.Department;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.entity.Position;
import com.sinitek.sirm.org.service.IOrgTreeService;
import com.sinitek.sirm.org.service.IOrgUpdaterService;
import com.sinitek.spirit.org.core.IOrgFinder;
import com.sinitek.spirit.org.core.dto.OrgSpiritObjectDTO;
import com.sinitek.spirit.org.core.dto.UnitDTO;
import com.sinitek.spirit.org.core.enumerate.UnitTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 组织服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DeptServiceImpl implements IDeptService {

    /**
     * 新增部门时，会默认新增一个岗位用于承载成员，如果缺少岗位就会报错
     */

    @Autowired
    private com.sinitek.sirm.org.service.IOrgService appOrgService;

    @Autowired
    private IOrgFinder orgFinder;

    @Autowired
    private IOrgUpdaterService orgUpdaterService;
    
    @Autowired
    private IOrgTreeService orgTreeService;

    // 缓存，通过orgId获取path
    private final Map<String, String> pathCache = new HashMap<>(100);

    @Override
    public List<DeptDTO> getOrgList(DeptListRequest request) {
        String orgId = request.getOrgId();
        if (StringUtils.isBlank(orgId)) {
            orgId = orgFinder.getRoot().getId();
        }

        OrgSpiritObjectDTO orgObject = orgFinder.getOrgObject(orgId);

        OrgTreeDTO orgTreeDTO = orgTreeService.loadOrgTree(orgObject, false, false,
            false);

        List<OrgTreeDTO> children = orgTreeDTO.getChildren();
        if (CollUtil.isEmpty(children)) {
            return List.of();
        }

        // 只获取一层即可
        List<DeptDTO> result = new LinkedList<>();
        for (OrgTreeDTO treeDTO : children) {
            result.add(convertOrgTreeDTO2DeptDTO(treeDTO));
        }
        return result;
    }

    /**
     * 组织树中构建部门dto
     * @param orgTreeDTO
     * @return
     */
    private DeptDTO convertOrgTreeDTO2DeptDTO(OrgTreeDTO orgTreeDTO) {
        DeptDTO deptDTO = new DeptDTO();
        if (orgTreeDTO == null) {
            return deptDTO;
        }

        UnitDTO unitDTO = orgFinder.getUnitById(orgTreeDTO.getId());

        deptDTO.set_id(unitDTO.getId());
        deptDTO.setTeamId(unitDTO.getTenantId());
        deptDTO.setName(unitDTO.getName());
        deptDTO.setDescription(unitDTO.getDescription());

        // 子部门和子成员的数量总和
        List<UnitDTO> childrenOrg = orgFinder.findAllUnits(unitDTO.getId(),
            List.of(UnitTypeEnum.UNIT.toString()), false,
            null, null);
        List<Employee> childrenEmployee = findEmployeeByDeptId(unitDTO.getId());
        deptDTO.setTotal(childrenOrg.size() + childrenEmployee.size());

        String parentPath = MapUtils.getString(pathCache, orgTreeDTO.getParentId(), "");

        deptDTO.setPath(parentPath + "/" + orgTreeDTO.getId());
        deptDTO.setPathId(orgTreeDTO.getId());

        // 加入结果中，并构建缓存
        pathCache.put(orgTreeDTO.getId(), deptDTO.getPath());

        return deptDTO;
    }

    @Override
    public void createOrg(CreateDeptRequest request) {
        Department department = new Department();
        department.setName(request.getName());
        department.setDescription(request.getDescription());
        if (StringUtils.isBlank(request.getOrgId())) {
            UnitDTO root = orgFinder.getRoot();
            department.setParentId(root.getId());
        } else {
            department.setParentId(request.getOrgId());
        }

        Department saved = orgUpdaterService.saveDepartment(department);

        // 创建部门后，默认创建一个岗位，用于设置员工
        Position position = new Position();
        position.setName(request.getName() + "岗位");
        position.setDescription(request.getName() + "部门的默认岗位");
        position.setParentId(saved.getOrgid());

        orgUpdaterService.savePosition(position);

    }
    
    @Override
    public void deleteOrg(DeleteDeptRequest request) {
        orgUpdaterService.deleteUnitsByIds(request.getOrgId());
    }
    
    @Override
    public void moveOrg(MoveDeptRequest request) {
        String orgId = request.getOrgId();
        String tarId = request.getTargetOrgId();
        // 移动到目标部门下方
        orgUpdaterService.moveUnit(orgId, tarId, TypeEnum.SUB);
    }
    
    @Override
    public void updateOrg(UpdateDeptRequest request) {
        UnitDTO unit = new UnitDTO();
        unit.setId(request.getOrgId());
        unit.setName(request.getName());
        unit.setDescription(request.getDescription());
        orgUpdaterService.renameUnit(null, unit);
    }
    
    @Override
    public void updateOrgMembers(DeptOrgMembersRequest request) {
        String orgId = request.getOrgId();
        List<DeptMemberDTO> members = request.getMembers();
        // 先移除该部门的所有成员
        List<Employee> employeeList = findEmployeeByDeptId(orgId);
        orgUpdaterService.removeUnitEmoloyee(orgId, employeeList.stream()
            .map(Employee::getId)
            .collect(Collectors.toList()));

        // 获取该部门的岗位
        List<Position> positionList = appOrgService.findPositionsByUnitId(orgId);

        if (positionList == null || CollUtil.isEmpty(positionList)) {
            throw new BussinessException("该部门没有岗位，请新建一个默认岗位，用于承载成员");
        }

        // 在该岗位添加所有成员
        orgUpdaterService.addOrgEmployee(positionList.get(0).getOrgid(), members.stream()
            .map(DeptMemberDTO::getTmbId)
            .collect(Collectors.toList()));
    }
    
    @Override
    public DeptMembersResponse getOrgMembers(GetDeptMembersRequest request) {
        // 未找到具体按钮
        return null;
    }
    
    @Override
    public void deleteOrgMember(DeleteDeptMemberRequest request) {
        // 将成员移出部门
        String id = request.getOrgId();
        String empId = request.getTmbId();

        if (StringUtils.isNotBlank(id) && StringUtils.isNotBlank(empId)) {

            orgUpdaterService.removeUnitEmoloyee(id, List.of(empId));
        }
    }

    @Override
    public List<String> getDeptNameByTmbId(String tmbId) {
        if (StringUtils.isBlank(tmbId)) {
            return List.of();
        }
        List<Department> allDeptList = appOrgService.findUnitsByEmpId(tmbId);
        if (CollUtil.isEmpty(allDeptList)) {
            return List.of();
        }

        return allDeptList.stream()
            .map(Department::getName)
            .toList();
    }

    /**
     * 获取当前部门下的员工
     */
    private List<Employee> findEmployeeByDeptId(String orgId) {
        List<Position> positionList = appOrgService.findPositionsByUnitId(orgId);
        if (CollUtil.isEmpty(positionList)) {
            // 无岗位，则无员工
            return new LinkedList<>();
        }
        return appOrgService.findEmployeeByPosId(positionList.get(0).getOrgid());
    }

} 