package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组织DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptDTO {
    
    @Schema(description = "组织ID")
    private String _id;
    
    @Schema(description = "团队ID")
    private String teamId;
    
    @Schema(description = "路径ID")
    private String pathId;
    
    @Schema(description = "完整路径")
    private String path;
    
    @Schema(description = "组织名称")
    private String name;
    
    @Schema(description = "组织头像")
    private String avatar;
    
    @Schema(description = "组织描述")
    private String description;
    
    @Schema(description = "权限对象")
    private Object permission;
    
    @Schema(description = "成员数 + 子组织数")
    private Integer total;
} 