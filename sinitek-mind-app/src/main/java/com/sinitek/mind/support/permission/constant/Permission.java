package com.sinitek.mind.support.permission.constant;

import com.sinitek.mind.support.permission.dto.PermissionItemDTO;
import com.sinitek.mind.support.permission.enumerate.PermissionKeyEnum;
import com.sinitek.mind.support.permission.model.PermissionConstructorProps;
import lombok.Data;
import lombok.Getter;

import java.util.Map;

/**
 * 权限核心类
 * 对应TypeScript中的Permission类
 * 使用位运算实现权限管理，灵感来自Linux权限系统
 */
@Data
public class Permission {
    // Getters
    @Getter
    protected Long value;
    @Getter
    protected boolean isOwner = false;
    protected boolean hasManagePer = false;
    protected boolean hasWritePer = false;
    protected boolean hasReadPer = false;
    @Getter
    protected Map<String, PermissionItemDTO> permissionList;

    private Runnable updatePermissionCallback;

    /**
     * 默认构造函数
     */
    public Permission() {
        this(null);
    }

    /**
     * 构造函数
     * @param props 构造参数
     */
    public Permission(PermissionConstructorProps props) {
        Long per = PermissionConstant.NULL_PERMISSION;
        boolean isOwner = false;
        Map<String, PermissionItemDTO> permissionList = PermissionConstant.PERMISSION_LIST;

        if (props != null) {
            per = props.getPermission() != null ? props.getPermission() : PermissionConstant.NULL_PERMISSION;
            isOwner = props.getIsOwner() != null ? props.getIsOwner() : false;
            permissionList = props.getPermissionList() != null ? props.getPermissionList() : PermissionConstant.PERMISSION_LIST;
        }

        if (isOwner) {
            this.value = PermissionConstant.OWNER_PERMISSION_VAL;
        } else {
            this.value = per;
        }

        this.permissionList = permissionList;
        updatePermissions();
    }

    /**
     * 添加权限
     * 支持链式调用
     * @param perList 权限值列表
     * @return 当前权限对象
     */
    public Permission addPer(int... perList) {
        if (this.isOwner) {
            return this;
        }

        for (int per : perList) {
            this.value = this.value | per;
        }

        updatePermissions();
        return this;
    }

    /**
     * 移除权限
     * @param perList 权限值列表
     * @return 当前权限对象
     */
    public Permission removePer(int... perList) {
        if (this.isOwner) {
            return this;
        }

        for (int per : perList) {
            this.value = this.value & ~per;
        }

        updatePermissions();
        return this;
    }

    /**
     * 检查是否具有指定权限
     * @param perm 权限值
     * @return 是否具有权限
     */
    public boolean checkPer(long perm) {
        // 如果是所有者权限，只有所有者才有此权限
        if (perm == PermissionConstant.OWNER_PERMISSION_VAL) {
            return this.value == PermissionConstant.OWNER_PERMISSION_VAL;
        }
        return (this.value & perm) == perm;
    }

    /**
     * 设置权限更新回调
     * @param callback 回调函数
     */
    public void setUpdatePermissionCallback(Runnable callback) {
        if (callback != null) {
            callback.run();
        }
        this.updatePermissionCallback = callback;
    }

    /**
     * 更新权限状态
     */
    private void updatePermissions() {
        this.isOwner = this.value == PermissionConstant.OWNER_PERMISSION_VAL;

        PermissionItemDTO manageItem = this.permissionList.get(PermissionKeyEnum.MANAGE.getValue());
        PermissionItemDTO writeItem = this.permissionList.get(PermissionKeyEnum.WRITE.getValue());
        PermissionItemDTO readItem = this.permissionList.get(PermissionKeyEnum.READ.getValue());

        this.hasManagePer = manageItem != null && checkPer(manageItem.getValue());
        this.hasWritePer = writeItem != null && checkPer(writeItem.getValue());
        this.hasReadPer = readItem != null && checkPer(readItem.getValue());

        if (this.updatePermissionCallback != null) {
            this.updatePermissionCallback.run();
        }
    }

    /**
     * 转换为二进制字符串
     * @return 二进制字符串
     */
    public String toBinary() {
        return Long.toBinaryString(this.value);
    }

    public boolean hasManagePer() {
        return hasManagePer;
    }

    public boolean hasWritePer() {
        return hasWritePer;
    }

    public boolean hasReadPer() {
        return hasReadPer;
    }

    @Override
    public String toString() {
        return "Permission{" +
                "value=" + value +
                ", isOwner=" + isOwner +
                ", hasManagePer=" + hasManagePer +
                ", hasWritePer=" + hasWritePer +
                ", hasReadPer=" + hasReadPer +
                ", binary='" + toBinary() + '\'' +
                '}';
    }
}
