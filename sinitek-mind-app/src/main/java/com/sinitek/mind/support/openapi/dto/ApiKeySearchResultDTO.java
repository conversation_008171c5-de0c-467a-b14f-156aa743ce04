package com.sinitek.mind.support.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * API密钥搜索结果响应DTO
 * 与FastGPT的返回格式保持一致
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiKeySearchResultDTO {
    
    /**
     * API密钥（脱敏显示）
     */
    private String apiKey;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * API密钥ID
     */
    private String id;

    /**
     * API密钥ID
     */
    private String _id;
    
    /**
     * 使用限制信息
     */
    private LimitInfo limit;
    
    /**
     * 密钥名称
     */
    private String name;
    
    /**
     * 团队ID
     */
    private String teamId;
    
    /**
     * 用户ID
     */
    private String tmbId;
    
    /**
     * 已使用点数
     */
    private Long usagePoints;
    
    /**
     * 限制信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class LimitInfo {
        /**
         * 最大使用点数，-1表示无限制
         */
        private Long maxUsagePoints;
    }
} 