package com.sinitek.mind.support.team.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TeamPlanStatusDTO {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class StandardSub {
        private String _id;
        private String teamId;
        private String type;
        private Date startTime;
        private Date expiredTime;
        private String currentMode;
        private String nextMode;
        private String currentSubLevel;
        private String nextSubLevel;
        private Integer totalPoints;
        private Integer surplusPoints;
        private Integer version;
    }

    private StandardSub standard;

    private Integer totalPoints;
    private Integer usedPoints;
    private Integer datasetMaxSize;

    private Integer usedMember;
    private Integer usedAppAmount;
    private Integer usedDatasetSize;
    private Integer usedDatasetIndexSize;
}