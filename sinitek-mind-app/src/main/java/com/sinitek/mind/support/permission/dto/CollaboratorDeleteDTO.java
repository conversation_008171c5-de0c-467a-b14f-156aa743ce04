package com.sinitek.mind.support.permission.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 协作者信息更新DTO
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "协作者删除基础对象")
public class CollaboratorDeleteDTO {

    @Schema(description = "团队成员ID（groupId、orgId）")
    private String tmbId;

    @Schema(description = "群组ID（tmbId、orgId）")
    private String groupId;

    @Schema(description = "组织ID（tmbId、groupId）")
    private String orgId;
}