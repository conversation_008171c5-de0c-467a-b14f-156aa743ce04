package com.sinitek.mind.support.permission.constant;

import com.sinitek.mind.support.permission.model.PermissionConstructorProps;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 应用权限类
 * 对应TypeScript中的AppPermission类
 * 继承基础权限类，使用应用特定的权限配置
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppPermission extends Permission {

    /**
     * 默认构造函数
     */
    public AppPermission() {
        this(null);
    }

    /**
     * 构造函数
     * @param props 构造参数
     */
    public AppPermission(PermissionConstructorProps props) {
        // 调用父类构造函数，必须是第一行
        super(buildAppProps(props));
    }

    /**
     * 构建应用权限参数
     * @param props 原始参数
     * @return 构建后的参数
     */
    private static PermissionConstructorProps buildAppProps(PermissionConstructorProps props) {
        // 设置默认值和权限列表
        PermissionConstructorProps appProps = props;
        if (appProps == null) {
            appProps = PermissionConstructorProps.builder()
                    .permission(PermissionConstant.APP_DEFAULT_PERMISSION_VAL)
                    .permissionList(PermissionConstant.APP_PERMISSION_LIST)
                    .build();
        } else {
            if (appProps.getPermission() == null) {
                appProps.setPermission(PermissionConstant.APP_DEFAULT_PERMISSION_VAL);
            }
            // 确保使用应用权限列表
            appProps.setPermissionList(PermissionConstant.APP_PERMISSION_LIST);
        }
        return appProps;
    }

    /**
     * 检查是否有应用读权限
     * @return 是否有读权限
     */
    public boolean hasAppReadPermission() {
        return hasReadPer();
    }

    /**
     * 检查是否有应用写权限
     * @return 是否有写权限
     */
    public boolean hasAppWritePermission() {
        return hasWritePer();
    }

    /**
     * 检查是否有应用管理权限
     * @return 是否有管理权限
     */
    public boolean hasAppManagePermission() {
        return hasManagePer();
    }

    @Override
    public String toString() {
        return "AppPermission{" +
                "value=" + getValue() +
                ", isOwner=" + isOwner() +
                ", hasManagePer=" + hasManagePer() +
                ", hasWritePer=" + hasWritePer() +
                ", hasReadPer=" + hasReadPer() +
                ", binary='" + toBinary() + '\'' +
                '}';
    }
}
