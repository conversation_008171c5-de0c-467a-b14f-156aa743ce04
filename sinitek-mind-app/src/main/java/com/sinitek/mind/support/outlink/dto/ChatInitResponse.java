package com.sinitek.mind.support.outlink.dto;

import com.sinitek.mind.core.app.entity.App;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

@Data
@Schema(description = "聊天初始化响应")
public class ChatInitResponse {

    @Schema(description = "聊天ID")
    private String chatId;

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "聊天标题")
    private String title;

    @Schema(description = "用户头像URL")
    private String userAvatar;

    @Schema(description = "聊天变量")
    private Map<String, Object> variables;

    @Schema(description = "应用配置信息")
    private App app;

    @Schema(description = "是否显示原始引用")
    private Boolean showRawSource;

    @Schema(description = "是否显示详细响应")
    private Boolean responseDetail;

    @Schema(description = "是否显示节点状态")
    private Boolean showNodeStatus;
} 