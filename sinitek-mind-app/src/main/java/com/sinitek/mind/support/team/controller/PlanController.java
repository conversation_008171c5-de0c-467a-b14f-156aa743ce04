package com.sinitek.mind.support.team.controller;

import cn.hutool.core.date.DateUtil;
import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.team.dto.TeamPlanStatusDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/mind/api/support/user/team/plan")
public class PlanController {

    @GetMapping("/getTeamPlanStatus")
    public ApiResponse<TeamPlanStatusDTO> getTeamPlanStatus(int maxQuantity) {
        return ApiResponse.success(TeamPlanStatusDTO.builder()
                .totalPoints(999999999)
                .usedPoints(0)
                .datasetMaxSize(999999999)
                .usedMember(0)
                .usedAppAmount(0)
                .usedDatasetSize(0)
                .usedDatasetIndexSize(0)
                .standard(TeamPlanStatusDTO.StandardSub.builder()
                        ._id("6870bae4a2d41e4ab79493f1")
                        .teamId("685e0b4574813aa1df06dbd7")
                        .type("standard")
                        .startTime(DateUtil.parse("2025-07-23"))
                        .expiredTime(DateUtil.parse("2026-07-23"))
                        .currentMode("year")
                        .nextMode("year")
                        .currentSubLevel("enterprise")
                        .nextSubLevel("enterprise")
                        .totalPoints(999999999)
                        .surplusPoints(999999999)
                        .build())
                .build());
    }
}

