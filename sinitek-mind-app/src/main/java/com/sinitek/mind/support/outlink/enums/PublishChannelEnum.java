package com.sinitek.mind.support.outlink.enums;

/**
 * 发布渠道类型枚举
 */
public enum PublishChannelEnum {
    
    SHARE("share", "分享链接"),
    IFRAME("iframe", "嵌入式页面"),
    APIKEY("apikey", "API Key"),
    FEISHU("feishu", "飞书机器人"),
    DINGTALK("dingtalk", "钉钉机器人"),
    WECOM("wecom", "企业微信"),
    OFFICIAL_ACCOUNT("official_account", "微信公众号");
    
    private final String code;
    private final String description;
    
    PublishChannelEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    public static PublishChannelEnum fromCode(String code) {
        for (PublishChannelEnum channel : values()) {
            if (channel.code.equals(code)) {
                return channel;
            }
        }
        throw new IllegalArgumentException("未知的发布渠道类型: " + code);
    }
} 