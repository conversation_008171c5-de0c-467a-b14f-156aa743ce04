package com.sinitek.mind.support.wallet.util;

import com.sinitek.mind.core.workflow.model.ChatNodeUsageType;
import com.sinitek.mind.model.dto.SystemModelDTO;
import com.sinitek.mind.model.support.ModelConfigManager;
import com.sinitek.mind.support.wallet.enumerate.UsageSourceEnum;
import com.sinitek.mind.support.wallet.model.ModelPointsRequest;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;

public class WalletUtil {

    // ￥1 = 100000.
    private static final int PRICE_SCALE = 100000;

    /**
     * 将存储价格格式化为可读价格
     * @param val 存储的价格值，默认为 0
     * @param multiple 倍数，默认为 1
     * @return 格式化后的可读价格
     */
    public static double formatStorePrice2Read(double val, double multiple) {
        BigDecimal result = BigDecimal.valueOf(val)
                .divide(BigDecimal.valueOf(PRICE_SCALE), 10, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(multiple));
        return result.doubleValue();
    }

    /**
     * 根据认证类型获取使用来源
     * @param shareId 分享 ID，可为空
     * @param authType 认证类型，可为空
     * @return 使用来源枚举值
     */
    public static UsageSourceEnum getUsageSourceByAuthType(String shareId, String authType) {
        if (shareId != null && !shareId.isEmpty()) {
            return UsageSourceEnum.shareLink;
        }
        if (StringUtils.equals(authType, "apikey")) {
            return UsageSourceEnum.api;
        }
        return UsageSourceEnum.fastgpt;
    }

    /**
     * 计算模型字符/Token转积分
     * 对应TypeScript中的formatModelChars2Points函数
     *
     * @param request 计算请求参数
     * @return 计算结果
     */
    public static ChatNodeUsageType formatModelChars2Points(ModelPointsRequest request) {
        try {
            int inputTokens = request.getInputTokens();
            int multiple = request.getMultiple();
            int outputTokens = request.getOutputTokens();
            // 查找AI模型数据
            List<SystemModelDTO> systemModelList = ModelConfigManager.getSystemModelList();
            SystemModelDTO modelData = systemModelList.stream().filter(item -> request.getModel().equals(item.getModel()))
                    .findFirst().orElse(null);
            if (modelData == null) {
                return ChatNodeUsageType.builder()
                        .totalPoints(0.0)
                        .moduleName("")
                        .build();
            }

            // 判断是否为输入输出分别计费类型
            boolean isIOPriceType = modelData.getInputPrice() > 0;

            // 计算总积分
            double totalPoints = isIOPriceType ? modelData.getInputPrice() * ((double) inputTokens / multiple)
                    + modelData.getOutputPrice() * ((double) outputTokens / multiple) :
                    modelData.getCharsPointsPrice() * ((double)(inputTokens + outputTokens) / multiple);

            return ChatNodeUsageType.builder()
                    .moduleName(modelData.getName())
                    .totalPoints(totalPoints)
                    .build();

        } catch (Exception e) {
            return ChatNodeUsageType.builder()
                    .totalPoints(0.0)
                    .moduleName("")
                    .build();
        }
    }

}
