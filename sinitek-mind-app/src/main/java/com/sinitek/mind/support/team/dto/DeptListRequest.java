package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 组织列表请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptListRequest {
    
    @Schema(description = "父组织ID")
    private String orgId;
    
    @Schema(description = "是否包含权限信息")
    private Boolean withPermission;
    
    @Schema(description = "搜索关键词")
    private String searchKey;
} 