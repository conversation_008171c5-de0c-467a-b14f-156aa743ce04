package com.sinitek.mind.support.outlink.service.impl;

import com.sinitek.mind.support.outlink.dto.AuthHookRequestDTO;
import com.sinitek.mind.support.outlink.dto.AuthHookResponseDTO;
import com.sinitek.mind.support.outlink.dto.OutLinkAuthResultDTO;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.mind.support.outlink.repository.OutLinkRepository;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
@Slf4j
@Service
public class AuthOutLinkServiceImpl implements IAuthOutLinkService {

    @Autowired
    private OutLinkRepository outLinkRepository;

    @Autowired
    private RestTemplate restTemplate;

    private static final String HOOK_PATH_INIT = "/shareAuth/init";
    private static final String HOOK_PATH_START = "/shareAuth/start";
    private static final String HOOK_PATH_FINISH = "/shareAuth/finish";

    /**
     * 通用的Hook接口调用方法
     *
     * @param hookUrl Hook基础URL
     * @param path 接口路径
     * @param requestDTO 请求参数
     * @param responseClass 响应类型
     * @param <T> 响应类型泛型
     * @return HTTP响应
     */
    private <T> ResponseEntity<T> callHookApi(String hookUrl, String path, AuthHookRequestDTO requestDTO, Class<T> responseClass) {
        String fullUrl = hookUrl + path;
        
        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<AuthHookRequestDTO> requestEntity = new HttpEntity<>(requestDTO, headers);
        
        // 发送POST请求
        log.info("调用Hook接口: {}", fullUrl);
        return restTemplate.exchange(
            fullUrl, 
            HttpMethod.POST, 
            requestEntity, 
            responseClass
        );
    }

    /**
     * 获取外链配置并验证
     *
     * @param shareId 外链ID
     * @return OutLink对象
     */
    private OutLink getAndValidateOutLink(String shareId) {
        Optional<OutLink> outLinkOpt = outLinkRepository.findByShareId(shareId);
        if (outLinkOpt.isEmpty()) {
            throw new BussinessException("分享链接不存在或已过期");
        }
        return outLinkOpt.get();
    }

    /**
     * 进行外链聊天初始化验证，调用身份验证接口，获取实际uid
     *
     * @param shareId 外链id
     * @param outLinkUid 外链用户id
     * @return 外链验证后的用户id-验证接口返回的uid
     */
    @Override
    public String authOutLinkChatInit(String shareId, String outLinkUid) {
        log.info("开始外链聊天初始化验证，shareId: {}, outLinkUid: {}", shareId, outLinkUid);
        
        OutLink outLink = getAndValidateOutLink(shareId);

        if (outLink.getLimit() == null || StringUtils.isBlank(outLink.getLimit().getHookUrl())) {
            // 没有配置hookUrl，直接返回原outLinkUid
            log.info("未配置hookUrl，直接返回outLinkUid: {}", outLinkUid);
            return outLinkUid;
        }

        String hookUrl = outLink.getLimit().getHookUrl();
        
        try {
            // 准备请求参数
            AuthHookRequestDTO requestDTO = new AuthHookRequestDTO();
            requestDTO.setToken(outLinkUid);
            
            // 调用Hook接口
            ResponseEntity<AuthHookResponseDTO> response = callHookApi(
                hookUrl, 
                HOOK_PATH_INIT, 
                requestDTO, 
                AuthHookResponseDTO.class
            );
            
            AuthHookResponseDTO responseBody = response.getBody();
            if (responseBody == null) {
                throw new BussinessException("认证接口返回数据为空");
            }
            
            if (!responseBody.isSuccess()) {
                String errorMsg = StringUtils.isNotBlank(responseBody.getMessage()) ? 
                    responseBody.getMessage() : responseBody.getMsg();
                throw new BussinessException("认证失败: " + errorMsg);
            }
            
            if (responseBody.getData() == null || StringUtils.isBlank(responseBody.getData().getUid())) {
                throw new BussinessException("认证接口返回的uid为空");
            }
            
            String verifiedUid = responseBody.getData().getUid();
            log.info("外链聊天初始化验证成功，返回uid: {}", verifiedUid);
            return verifiedUid;
            
        } catch (Exception e) {
            log.error("调用初始化验证接口失败", e);
            if (e instanceof BussinessException) {
                throw e;
            }
            throw new BussinessException("调用认证接口失败: " + e.getMessage());
        }
    }

    /**
     * 验证外链聊天开始限制 调用身份验证接口的start接口
     *
     * @param shareId 外链id
     * @param outLinkUid 外链用户id
     * @param question 用户提问
     * @return 验证成功后的用户id
     */
    @Override
    public OutLinkAuthResultDTO authOutLinkChatStart(String shareId, String outLinkUid, String question) {
        log.info("开始外链聊天开始验证，shareId: {}, outLinkUid: {}", shareId, outLinkUid);
        
        OutLink outLink = getAndValidateOutLink(shareId);

        if (outLink.getLimit() == null || StringUtils.isBlank(outLink.getLimit().getHookUrl())) {
            // 没有配置hookUrl，直接返回原outLinkUid
            log.info("未配置hookUrl，直接返回outLinkUid: {}", outLinkUid);
            return convertOutLink2AuthResult(outLink, outLinkUid);
        }

        String hookUrl = outLink.getLimit().getHookUrl();
        
        try {
            // 准备请求参数
            AuthHookRequestDTO requestDTO = new AuthHookRequestDTO();
            requestDTO.setToken(outLinkUid);
            requestDTO.setQuestion(question);
            
            // 调用Hook接口
            ResponseEntity<AuthHookResponseDTO> response = callHookApi(
                hookUrl, 
                HOOK_PATH_START, 
                requestDTO, 
                AuthHookResponseDTO.class
            );
            
            AuthHookResponseDTO responseBody = response.getBody();
            if (responseBody == null) {
                throw new BussinessException("认证接口返回数据为空");
            }
            
            if (!responseBody.isSuccess()) {
                String errorMsg = StringUtils.isNotBlank(responseBody.getMessage()) ? 
                    responseBody.getMessage() : responseBody.getMsg();
                throw new BussinessException("身份验证失败: " + errorMsg);
            }
            
            String verifiedUid = outLinkUid;
            if (responseBody.getData() != null && StringUtils.isNotBlank(responseBody.getData().getUid())) {
                verifiedUid = responseBody.getData().getUid();
            }
            
            log.info("外链聊天开始验证成功，返回uid: {}", verifiedUid);
            return convertOutLink2AuthResult(outLink, outLinkUid);
            
        } catch (Exception e) {
            log.error("调用开始验证接口失败", e);
            if (e instanceof BussinessException) {
                throw e;
            }
            throw new BussinessException("调用认证接口失败: " + e.getMessage());
        }
    }

    /**
     * 外链聊天结束时，需要进行身份验证的结果上报，并无任何验证 为了统一，放在验证服务类下
     *
     * @param shareId 外链id
     * @param outLinkUid 外链用户id
     */
    @Override
    public void authOutLinkChatFinish(String shareId, String outLinkUid, int totalPoints) {
        // TODO 还需要添加聊天结果参数，进行聊天结束上报
        log.info("开始外链聊天结束上报，shareId: {}, outLinkUid: {}", shareId, outLinkUid);
        
        try {
            OutLink outLink = getAndValidateOutLink(shareId);

            if (outLink.getLimit() == null || StringUtils.isBlank(outLink.getLimit().getHookUrl())) {
                // 没有配置hookUrl，直接返回
                log.info("未配置hookUrl，跳过结束上报");
                return;
            }

            String hookUrl = outLink.getLimit().getHookUrl();
            
            // 准备请求参数
            AuthHookRequestDTO requestDTO = new AuthHookRequestDTO();
            requestDTO.setToken(outLinkUid);
            requestDTO.setTotalPoints(totalPoints);

            // 调用Hook接口
            ResponseEntity<String> response = callHookApi(
                hookUrl, 
                HOOK_PATH_FINISH, 
                requestDTO, 
                String.class
            );
            
            log.info("外链聊天结束上报完成，响应状态: {}", response.getStatusCode());
            
        } catch (Exception e) {
            // 结束上报接口失败不影响业务流程，只记录日志
            log.warn("调用结束上报接口失败，继续执行业务流程", e);
        }
    }

    private OutLinkAuthResultDTO convertOutLink2AuthResult(OutLink outLink, String verifiedUid) {
        OutLinkAuthResultDTO result = new OutLinkAuthResultDTO();
        result.setSourceName(outLink.getName());
        result.setTeamId(outLink.getTeamId());
        result.setTmbId(outLink.getTmbId());
        result.setAuthType("outLink");
        result.setResponseDetail(outLink.getResponseDetail());
        result.setShowNodeStatus(outLink.getShowNodeStatus());
        result.setAppId(outLink.getAppId());
        result.setUid(verifiedUid);
        return result;
    }
}
