package com.sinitek.mind.support.permission.enumerate;

import com.sinitek.mind.support.permission.constant.PermissionConstant;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
@Getter
@AllArgsConstructor
public enum AuthTypeEnum {
    READ("READ", PermissionConstant.READ_PER),
    WRITE("WRITE", PermissionConstant.WRITE_PER),
    MANAGE("MANAGE", PermissionConstant.MANAGER_PER),
    TEAM_AUTH_TYPE("ACCESS", -1);

    private final String name;
    private final long value;

}
