package com.sinitek.mind.support.outlink.converter;

import com.sinitek.mind.support.outlink.dto.OutLinkResponse;
import com.sinitek.mind.support.outlink.entity.OutLink;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * OutLink Entity and DTO Converter
 */
@Component
public class OutLinkConverter {

    /**
     * Convert OutLink entity to OutLinkResponse DTO
     *
     * @param outLink entity object
     * @return DTO object
     */
    public static OutLinkResponse toResponse(OutLink outLink) {
        if (outLink == null) {
            return null;
        }

        OutLinkResponse response = new OutLinkResponse();
        response.set_id(outLink.getId());
        response.setShareId(outLink.getShareId());
        response.setTeamId(outLink.getTeamId());
        response.setTmbId(outLink.getTmbId());
        response.setAppId(outLink.getAppId());
        response.setType(outLink.getType());
        response.setName(outLink.getName());
        response.setUsagePoints(outLink.getUsagePoints());
        response.setLastTime(outLink.getLastTime());
        response.setResponseDetail(outLink.getResponseDetail());
        response.setShowNodeStatus(outLink.getShowNodeStatus());
        response.setShowRawSource(outLink.getShowRawSource());
        response.setLimit(outLink.getLimit());
        response.setApp(outLink.getApp());
        response.setImmediateResponse(outLink.getImmediateResponse());
        response.setDefaultResponse(outLink.getDefaultResponse());

        return response;
    }

    /**
     * Convert OutLink entity list to OutLinkResponse DTO list
     *
     * @param outLinks entity list
     * @return DTO list
     */
    public static List<OutLinkResponse> toResponseList(List<OutLink> outLinks) {
        if (outLinks == null) {
            return null;
        }

        return outLinks.stream()
                .map(OutLinkConverter::toResponse)
                .collect(Collectors.toList());
    }
} 