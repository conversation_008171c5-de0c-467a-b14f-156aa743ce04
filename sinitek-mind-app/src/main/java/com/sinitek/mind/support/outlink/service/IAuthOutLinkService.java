package com.sinitek.mind.support.outlink.service;

import com.sinitek.mind.support.outlink.dto.OutLinkAuthResultDTO;

/**
 * <AUTHOR>
 * @date 2025/7/15
 */
public interface IAuthOutLinkService {

    /**
     * 进行外链初始化验证，调用身份验证接口，获取实际uid
     * @param shareId 外链id
     * @param outLinkUid 外链用户id
     * @return 外链验证后的用户id-验证接口返回的uid
     */
    String authOutLinkChatInit(String shareId, String outLinkUid);

    /**
     * 验证外链聊天开始限制
     * 先主动调用身份验证接口，在进行下列的外链验证（没有身份验证则直接跳过）
     * qpm限制、过期时间限制、用量限制
     * @param shareId 外链id
     * @param outLinkUid 外链用户id
     * @param question 对话中用户的提问
     */
    OutLinkAuthResultDTO authOutLinkChatStart(String shareId, String outLinkUid, String question);

    /**
     * 外链聊天结束时，需要进行身份验证的结果上报，并无任何验证
     * 为了统一，放在验证服务类下
     * @param shareId 外链id
     * @param outLinkUid 外链用户id
     * @param totalPoints 消耗的积分
     */
    void authOutLinkChatFinish(String shareId, String outLinkUid, int totalPoints);

}
