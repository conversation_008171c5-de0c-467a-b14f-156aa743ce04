package com.sinitek.mind.support.common.service.impl;

import cn.hutool.core.util.IdUtil;
import com.mongodb.client.gridfs.model.GridFSFile;
import com.sinitek.mind.common.dto.FileTokenQuery;
import com.sinitek.mind.common.util.MindFileTokenUtil;
import com.sinitek.mind.support.common.dto.BaseFileDTO;
import com.sinitek.mind.support.common.dto.FullFileDTO;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.dto.UploadImageRequest;
import com.sinitek.mind.support.common.service.IFileService;
import com.sinitek.mind.support.common.util.Base64Utils;
import com.sinitek.mind.support.common.util.MindFIleUtils;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import com.sinitek.sirm.common.attachment.entity.Attachment;
import com.sinitek.sirm.common.attachment.service.IAttachmentService;
import com.sinitek.sirm.common.setting.utils.SettingUtils;
import com.sinitek.sirm.common.utils.IOUtil;
import com.sinitek.sirm.common.utils.NumberTool;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.framework.utils.AttachmentUtils;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.io.IOUtils;
import org.bson.Document;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.data.mongodb.gridfs.GridFsResource;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
@Slf4j
@Service
public class FileServiceImpl implements IFileService {

    public static final String FAST_GPT = "FastGPT";
    public static final int FAST_GPT_TYPE = 1;
    public static final String IMAGE_DOWNLOAD_URL = "/common/file/downloadImage";
    @Autowired
    private IAttachmentService attachmentService;

    @Autowired
    private MindFIleUtils mindFIleUtils;

    @Autowired
    private MindFileTokenUtil mindFileTokenUtil;

    @Autowired
    private IAuthService authService;

    @Override
    public String uploadImage(UploadImageRequest request) {
        log.info("开始上传图片，base64长度: {}",
            request.getBase64Img() != null ? request.getBase64Img().length() : 0);

        try {
            // 1. 使用工具类转换base64为文件（自动识别类型和生成文件名）
            File file = Base64Utils.convertBase64ToFile(request.getBase64Img());

            log.info("base64转换为文件成功: {}, 大小: {} 字节", file.getAbsolutePath(),
                file.length());

            // 2. 验证文件大小（限制为10MB）
            validateFileSize(file.length());

            long sourceId = IdUtil.getSnowflakeNextId();
            AttachmentUtils.saveAttachment(file, sourceId,
                FAST_GPT, FAST_GPT_TYPE);


            log.info("图片上传成功，文件sourceId: {}", sourceId);
            return IMAGE_DOWNLOAD_URL + "/" + sourceId;

        } catch (Exception e) {
            log.error("图片上传失败", e);
            throw new BussinessException("图片上传失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Resource downloadImage(Long sourceId) {
        log.info("开始下载文件，文件sourceId: {}", sourceId);

        try {
            // 使用附件服务获取附件对象
            Attachment attachment = attachmentService.getAttachment(FAST_GPT, sourceId, FAST_GPT_TYPE);
            InputStream content = attachment.getContent();

            String tempDir = SettingUtils.getTempDir();
            File file = new File(tempDir + File.separator + attachment.getName());

            // 写入文件
            try (FileOutputStream fos = new FileOutputStream(file)) {
                IOUtils.copy(content, fos);
                content.close();
            }

            log.info("文件下载成功，文件路径: {}, 大小: {} 字节", file.getAbsolutePath(),
                file.length());
            return new FileSystemResource(file);

        } catch (NumberFormatException e) {
            log.error("sourceId格式错误，sourceId: {}", sourceId, e);
            throw new BussinessException("文件ID格式错误: " + sourceId, e);
        } catch (Exception e) {
            log.error("文件下载失败，sourceId: {}", sourceId, e);
            throw new BussinessException("文件下载失败: " + e.getMessage(), e);
        }
    }

    @SneakyThrows
    @Override
    public UploadFileResponse uploadFile(MultipartFile file, String bucketName, Map<String, String> metadata) {
        String originalFilename = file.getOriginalFilename();
        String contentType = file.getContentType();
        InputStream inputStream = file.getInputStream();
        return commonUploadFile(originalFilename, contentType, inputStream, bucketName, metadata);
    }

    @SneakyThrows
    @Override
    public UploadFileResponse uploadFile(File file, String fileName, String bucketName, Map<String, String> metadata) {
        return commonUploadFile(fileName, "", new FileInputStream(file), bucketName, metadata);
    }

    @Override
    public FullFileDTO getFileById(String bucketName, String fileId) {
        try {
            GridFSFile file = getCommonFileInfo(bucketName, fileId);
            if (file == null) {
                return null;
            }
            BaseFileDTO baseFileDTO = buildBaseFileDTO(file, fileId);
            FullFileDTO fullFileDTO = new FullFileDTO();
            BeanUtils.copyProperties(baseFileDTO, fullFileDTO);

            GridFsResource resource = mindFIleUtils.getGridFsTemplate(bucketName).getResource(file);
            File tempDir = IOUtil.createTempDir();
            File tempFile = new File(tempDir.getPath(), file.getFilename());
            try (InputStream inputStream = resource.getInputStream();
                 FileOutputStream fos = new FileOutputStream(tempFile)) {
                 IOUtils.copy(inputStream, fos);
            }

            fullFileDTO.setSourceFile(tempFile);
            return fullFileDTO;
        } catch (Exception e) {
            log.error("从bucketName: {}获取文件: {}失败", bucketName, fileId, e);
            return null;
        }
    }

    @Override
    public BaseFileDTO getBaseFileById(String bucketName, String fileId) {
        try {
            GridFSFile file = getCommonFileInfo(bucketName, fileId);
            if (file == null) {
                return null;
            }
            return buildBaseFileDTO(file, fileId);
        } catch (Exception e) {
            log.error("从bucketName: {}获取基础文件信息: {}失败", bucketName, fileId, e);
            return null;
        }
    }

    /**
     * 通用的文件上传方法
     * @param fileName
     * @param contentType
     * @param inputStream
     * @param bucketName
     * @param metaData
     * @return
     */
    private UploadFileResponse commonUploadFile(String fileName, String contentType, InputStream inputStream, String bucketName, Map<String, String> metaData) {
        try {
            AuthDTO authDTO = authService.authCert();
            String teamId = authDTO.getTeamId();
            String userId = authDTO.getUserId();

            GridFsTemplate gridFsTemplate = mindFIleUtils.getGridFsTemplate(bucketName);
            String fileId = mindFIleUtils.store(fileName, contentType, inputStream, metaData, gridFsTemplate);

            FileTokenQuery fileTokenQuery = new FileTokenQuery();
            fileTokenQuery.setTeamId(teamId);
            fileTokenQuery.setUid(userId);
            fileTokenQuery.setBucketName(bucketName);
            fileTokenQuery.setFileId(fileId);

            String fileToken = mindFileTokenUtil.createFileToken(fileTokenQuery);
            String previewUrl = "/mind/api/common/file/read/" + fileName + "?token=" + fileToken;

            UploadFileResponse response;
            response = new UploadFileResponse();
            response.setFileId(fileId);
            response.setPreviewUrl(previewUrl);

            return response;
        } catch (Exception e) {
            log.error("上传文件: {}到bucketName: {}失败", fileName, bucketName, e);
            throw new RuntimeException("File upload failed", e);
        }
    }

    /**
     * 抽取公共文件信息获取逻辑
     */
    private GridFSFile getCommonFileInfo(String bucketName, String fileId) {
        try {
            GridFsTemplate gridFsTemplate = mindFIleUtils.getGridFsTemplate(bucketName);
            GridFSFile file = mindFIleUtils.getGridFSFileByFileId(fileId, gridFsTemplate);
            return file;
        } catch (Exception e) {
            log.error("从bucketName: {}获取文件信息: {}失败", bucketName, fileId, e);
            return null;
        }
    }

    /**
     * 根据GridFSFile构建BaseFileDTO
     */
    private BaseFileDTO buildBaseFileDTO(GridFSFile file, String fileId) {
        Document metadata = file.getMetadata();
        String contentType = MapUtils.getString(metadata, "contentType");
        String encoding = MapUtils.getString(metadata, "encoding");
        Long length = file.getLength();
        int chunkSize = file.getChunkSize();
        String filename = null;
        try {
            filename = URLDecoder.decode(file.getFilename(), StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("文件名URL解码失败: {}", file.getFilename(), e);
            filename = file.getFilename();
        }
        Date uploadDate = file.getUploadDate();

        Map<String, String> metadataMap = new HashMap<>();
        if (MapUtils.isNotEmpty(metadata)) {
            for (String key : metadata.keySet()) {
                String value = MapUtils.getString(metadata, key);
                metadataMap.put(key, value);
            }
        }

        BaseFileDTO baseFileDTO = new BaseFileDTO();
        baseFileDTO.set_id(fileId);
        baseFileDTO.setUploadDate(uploadDate);
        baseFileDTO.setFileId(fileId);
        baseFileDTO.setLength(NumberTool.safeToInteger(length, 0));
        baseFileDTO.setChunkSize(chunkSize);
        baseFileDTO.setFilename(filename);
        baseFileDTO.setContentType(contentType);
        baseFileDTO.setEncoding(encoding);
        baseFileDTO.setMetadata(metadataMap);
        return baseFileDTO;
    }

    /**
     * 验证文件大小
     */
    private void validateFileSize(long fileSize) {
        // 10MB
        long maxSize = 10 * 1024 * 1024;
        if (fileSize > maxSize) {
            throw new BussinessException("文件大小超过限制，最大允许10MB");
        }
        if (fileSize <= 0) {
            throw new BussinessException("文件大小无效");
        }
    }
}
