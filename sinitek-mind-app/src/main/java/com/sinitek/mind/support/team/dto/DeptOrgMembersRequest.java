package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 更新组织成员请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptOrgMembersRequest {
    
    @Schema(description = "组织ID")
    private String orgId;
    
    @Schema(description = "成员列表")
    private List<DeptMemberDTO> members;
} 