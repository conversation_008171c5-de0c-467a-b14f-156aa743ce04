package com.sinitek.mind.support.permission.constant;

import com.sinitek.mind.support.permission.dto.PermissionItemDTO;
import com.sinitek.mind.support.permission.enumerate.PermissionKeyEnum;
import com.sinitek.mind.support.permission.enumerate.TeamResourceEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
public class PermissionConstant {

    /**
     * 权限值位说明：
     * 第0位(1): 管理权限 | 第1位(2): 写权限 | 第2位(4): 读权限
     * 第3位(8): 应用创建 | 第4位(16): 知识库创建 | 第5位(32): API密钥创建
     * 第6位+: 扩展权限预留
     *
     * 常用值: 4=只读, 6=读写, 7=管理, 63=完整权限, 4294967295=所有者(fastgpt的总权限位数比较大-0xFFFFFFFF)
     * 检查: (用户权限 & 所需权限) == 所需权限
     */
    // 基础权限值
    public static final long NULL_PERMISSION = 0;
    public static final long OWNER_PERMISSION_VAL = 0xFFFFFFFFL;

    /**
     * 管理权限
     */
    public static final long MANAGER_PER = 0b111;

    /**
     * 写权限
     */
    public static final long WRITE_PER = 0b110;

    /**
     * 读权限
     */
    public static final long READ_PER = 0b100;

    // 团队权限位值
    public static final long TEAM_READ_PER = 0b000100;    // 4

    public static final long TEAM_WRITE_PER = 0b000010;   // 2

    public static final long TEAM_MANAGE_PER = 0b000001;  // 1

    /**
     * 应用创建权限
     */
    public static final long APP_CREATE_PER = 0b001000;

    /**
     * 知识库创建权限
     */
    public static final long DATASET_CREATE_PER = 0b010000;

    /**
     * api密钥创建权限
     */
    public static final long APIKEY_CREATE_PER = 0b100000;

    // 应用默认权限值
    public static final long APP_DEFAULT_PERMISSION_VAL = NULL_PERMISSION;

    // team默认权限值
    public static final long TEAM_DEFAULT_PERMISSION_VAL = TEAM_READ_PER;

    /**
     * 构建权限列表，都是固定值
     */
    public static final Map<String, PermissionItemDTO> PERMISSION_LIST = new HashMap<>();

    /**
     * 构建团队权限列表，都是固定值
     */
    public static final Map<String, PermissionItemDTO> TEAM_PERMISSION_LIST = new HashMap<>();

    /**
     * APP应用权限列表
     */
    public static final Map<String, PermissionItemDTO> APP_PERMISSION_LIST = new HashMap<>();

    static {
        // 基础
        PERMISSION_LIST.put(PermissionKeyEnum.READ.getValue(), new PermissionItemDTO("common:permission.read", "", READ_PER, "single"));
        PERMISSION_LIST.put(PermissionKeyEnum.WRITE.getValue(), new PermissionItemDTO("common:permission.write", "", WRITE_PER, "single"));
        PERMISSION_LIST.put(PermissionKeyEnum.MANAGE.getValue(), new PermissionItemDTO("common:permission.manager", "", MANAGER_PER, "single"));
        // 团队
        TEAM_PERMISSION_LIST.put(PermissionKeyEnum.READ.getValue(), new PermissionItemDTO("common:permission.read", "", TEAM_READ_PER, "single"));
        TEAM_PERMISSION_LIST.put(PermissionKeyEnum.WRITE.getValue(), new PermissionItemDTO("common:permission.write", "", TEAM_WRITE_PER, "single"));
        TEAM_PERMISSION_LIST.put(PermissionKeyEnum.MANAGE.getValue(), new PermissionItemDTO("common:permission.manager", "", TEAM_MANAGE_PER, "single"));
        TEAM_PERMISSION_LIST.put(TeamResourceEnum.APP_CREATE.getId(), new PermissionItemDTO("account_team:permission_appCreate", "", APP_CREATE_PER, "multiple"));
        TEAM_PERMISSION_LIST.put(TeamResourceEnum.DATASET_CREATE.getId(), new PermissionItemDTO("account_team:permission_datasetCreate", "", DATASET_CREATE_PER, "multiple"));
        TEAM_PERMISSION_LIST.put(TeamResourceEnum.APIKEY_CREATE.getId(), new PermissionItemDTO("account_team:permission_apikeyCreate", "", APIKEY_CREATE_PER, "multiple"));
        // APP
        // 应用读权限
        APP_PERMISSION_LIST.put(PermissionKeyEnum.READ.getValue(), new PermissionItemDTO("app:permission.des.read", "", READ_PER, "single"));
        // 应用写权限
        APP_PERMISSION_LIST.put(PermissionKeyEnum.WRITE.getValue(), new PermissionItemDTO("app:permission.des.write", "", WRITE_PER, "single"));
        // 应用管理权限
        APP_PERMISSION_LIST.put(PermissionKeyEnum.MANAGE.getValue(), new PermissionItemDTO("app:permission.des.manage", "", MANAGER_PER, "single"));
    }
}

