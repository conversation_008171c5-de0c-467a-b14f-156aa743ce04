package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 群组DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupDTO {
    
    @Schema(description = "群组ID")
    private String _id;
    
    @Schema(description = "团队ID")
    private String teamId;
    
    @Schema(description = "群组名称")
    private String name;
    
    @Schema(description = "群组头像")
    private String avatar;
    
    @Schema(description = "更新时间")
    private String updateTime;
    
    @Schema(description = "权限对象")
    private Object permission;

    @Schema(description = "拥有者/管理员")
    private GroupMemberDTO owner;

    @Schema(description = "成员列表")
    private List<GroupMemberDTO> members;

}