package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 更新群组信息请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateGroupRequest {
    
    @Schema(description = "群组ID")
    private String groupId;
    
    @Schema(description = "群组名称")
    private String name;
    
    @Schema(description = "群组头像URL")
    private String avatar;
    
    @Schema(description = "成员列表")
    private List<GroupMemberDTO> memberList;
} 