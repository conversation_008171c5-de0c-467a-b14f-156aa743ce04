package com.sinitek.mind.support.operationlog.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.support.operationlog.dto.AuditDTO;
import com.sinitek.mind.support.operationlog.dto.GetAuditListRequest;
import com.sinitek.mind.support.operationlog.service.IOperationLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 审计 Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@RestController
@RequestMapping("/mind/api/support/user/audit")
@Tag(name = "审计")
@RequiredArgsConstructor
public class OperationLogController {
    private final IOperationLogService operationLogService;

    @PostMapping("/list")
    @Operation(summary = "获取审计日志")
    public ApiResponse<PageResult<AuditDTO>> getOrgList(@RequestBody GetAuditListRequest request) {
        PageResult<AuditDTO> response = operationLogService.findAudit(request);
        return ApiResponse.success(response);
    }
}
