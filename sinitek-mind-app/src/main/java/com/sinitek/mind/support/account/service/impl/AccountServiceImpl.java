package com.sinitek.mind.support.account.service.impl;

import com.sinitek.mind.core.app.model.SourceMemberDTO;
import com.sinitek.mind.support.account.service.IAccountService;
import com.sinitek.mind.support.team.util.TeamMemberTransformerUtil;
import com.sinitek.sirm.org.entity.Employee;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.user.service.IUserExtService;
import com.sinitek.spirit.org.core.dto.EmployeeDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 账户服务实现类
 *
 * <AUTHOR> Assistant
 * @date 2025/7/7
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountServiceImpl implements IAccountService {

    @Autowired
    private IUserExtService userExtService;

    @Autowired
    private IOrgService orgService;

    @Override
    public String getAvatarByOrgId(String orgId) {
        return userExtService.getPhotoImageData(orgId);
    }

    @Override
    public SourceMemberDTO getSourceMemberByOrgId(String orgId) {
        String imageData = userExtService.getPhotoImageData(orgId);

        SourceMemberDTO sourceMember = new SourceMemberDTO();
        sourceMember.setAvatar(imageData);

        Employee employee = orgService.getEmployeeById(orgId);
        if (!Objects.isNull(employee)) {
            sourceMember.setName(employee.getEmpName());
            sourceMember.setStatus(TeamMemberTransformerUtil.status2gpt(String.valueOf(employee.getInservice())));
        }
        return sourceMember;
    }

    @Override
    public Map<String, SourceMemberDTO> getSourceMemberMapByOrgIds(List<String> orgIds) {
        Map<String, SourceMemberDTO> res =new HashMap<>();

        List<String> finalOrdIds = new ArrayList<>(new HashSet<>(orgIds));

        Map<String, String> imageDataMap = new HashMap<>();
        finalOrdIds.forEach(id -> {
            imageDataMap.put(id, userExtService.getPhotoImageData(id));
        });

        List<EmployeeDTO> employees = orgService.findEmpByEmpIds(finalOrdIds);
        employees.forEach(item -> {
            SourceMemberDTO sourceMember = new SourceMemberDTO();
            sourceMember.setAvatar(imageDataMap.get(item.getId()));
            sourceMember.setName(item.getName());
            sourceMember.setStatus(TeamMemberTransformerUtil.status2gpt(String.valueOf(item.isInservice())));
            res.put(item.getId(), sourceMember);
        });

        return res;
    }
}