package com.sinitek.mind.support.operationlog.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.support.operationlog.dto.AuditDTO;
import com.sinitek.mind.support.operationlog.dto.GetAuditListRequest;
import com.sinitek.mind.support.operationlog.enumerate.OperationLogEventEnum;

import java.util.Map;

/**
 * 操作记录接口
 * <AUTHOR>
 * @date 2025-07-03
 */
public interface IOperationLogService {

    void addOperationLog(OperationLogEventEnum event, Map<String, String> params);

    /**
     * 获取审计日志
     * @return
     */
    PageResult<AuditDTO> findAudit(GetAuditListRequest request);
}
