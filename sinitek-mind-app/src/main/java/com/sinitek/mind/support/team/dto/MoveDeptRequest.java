package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 移动组织请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MoveDeptRequest {
    
    @Schema(description = "要移动的组织ID")
    private String orgId;
    
    @Schema(description = "目标父组织ID，空字符串表示移动到根组织")
    private String targetOrgId;
} 