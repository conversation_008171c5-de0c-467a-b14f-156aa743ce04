package com.sinitek.mind.support.openapi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Date;

/**
 * 更新API密钥请求DTO
 * 与FastGPT的EditApiKeyProps & { _id: string }保持一致
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
public class UpdateApiKeyRequestDTO {
    
    /**
     * API密钥ID
     * JSON中对应_id字段
     */
    @JsonProperty("_id")
    private String id;
    
    /**
     * 密钥名称
     */
    private String name;
    
    /**
     * 使用限制配置
     */
    private LimitDTO limit;
    
    /**
     * 限制配置内嵌类
     */
    @Data
    public static class LimitDTO {
        /**
         * 过期时间（可选）
         */
        private Date expiredTime;
        
        /**
         * 最大使用点数（可选，-1表示无限制）
         */
        private Long maxUsagePoints;
    }
} 