package com.sinitek.mind.support.permission.service;

import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.dataset.entity.Dataset;
import com.sinitek.mind.support.permission.dto.CollaboratorDTO;
import com.sinitek.mind.support.permission.dto.CollaboratorPermissionDTO;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;

import java.util.List;
import java.util.Map;

public interface ICollaboratorService {

    List<CollaboratorPermissionDTO> getResourceClbsAndGroups(String resourceId, ResourceTypeEnum resourceType);

    void syncCollaborators(String resourceId, ResourceTypeEnum resourceType, List<CollaboratorPermissionDTO> collaborators);

    /**
     * 同步下级权限，app专用
     * @param app
     * @param folderTypeList
     * @param collaborators
     */
    void syncChildrenPermission(App app, List<String> folderTypeList, List<CollaboratorPermissionDTO> collaborators);

    /**
     * 同步下级权限，dataset专用
     * @param dataset
     * @param folderTypeList
     * @param collaborators
     */
    void syncChildrenPermission(Dataset dataset, List<String> folderTypeList, List<CollaboratorPermissionDTO> collaborators);

    /**
     * 获取指定资源的协作者
     * 当资源类型为team时，资源id则为teamId
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     */
    List<CollaboratorDTO> findClb(String resourceId, ResourceTypeEnum resourceTypeEnum);

    /**
     * 更新协作者
     * 当资源类型为team时，资源id则为teamId
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgIdList 授权的对象，成员id、部门id、群组id
     * @param permission 权限值-需要设置的权限值，可查看PermissionConstant中的定义
     */
    void updateClb(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList, long permission);

    /**
     * 删除协作者
     * 当资源类型为team时，资源id则为teamId
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgId 需要删除的对象，成员id、部门id、群组id
     */
    void deleteClb(String resourceId, ResourceTypeEnum resourceTypeEnum, String orgId);

    /**
     * 查询指定资源的协作者数量
     *
     * @param resourceIdList   资源id列表
     * @param resourceTypeEnum 资源类型
     * @return map，key-资源id。value-协作者数量
     */
    Map<String, Integer> findClbNumberMap(List<String> resourceIdList, ResourceTypeEnum resourceTypeEnum);
}
