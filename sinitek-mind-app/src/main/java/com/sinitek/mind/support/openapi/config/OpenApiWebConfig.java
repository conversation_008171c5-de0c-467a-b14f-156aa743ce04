package com.sinitek.mind.support.openapi.config;

import com.sinitek.mind.support.openapi.interceptor.OpenApiAuthInterceptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * OpenAPI模块Web配置
 * 自动注册OpenAPI认证拦截器
 * 
 * 拦截路径规则：
 * - /api/v1/** : OpenAI v1 API标准路径 (如: /api/v1/chat/completions, /api/v1/embeddings)
 * - /api/v2/** : OpenAI v2 API标准路径 (如: /api/v2/chat/completions)
 * - /api/mcp/app/* /mcp : MCP路径 (如: /api/mcp/app/ynKsG2VFpjAmkvbaplRXAjQV/mcp)
 * 
 * 设计考虑：
 * - 符合OpenAI API标准路径规范
 * - 将管理接口与业务接口分离
 * - 自动配置，用户无需手动配置
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Configuration
public class OpenApiWebConfig implements WebMvcConfigurer {
    
    @Autowired
    private OpenApiAuthInterceptor openApiAuthInterceptor;
    
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(openApiAuthInterceptor)
                // 拦截OpenAI标准API路径
                .addPathPatterns("/mind/open-api/**")
                .order(Integer.MAX_VALUE);
    }
} 