package com.sinitek.mind.support.openapi.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * OpenAPI密钥实体类
 * 对应FastGPT中的OpenApiSchema
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Document(collection = "openapis")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenApi {
    
    @Id
    private String id;
    
    /**
     * 团队ID
     */
    @Indexed
    private String teamId;
    
    /**
     * 团队成员ID
     */
    @Indexed
    private String tmbId;
    
    /**
     * API密钥，格式：sinitek-xxxx
     */
    @Indexed(unique = true)
    private String apiKey;
    
    /**
     * 创建时间
     */
    @Builder.Default
    private Date createTime = new Date();
    
    /**
     * 最后使用时间
     */
    private Date lastUsedTime;
    
    /**
     * 关联的应用ID（可选）
     */
    private String appId;
    
    /**
     * 密钥名称
     */
    @Builder.Default
    private String name = "Api Key";
    
    /**
     * 累计使用点数
     */
    @Builder.Default
    private Long usagePoints = 0L;
    
    /**
     * 使用限制配置
     */
    private UsageLimit limit;
    
    /**
     * 使用限制内嵌类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class UsageLimit {
        /**
         * 过期时间
         */
        private Date expiredTime;
        
        /**
         * 最大使用点数，-1表示无限制
         */
        @Builder.Default
        private Long maxUsagePoints = -1L;
    }
} 