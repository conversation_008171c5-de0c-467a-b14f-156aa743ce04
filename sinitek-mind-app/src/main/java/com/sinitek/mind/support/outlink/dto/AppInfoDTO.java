package com.sinitek.mind.support.outlink.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "应用信息DTO")
public class AppInfoDTO {

    @Schema(description = "应用ID")
    private String appId;

    @Schema(description = "关联应用信息")
    private AssociatedAppDTO associatedApp;

    @Schema(description = "是否显示原始引用")
    private Boolean showRawSource;

    @Schema(description = "是否显示详细响应")
    private Boolean responseDetail;

    @Schema(description = "是否显示节点状态")
    private Boolean showNodeStatus;

    @Data
    @Schema(description = "关联应用基本信息")
    public static class AssociatedAppDTO {

        @Schema(description = "应用名称")
        private String name;

        @Schema(description = "应用头像")
        private String avatar;

        @Schema(description = "应用介绍")
        private String intro;
    }
} 