package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.dto.CreateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.dto.UpdateApiKeyRequestDTO;
import com.sinitek.mind.support.openapi.service.IOpenApiService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * OpenAPI管理控制器
 * 与FastGPT的API接口保持一致
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/mind/api/support/openapi")
@Tag(name = "API密钥管理")
@Slf4j
public class OpenApiController {
    
    @Autowired
    private IOpenApiService openApiService;

    /**
     * 创建API密钥
     * 对应 FastGPT: POST /support/openapi/create
     */
    @PostMapping("/create")
    @Operation(summary = "创建API密钥")
    public ApiResponse<String> createApiKey(@RequestBody CreateApiKeyRequestDTO request) {
        try {
            // 从认证上下文获取用户信息（与FastGPT保持一致）
            String teamId = CurrentUserFactory.getTenantId();
            String tmbId = CurrentUserFactory.getOrgId();
            
            String apiKey = openApiService.createApiKey(teamId, tmbId, request);
            return ApiResponse.success(apiKey);
        } catch (Exception e) {
            log.error("创建API密钥失败", e);
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 获取API密钥列表
     * 对应 FastGPT: GET /support/openapi/list?appId=xxx
     */
    @GetMapping("/list")
    @Operation(summary = "获取API密钥列表")
    public ApiResponse<List<ApiKeySearchResultDTO>> listApiKeys(
            @RequestParam(required = false) String appId) {
        try {
            // 从认证上下文获取用户信息
            String tmbId = CurrentUserFactory.getOrgId();
            
            List<ApiKeySearchResultDTO> apiKeys = openApiService.listApiKeysForFastGpt(tmbId, appId);
            return ApiResponse.success(apiKeys);
        } catch (Exception e) {
            log.error("获取API密钥列表失败", e);
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 更新API密钥
     * 对应 FastGPT: PUT /support/openapi/update
     */
    @PostMapping("/update")
    @Operation(summary = "更新API密钥")
    public ApiResponse<String> updateApiKey(@RequestBody UpdateApiKeyRequestDTO request) {
        try {
            openApiService.updateApiKey(request);
            return ApiResponse.success("更新成功");
        } catch (Exception e) {
            log.error("更新API密钥失败", e);
            return ApiResponse.error(400, e.getMessage());
        }
    }
    
    /**
     * 删除API密钥
     * 对应 FastGPT: DELETE /support/openapi/delete?id=xxx
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除API密钥")
    public ApiResponse<String> deleteApiKey(@RequestParam String id) {
        try {
            openApiService.deleteApiKey(id);
            return ApiResponse.success("删除成功");
        } catch (Exception e) {
            log.error("删除API密钥失败", e);
            return ApiResponse.error(400, e.getMessage());
        }
    }
} 