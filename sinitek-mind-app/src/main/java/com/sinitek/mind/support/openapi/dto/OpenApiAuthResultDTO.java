package com.sinitek.mind.support.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * OpenAPI认证结果DTO
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OpenApiAuthResultDTO {
    
    /**
     * 团队ID
     */
    private String teamId;
    
    /**
     * 团队成员ID
     */
    private String tmbId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 来源名称（API密钥名称）
     */
    private String sourceName;
    
    /**
     * API密钥
     */
    private String apiKey;
} 