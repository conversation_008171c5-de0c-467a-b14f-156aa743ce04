package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 创建组织请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateDeptRequest {
    
    @Schema(description = "组织名称")
    @NotNull
    private String name;
    
    @Schema(description = "组织描述")
    private String description;
    
    @Schema(description = "组织头像URL")
    private String avatar;
    
    @Schema(description = "父组织ID，不传则创建在根组织下")
    private String orgId;
} 