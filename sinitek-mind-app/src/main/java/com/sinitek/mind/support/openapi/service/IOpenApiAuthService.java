package com.sinitek.mind.support.openapi.service;

import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import com.sinitek.sirm.common.um.RequestUser;

/**
 * OpenAPI认证服务接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
public interface IOpenApiAuthService {
    
    /**
     * 认证API密钥
     */
    OpenApiAuthResultDTO authenticateApiKey(String apiKey);

    /**
     * 生成当前用户信息
     * @param apiKey 密钥
     */
    RequestUser generateUserInfo(String apiKey);
} 