package com.sinitek.mind.support.team.dto;

import com.sinitek.mind.common.support.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 团队成员列表请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamMemberListRequest extends PageParam {
    
    @Schema(description = "成员状态：'active'或'inactive'")
    private String status;
    
    @Schema(description = "是否包含组织信息")
    private Boolean withOrgs;
    
    @Schema(description = "是否包含权限信息")
    private Boolean withPermission;
    
    @Schema(description = "搜索关键词")
    private String searchKey;
    
    @Schema(description = "组织ID筛选")
    private String orgId;
    
    @Schema(description = "群组ID筛选")
    private String groupId;
} 