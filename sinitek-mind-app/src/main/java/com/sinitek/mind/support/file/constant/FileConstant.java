package com.sinitek.mind.support.file.constant;

import java.util.Arrays;
import java.util.List;

/**
 * 文件常量
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
public class FileConstant {
    
    /**
     * 文档文件类型
     */
    public static final List<String> DOCUMENT_FILE_TYPES = Arrays.asList(
        "txt", "docx", "csv", "xlsx", "pdf", "md", "html", "pptx"
    );
    
    /**
     * 图片文件类型
     */
    public static final List<String> IMAGE_FILE_TYPES = Arrays.asList(
        "jpg", "jpeg", "png", "gif", "bmp", "webp", "svg", "tiff", "tif", 
        "ico", "heic", "heif", "avif", "raw", "cr2", "nef", "arw", "dng", 
        "psd", "ai", "eps", "emf", "wmf", "jfif", "exif", "pgm", "ppm", 
        "pbm", "jp2", "j2k", "jpf", "jpx", "jpm", "mj2", "xbm", "pcx"
    );
    
    /**
     * 有效的URL前缀
     */
    public static final List<String> VALID_URL_PREFIXES = Arrays.asList("/", "http", "ws");
    
    /**
     * 原始文本缓冲桶名称
     */
    public static final String RAW_TEXT_BUFFER_BUCKET = "buffer_rawtext";
    
    /**
     * 最大文件大小（10MB）
     */
    public static final int MAX_FILE_SIZE_FOR_DIRECT_READ = 10_000_000;
    
    /**
     * 最小块大小（128KB）
     */
    public static final int MIN_CHUNK_SIZE = 128 * 1024;
    
    /**
     * 最大块大小（14MB）
     */
    public static final int MAX_CHUNK_SIZE = 14 * 1024 * 1024;
    
    /**
     * 块大小对齐（64KB）
     */
    public static final int CHUNK_SIZE_ALIGNMENT = 64 * 1024;
    
    /**
     * 目标块数
     */
    public static final int TARGET_CHUNK_COUNT = 10;
}