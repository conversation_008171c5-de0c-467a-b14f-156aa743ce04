package com.sinitek.mind.support.operationlog.dto;

import com.sinitek.mind.core.app.model.SourceMemberDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/4
 */
@Data
@Schema(description = "审计DTO")
public class AuditDTO {

    @Schema(description = "")
    private String _id;

    @Schema(description = "操作人")
    private SourceMemberDTO sourceMember;

    @Schema(description = "事件")
    private String event;

    @Schema(description = "记录时间")
    private Date timestamp;

    @Schema(description = "详细数据")
    private Map metadata;
}
