package com.sinitek.mind.support.outlink.repository;

import com.sinitek.mind.support.outlink.entity.OutLink;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;
import java.util.Optional;

public interface OutLinkRepository extends MongoRepository<OutLink, String> {
    
    void deleteByAppId(String id);
    
    /**
     * 根据应用ID和类型查询免登录链接列表
     */
    List<OutLink> findByAppIdAndType(String appId, String type);
    
    /**
     * 根据shareId查询免登录链接
     */
    Optional<OutLink> findByShareId(String shareId);
}
