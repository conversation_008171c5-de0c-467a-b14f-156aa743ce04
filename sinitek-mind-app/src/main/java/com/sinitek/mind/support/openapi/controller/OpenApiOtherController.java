package com.sinitek.mind.support.openapi.controller;

import com.sinitek.mind.common.support.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 其他的OpenApi接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/mind/open-api")
@Tag(name = "其他接口", description = "其他相关接口")
public class OpenApiOtherController {

    @PostMapping("/core/ai/agent/v2/createQuestionGuide")
    @Operation(summary = "猜你想问", description = "猜你想问")
    public ApiResponse<?> createQuestionGuide() {
        return ApiResponse.success();
    }

    @PostMapping("/support/wallet/usage/createTrainingUsage")
    @Operation(summary = "创建训练订单", description = "创建训练订单")
    public ApiResponse<?> createTrainingUsage() {
        return ApiResponse.success();
    }

    @PostMapping("/proApi/core/dataset/collection/create/externalFileUrl")
    @Operation(summary = "创建外部文件库集合（商业版）", description = "创建外部文件库集合（商业版）")
    public ApiResponse<?> createExternalFileUrlCollection() {
        return ApiResponse.success();
    }

}