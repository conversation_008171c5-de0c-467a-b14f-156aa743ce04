package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 组织成员响应DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DeptMembersResponse {
    
    @Schema(description = "总数量")
    private Integer total;
    
    @Schema(description = "成员列表（格式同团队成员列表）")
    private List<TeamMemberDTO> list;
} 