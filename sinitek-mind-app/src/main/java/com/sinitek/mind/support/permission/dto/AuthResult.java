package com.sinitek.mind.support.permission.dto;

import com.sinitek.mind.core.app.model.AppDetailType;
import com.sinitek.mind.support.permission.constant.Permission;
import lombok.Data;

/**
 * 认证结果DTO
 */
@Data
public class AuthResult<T extends Permission> {
    private String teamId;
    private String tmbId;
    private String userId;
    private String authType; // AuthUserTypeEnum
    private String appId;
    private String apiKey;
    private String sourceName;
    private boolean isRoot;
    private T permission;
    private AppDetailType app; // 可以根据需要定义应用对象类型
    private Object tmb;
}