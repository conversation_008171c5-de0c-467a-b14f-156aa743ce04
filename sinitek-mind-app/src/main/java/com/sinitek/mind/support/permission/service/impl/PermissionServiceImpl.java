package com.sinitek.mind.support.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.sinitek.mind.core.app.enumerate.AppTypeEnum;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.support.permission.constant.PermissionConstant;
import com.sinitek.mind.support.permission.dto.Permission;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.permission.enumerate.AuthTypeEnum;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;
import com.sinitek.mind.support.permission.enumerate.TeamResourceEnum;
import com.sinitek.mind.support.permission.service.IPermissionService;
import com.sinitek.sirm.enumerate.CommonBooleanEnum;
import com.sinitek.sirm.framework.exception.BussinessException;
import com.sinitek.sirm.org.service.IOrgService;
import com.sinitek.sirm.org.service.IRightService;
import com.sinitek.sirm.right.IRightExtService;
import com.sinitek.spirit.right.core.IRightAuthInfo;
import com.sinitek.spirit.right.core.IRightUpdater;
import com.sinitek.spirit.right.server.entity.RightAuth;
import com.sinitek.spirit.right.server.service.ISprtRightAuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PermissionServiceImpl implements IPermissionService {

    private static final List<String> APP_FOLDER_TYPE_LIST = List.of(AppTypeEnum.FOLDER.getValue(), AppTypeEnum.HTTP_PLUGIN.getValue());

    private static final String[] ALL_AUTH_TYPE = new String[]{
        AuthTypeEnum.WRITE.getName(),
        AuthTypeEnum.READ.getName(),
        AuthTypeEnum.MANAGE.getName()
    };

    private static final List<AuthTypeEnum> ALL_AUTH_TYPE_ENUM = List.of(AuthTypeEnum.READ, AuthTypeEnum.WRITE,
            AuthTypeEnum.MANAGE);

    @Autowired
    private IOrgService orgService;

    @Autowired
    private IRightExtService rightExtService;

    @Autowired
    private IRightService rightService;

    @Autowired
    private final AppRepository appRepository;

    @Autowired
    private IRightUpdater rightUpdater;

    @Autowired
    private ISprtRightAuthService sprtRightAuthService;

    @Override
    public PermissionDTO getPermissionResultDTO(String orgId, String resourceId,
                                                String resourceType) {
        if (StringUtils.isEmpty(orgId)) {
            log.warn("获取指定对象权限结果DTO,orgId不能为空");
            return new TeamPermissionDTO();
        }

        // type=3,表示查询上级节点和自身的权限
        List<RightAuth> authDTOList = rightService.findAllAuthList(orgId, resourceType,
            ALL_AUTH_TYPE, 3, false);

        if (CollUtil.isEmpty(authDTOList)) {
            return new PermissionDTO();
        }

        List<RightAuth> filterAuthList = authDTOList.stream()
            .filter(dto -> ObjectUtil.equals(resourceId, dto.getObjectKey()))
            .toList();

        if (CollUtil.isEmpty(filterAuthList)) {
            return new PermissionDTO();
        }

        boolean isManage = false;
        boolean isWrite = false;
        boolean isRead = false;

        // 只要有一个权限对象有权限，就说明有权限
        for (RightAuth authDTO : filterAuthList) {
            if (AuthTypeEnum.MANAGE.getName().equals(authDTO.getRightType())) {
                isManage = true;
            } else if (AuthTypeEnum.WRITE.getName().equals(authDTO.getRightType())) {
                isWrite = true;
            } else if (AuthTypeEnum.READ.getName().equals(authDTO.getRightType())){
                isRead = true;
            }
        }

        PermissionDTO resultDTO = new PermissionDTO();
        if (isManage) {
            resultDTO.setValue(PermissionConstant.MANAGER_PER);
        } else if (isWrite) {
            resultDTO.setValue(PermissionConstant.WRITE_PER);
        } else if (isRead) {
            resultDTO.setValue(PermissionConstant.READ_PER);
        } else {
            resultDTO.setValue(PermissionConstant.NULL_PERMISSION);
        }

        return resultDTO;
    }

    @Override
    public TeamPermissionDTO getTeamPermissionResultDTO(String orgId) {
        if (StringUtils.isEmpty(orgId)) {
            log.warn("获取指定对象权限结果DTO,orgId不能为空");
            return new TeamPermissionDTO();
        }

        // 团队权限中的权限类型是固定的
        // type=3,表示查询上级节点和自身的权限
        List<RightAuth> authDTOList = rightService.findAllAuthList(orgId,
            ResourceTypeEnum.TEAM.getValue(), new String[]{AuthTypeEnum.TEAM_AUTH_TYPE.getName()}, 3, false);

        // 键为resourceId，值为是否有权限
        Map<String, Boolean> authMap = authDTOList.stream()
            .map(entity -> {
                    Boolean rejectFlag = CommonBooleanEnum.getBoolValueByValue(entity.getRejectFlag());
                return Tuples.of(entity.getObjectKey(), rejectFlag);
            })
            .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));

        // 设置权限结果类
        TeamPermissionDTO resultDTO = new TeamPermissionDTO();
        boolean appCreate = MapUtil.getBool(authMap, TeamResourceEnum.APP_CREATE.getId(), true);
        boolean datasetCreate = MapUtil.getBool(authMap, TeamResourceEnum.DATASET_CREATE.getId(), true);
        boolean apikeyCreate = MapUtil.getBool(authMap, TeamResourceEnum.APIKEY_CREATE.getId(), true);
        boolean manager = MapUtil.getBool(authMap, TeamResourceEnum.MANAGER.getId(), true);

        long per = PermissionConstant.NULL_PERMISSION;
        per += !manager ? PermissionConstant.TEAM_MANAGE_PER + PermissionConstant.TEAM_READ_PER : 0;
        per += !appCreate ? PermissionConstant.APP_CREATE_PER : 0;
        per += !datasetCreate ? PermissionConstant.DATASET_CREATE_PER : 0;
        per += !apikeyCreate ? PermissionConstant.APIKEY_CREATE_PER : 0;

        resultDTO.setValue(per);

        return resultDTO;
    }

    @Override
    public Map<String, TeamPermissionDTO> getTeamPermissionResultDTO(List<String> orgIds) {
        if (CollUtil.isEmpty(orgIds)) {
            log.warn("获取指定对象权限结果DTO,orgIds不能为空");
            return Map.of();
        }

        // TODO 能优化吗？
        return orgIds.stream()
            .map(orgId -> Tuples.of(orgId, getTeamPermissionResultDTO(orgId)))
            .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));
    }

    @Override
    public List<Permission> findAllAuthedResource(String orgId,
                                                  ResourceTypeEnum resourceType, List<AuthTypeEnum> authTypeList) {

        if (StringUtils.isBlank(orgId) || resourceType == null || CollUtil.isEmpty(
            authTypeList)) {
            return List.of();
        }

        String[] rightType = authTypeList.stream()
                        .map(AuthTypeEnum::getName)
                                .toArray(String[]::new);
        List<RightAuth> allAuthList = rightService.findAllAuthList(orgId, resourceType.getValue(), rightType, 3, false);

        return allAuthList.stream()
            .map(this::convertRightAuth2Permission)
            .toList();
    }

    @Override
    public List<String> findAllAuthedResourceId(String orgId, ResourceTypeEnum resourceType, List<AuthTypeEnum> authTypeList) {
        if (StringUtils.isBlank(orgId) || resourceType == null || CollUtil.isEmpty(
                authTypeList)) {
            return List.of();
        }

        String[] rightType = authTypeList.stream()
                .map(AuthTypeEnum::getName)
                .toArray(String[]::new);
        List<RightAuth> allAuthList = rightService.findAllAuthList(orgId, resourceType.getValue(), rightType, 3, false);

        return allAuthList.stream()
                .map(RightAuth::getObjectKey)
                .toList();
    }

    @Override
    public Map<String, Permission> findAllAuthedOrg(String resourceId, ResourceTypeEnum resourceTypeEnum) {
        // 优先判断是不是团队
        if (ObjectUtil.equals(resourceTypeEnum, ResourceTypeEnum.TEAM )) {
            throw new BussinessException("不支持团队资源类型");
        }

        if (StringUtils.isBlank(resourceId) || resourceTypeEnum == null) {
            return Map.of();
        }

        List<Permission> permission = findPermission(resourceId, resourceTypeEnum, ALL_AUTH_TYPE_ENUM);

        if (CollUtil.isEmpty(permission)) {
            return Map.of();
        }

        return permission.stream()
                .collect(Collectors.toMap(Permission::getOrgId, Function.identity()));
    }

    @Override
    public Map<String, Integer> findAllAuthedOrgNumber(List<String> resourceIdList, ResourceTypeEnum resourceTypeEnum) {
        // 优先判断是不是团队
        if (ObjectUtil.equals(resourceTypeEnum, ResourceTypeEnum.TEAM)) {
            throw new BussinessException("不支持团队资源类型使用");
        }

        if (CollUtil.isEmpty(resourceIdList) || resourceTypeEnum == null) {
            return Map.of();
        }

        // app和Dataset
        List<IRightAuthInfo> rightAuthInfos = rightUpdater.getRightAuthInfos(resourceTypeEnum.getValue(), null);

        if (CollUtil.isEmpty(rightAuthInfos)) {
            return resourceIdList.stream()
                    .collect(Collectors.toMap(Function.identity(), key -> 0));
        }

        // 转换
        return rightAuthInfos.stream()
                .filter(item -> resourceIdList.contains(item.getObjectKey()))
                .collect(Collectors.groupingBy(IRightAuthInfo::getObjectKey))
                .entrySet()
                .stream()
                .map((entry) -> {
                    String resourceId = entry.getKey();
                    int authCount = entry.getValue().size();
                    return Tuples.of(resourceId, authCount);
                })
                .collect(Collectors.toMap(Tuple2::getT1, Tuple2::getT2));
    }


    /**
     * 保存权限 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgIdList 需要保存权限的对象，成员id、部门id、群组id
     * @param per 权限值
     */
    @Override
    public void savePermission(String resourceId, ResourceTypeEnum resourceTypeEnum,
        List<String> orgIdList, long per) {

        if (CollUtil.isEmpty(orgIdList)) {
            // 不需要在添加权限了
            log.warn("orgIdList为空，无法保存权限");
            return;
        }

        if (ObjectUtil.equals(ResourceTypeEnum.TEAM, resourceTypeEnum)) {
            saveTeamPermission(orgIdList, per);
            return;
        }

        // app和dataset使用
        List<Permission> permissionList = findPermission(resourceId, resourceTypeEnum, ALL_AUTH_TYPE_ENUM);
        List<String> dbOrgIdList = permissionList.stream()
                .map(Permission::getOrgId)
                .toList();

        // 先删除对应资源的全部权限
        List<String> needDeleteOrgId = dbOrgIdList.stream()
            .filter(orgIdList::contains)
            .toList();

        // 删除指定资源的权限
        deletePermission(resourceId, resourceTypeEnum, needDeleteOrgId);

        // 从权限值中计算出，要保存的authType
        AuthTypeEnum authTypeEnum = calcAuthType(per);

        List<RightAuth> needSaveList = orgIdList.stream()
            .map(orgId -> {
                RightAuth rightAuth = new RightAuth();
                rightAuth.setAuthOrgId(orgId);
                rightAuth.setObjectKey(resourceId);
                rightAuth.setRightDefineKey(resourceTypeEnum.getValue());
                rightAuth.setRejectFlag(0);
                rightAuth.setRightType(authTypeEnum.getName());

                return rightAuth;
            })
            .toList();
        sprtRightAuthService.saveBatchRightAuthList(needSaveList);
    }

    @Override
    public void deletePermission(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList) {
        if (CollUtil.isEmpty(orgIdList)) {
            log.info("需要删除授权的对象为空，不进行删除授权");
            return;
        }
        if (ObjectUtil.equals(ResourceTypeEnum.TEAM, resourceTypeEnum)) {
            // 团队的删除逻辑
            removeTeamPermission(orgIdList);
        } else {
            // app和dataset的删除逻辑
            removePermission(resourceId, resourceTypeEnum, orgIdList);
        }
    }

    @Override
    public void deletePermission(String resourceId, ResourceTypeEnum resourceTypeEnum) {
        if (ObjectUtil.equals(ResourceTypeEnum.TEAM, resourceTypeEnum)) {
            // 团队的删除逻辑
            rightService.deleteRightAuth(ResourceTypeEnum.TEAM.getValue(), new String[]{AuthTypeEnum.TEAM_AUTH_TYPE.getName()});
        } else {
            // app和dataset的删除逻辑
            rightService.deleteRightAuth(resourceId, resourceTypeEnum.getValue(), ALL_AUTH_TYPE);
        }
    }


    /**
     * 保存团队的权限
     * @param orgIdList
     * @param per
     */
    private void saveTeamPermission(List<String> orgIdList, long per) {
        // 团队这边的权限，需要修改rejectFlag，来表示是否含有权限
        if (CollUtil.isEmpty(orgIdList)) {
            log.info("保存团队权限时，组织id列表为空，不进行修改");
            return;
        }

        removeTeamPermission(orgIdList);

        List<RightAuth> needSaveList = new LinkedList<>();
        orgIdList.forEach(orgId -> {
            RightAuth appCreate = generateTeamRightAuthDTO(orgId, TeamResourceEnum.APP_CREATE);
            RightAuth apikeyCreate = generateTeamRightAuthDTO(orgId, TeamResourceEnum.APIKEY_CREATE);
            RightAuth datasetCreate = generateTeamRightAuthDTO(orgId, TeamResourceEnum.DATASET_CREATE);
            RightAuth manager = generateTeamRightAuthDTO(orgId, TeamResourceEnum.MANAGER);

            // 因为表示的是否被拒绝，有权限时，结果为0
            appCreate.setRejectFlag((PermissionConstant.APP_CREATE_PER & per) == PermissionConstant.APP_CREATE_PER ? 0 : 1);
            apikeyCreate.setRejectFlag((
                    PermissionConstant.APIKEY_CREATE_PER & per) == PermissionConstant.APIKEY_CREATE_PER ? 0 : 1);
            datasetCreate.setRejectFlag((
                    PermissionConstant.DATASET_CREATE_PER & per) == PermissionConstant.DATASET_CREATE_PER ? 0 : 1);
            manager.setRejectFlag((PermissionConstant.TEAM_MANAGE_PER & per) == PermissionConstant.TEAM_MANAGE_PER ? 0 : 1);

            needSaveList.add(appCreate);
            needSaveList.add(apikeyCreate);
            needSaveList.add(datasetCreate);
            needSaveList.add(manager);
        });

        sprtRightAuthService.saveBatchRightAuthList(needSaveList);
    }

    /**
     * 生成团队权限DTO，默认无权限
     * @param orgId 组织id
     * @return 默认团队权限DTO
     */
    private RightAuth generateTeamRightAuthDTO(String orgId, TeamResourceEnum resourceEnum) {
        RightAuth dto = new RightAuth();
        dto.setAuthOrgId(orgId);
        dto.setObjectKey(resourceEnum.getId());
        dto.setRightDefineKey(resourceEnum.getType());
        dto.setRejectFlag(1);
        dto.setRightType(AuthTypeEnum.TEAM_AUTH_TYPE.getName());
        return dto;
    }


    /**
     * 从权限值中计算出authType
     * 只有资源类型为app和dataset使用，团队的为固定值AuthTypeEnum.TEAM_AUTH_TYPE
     * @param per 权限值
     * @return authTypeEnum
     */
    private AuthTypeEnum calcAuthType(long per) {
        // app和dataset
        for (AuthTypeEnum authTypeEnum : AuthTypeEnum.values()) {
            if (ObjectUtil.equals(per, authTypeEnum.getValue())) {
                return authTypeEnum;
            }
        }

        if (per == PermissionConstant.OWNER_PERMISSION_VAL) {
            // 拥有者是在具体场景下判断设置的，数据库中只能存储一个管理权限
            return AuthTypeEnum.MANAGE;
        }

        // 默认权限
        return AuthTypeEnum.READ;
    }

    private Permission convertRightAuth2Permission(RightAuth rightAuth) {
        String objectKey = rightAuth.getObjectKey();
        String rightDefineKey = rightAuth.getRightDefineKey();
        String rightType = rightAuth.getRightType();
        Long objId = rightAuth.getObjId();
        String orgId = rightAuth.getAuthOrgId();

        Permission permission = new Permission();
        permission.setId(objId);
        permission.setOrgId(orgId);
        permission.setResourceType(rightDefineKey);
        permission.setAuthType(rightType);
        permission.setResourceId(objectKey);

        long per = calcPerValueByAuthType(orgId, rightType);
        permission.setPermission(per);

        return permission;
    }

    /**
     * 计算权限值
     *
     * @param orgId 组织id，可以为-tmbId、deptId、groupId
     * @param authType 权限类型
     * @return 计算出的权限值
     */
    private long calcPerValueByAuthType(String orgId, String authType) {
        // 默认无权限
        long per = PermissionConstant.NULL_PERMISSION;
        if (AuthTypeEnum.READ.getName().equals(authType)) {
            // 只读
            per = PermissionConstant.READ_PER;
        } else if (AuthTypeEnum.WRITE.getName().equals(authType)) {
            // 写
            per = PermissionConstant.WRITE_PER;
        } else if (AuthTypeEnum.MANAGE.getName().equals(authType)) {
            // 管理
            per = PermissionConstant.MANAGER_PER;
        } else if (AuthTypeEnum.TEAM_AUTH_TYPE.getName().equals(authType)) {
            // 团队
            per = getTeamPermissionResultDTO(orgId).getValue();
        }
        return per;
    }

    /**
     * 都不能为空
     * @param resourceId
     * @param resourceTypeEnum
     * @param authTypeEnumList
     * @return
     */
    private List<Permission> findPermission(String resourceId, ResourceTypeEnum resourceTypeEnum, List<AuthTypeEnum> authTypeEnumList) {
        QueryWrapper<RightAuth> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("objectkey", resourceId);
        queryWrapper.eq("rightdeginekey", resourceTypeEnum.getValue());
        List<String> rightType = authTypeEnumList.stream()
                .map(AuthTypeEnum::getName).toList();
        queryWrapper.in("righttype", rightType);
        List<RightAuth> list = sprtRightAuthService.list(queryWrapper);

        if (CollUtil.isEmpty(list)) {
            return List.of();
        }

        return list.stream()
                .map(this::convertRightAuth2Permission)
                .toList();
    }

    /**
     * 查找用户的团队权限
     * @param orgIdList 不为空
     * @return
     */
    private List<Permission> findTeamPermission(List<String> orgIdList) {
        QueryWrapper<RightAuth> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("authorgid", orgIdList);
        List<RightAuth> list = sprtRightAuthService.list(queryWrapper);

        if (CollUtil.isEmpty(list)) {
            return List.of();
        }

        return list.stream()
                .map(this::convertRightAuth2Permission)
                .toList();
    }

    private void removePermission(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList) {
        List<Permission> permissionList = findPermission(resourceId, resourceTypeEnum, ALL_AUTH_TYPE_ENUM);
        if (CollUtil.isEmpty(permissionList)) {
            return;
        }

        List<RightAuth> needDelete = permissionList.stream()
                .filter(item -> orgIdList.contains(item.getOrgId()))
                .map(item -> {
                    RightAuth rightAuth = new RightAuth();
                    rightAuth.setObjId(item.getId());
                    return rightAuth;
                })
                .toList();

        sprtRightAuthService.removeBatchRightAuthList(needDelete);
    }

    /**
     * 删除指定用户的团队权限
     * @param orgIdList 不为空
     */
    private void removeTeamPermission(List<String> orgIdList) {
        List<Permission> teamPermission = findTeamPermission(orgIdList);
        List<RightAuth> needDeleteList = teamPermission.stream()
                .map(Permission::getId)
                .map(id -> {
                    RightAuth rightAuth = new RightAuth();
                    rightAuth.setObjId(id);
                    return rightAuth;
                })
                .toList();
        sprtRightAuthService.removeBatchRightAuthList(needDeleteList);
    }
}
