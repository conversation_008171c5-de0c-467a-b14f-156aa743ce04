package com.sinitek.mind.support.permission.dto;

import com.sinitek.mind.support.permission.constant.PermissionConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.LinkedList;
import java.util.List;

/**
 * 协作者信息更新DTO
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "协作者更新基础对象")
public class CollaboratorUpdateDTO {

    @Schema(description = "权限值")
    private Long permission = PermissionConstant.NULL_PERMISSION;

    @Schema(description = "团队成员ID（groups、orgs）")
    private List<String> members = new LinkedList<>();

    @Schema(description = "群组ID（members、orgs）")
    private List<String> groups = new LinkedList<>();

    @Schema(description = "组织ID（members、groups）")
    private List<String> orgs = new LinkedList<>();
}