package com.sinitek.mind.support.permission.service;

import com.sinitek.mind.support.permission.dto.Permission;
import com.sinitek.mind.support.permission.dto.PermissionDTO;
import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.permission.enumerate.AuthTypeEnum;
import com.sinitek.mind.support.permission.enumerate.ResourceTypeEnum;

import java.util.List;
import java.util.Map;

public interface IPermissionService {

    /**
     * 通过资源id、资源类型和成员id获取权限结果DTO
     * @param orgId 授权对象id-可以为成员、部门、群组
     * @param resourceId 资源id
     * @param resourceType 资源类型
     * @return
     */
    PermissionDTO getPermissionResultDTO(String orgId, String resourceId, String resourceType);

    /**
     * 通过资源id、资源类型和orgId获取团队权限结果DTO
     * @param orgId orgId，可以为成员id、部门id、群组id
     * @return
     */
    TeamPermissionDTO getTeamPermissionResultDTO(String orgId);

    /**
     * 通过资源id、资源类型和orgId获取团队权限结果DTO
     * @param orgId orgId，可以为成员id、部门id、群组id
     * @return
     */
    Map<String, TeamPermissionDTO> getTeamPermissionResultDTO(List<String> orgId);

    /**
     * 获取指定对象的所有已授权的资源
     * @param orgId 对象id-可以为tmbId、deptId、groupId
     * @param resourceType 资源类型
     * @param authTypeList 权限类型，不能为空，@see AuthTypeEnum
     * @return 已授权的资源
     */
    List<Permission> findAllAuthedResource(String orgId, ResourceTypeEnum resourceType, List<AuthTypeEnum> authTypeList);

    /**
     * 获取指定对象的所有已授权的资源
     *
     * @param orgId            对象id-可以为tmbId、deptId、groupId
     * @param resourceType 资源类型
     * @param authTypeList     权限类型，不能为空，@see AuthTypeEnum
     * @return 已授权的资源id
     */
    List<String> findAllAuthedResourceId(String orgId, ResourceTypeEnum resourceType, List<AuthTypeEnum> authTypeList);


    /**
     * 获取资源授权给的对象,不支持团队资源类型
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @return 权限结果
     */
    Map<String, Permission> findAllAuthedOrg(String resourceId, ResourceTypeEnum resourceTypeEnum);

    /**
     * 批量获取资源授权给的对象
     * <p>
     * 注：不支持团队资源类型
     *
     * @param resourceIdList   资源id列表
     * @param resourceTypeEnum 资源类型
     * @return map，key-资源id，value-授权的对象数量
     */
    Map<String, Integer> findAllAuthedOrgNumber(List<String> resourceIdList, ResourceTypeEnum resourceTypeEnum);

    /**
     * 保存权限 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgIdList 需要保存权限的对象，成员id、部门id、群组id
     * @param per 权限值
     */
    void savePermission(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList, long per);

    /**
     * 删除权限 当资源类型为team时，资源id则为teamId
     *
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     * @param orgIdList 需要删除的对象，成员id、部门id、群组id
     */
    void deletePermission(String resourceId, ResourceTypeEnum resourceTypeEnum, List<String> orgIdList);

    /**
     * 删除该资源所有已授权的权限
     * 当资源类型为team时，资源id则为teamId，会将该团队的所有团队权限删除。（建议使用上方的重载方法，指定需要取消授权的组织id）
     * @param resourceId 资源id
     * @param resourceTypeEnum 资源类型
     */
    void deletePermission(String resourceId, ResourceTypeEnum resourceTypeEnum);
}
