package com.sinitek.mind.support.team.util;

import com.sinitek.mind.support.team.enumerate.MemberStatusEnum;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/7/4
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class TeamMemberTransformerUtil {

    public static String status2gpt(String val) {
        for (MemberStatusEnum tempEnum : MemberStatusEnum.values()) {
            if (tempEnum.getAppValue().equals(val)) {
                return tempEnum.getGptValue();
            }
        }
        return "";
    }

    public static String status2app(String val) {
        for (MemberStatusEnum tempEnum : MemberStatusEnum.values()) {
            if (tempEnum.getGptValue().equals(val)) {
                return tempEnum.getAppValue();
            }
        }
        return "";
    }

}
