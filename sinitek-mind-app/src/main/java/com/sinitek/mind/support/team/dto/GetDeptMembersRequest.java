package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 获取组织成员请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetDeptMembersRequest {
    
    @Schema(description = "页码，默认1")
    private Integer page;
    
    @Schema(description = "每页数量，默认10")
    private Integer pageSize;
    
    @Schema(description = "组织路径")
    private String orgPath;
} 