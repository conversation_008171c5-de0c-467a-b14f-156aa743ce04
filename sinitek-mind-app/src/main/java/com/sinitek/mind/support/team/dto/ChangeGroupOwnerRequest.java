package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 转让群组所有权请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChangeGroupOwnerRequest {
    
    @Schema(description = "群组ID")
    private String groupId;
    
    @Schema(description = "新所有者的团队成员ID")
    private String tmbId;
} 