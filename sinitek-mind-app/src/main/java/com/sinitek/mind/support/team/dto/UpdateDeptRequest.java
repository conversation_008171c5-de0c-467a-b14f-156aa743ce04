package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 更新组织信息请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateDeptRequest {
    
    @Schema(description = "组织ID（注意：根组织不可修改）")
    private String orgId;
    
    @Schema(description = "组织名称")
    private String name;
    
    @Schema(description = "组织头像URL")
    private String avatar;
    
    @Schema(description = "组织描述")
    private String description;
} 