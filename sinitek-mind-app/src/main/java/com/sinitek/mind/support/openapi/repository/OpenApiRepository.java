package com.sinitek.mind.support.openapi.repository;

import com.sinitek.mind.support.openapi.entity.OpenApi;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.data.mongodb.repository.Update;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * OpenAPI Repository接口
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Repository
public interface OpenApiRepository extends MongoRepository<OpenApi, String> {
    
    /**
     * 根据API密钥查找
     */
    Optional<OpenApi> findByApiKey(String apiKey);
    
    /**
     * 根据团队成员ID查找
     */
    List<OpenApi> findByTmbId(String tmbId);
    
    /**
     * 根据团队成员ID和应用ID查找
     */
    List<OpenApi> findByTmbIdAndAppId(String tmbId, String appId);
    
    /**
     * 根据团队ID查找
     */
    List<OpenApi> findByTeamId(String teamId);
    
    /**
     * 统计用户的API密钥数量
     */
    long countByTmbId(String tmbId);
    
    /**
     * 统计用户在特定应用下的API密钥数量
     */
    long countByTmbIdAndAppId(String tmbId, String appId);
    
    /**
     * 更新使用量统计
     */
    @Query("{ 'apiKey': ?0 }")
    @Update("{ '$inc': { 'usagePoints': ?1 }, '$set': { 'lastUsedTime': ?2 } }")
    void incrementUsagePoints(String apiKey, Long points, Date lastUsedTime);
    
    /**
     * 更新最后使用时间
     */
    @Query("{ '_id': ?0 }")
    @Update("{ '$set': { 'lastUsedTime': ?1 } }")
    void updateLastUsedTime(String id, Date lastUsedTime);

    void deleteByAppId(String id);
}