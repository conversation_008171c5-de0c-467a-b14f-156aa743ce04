package com.sinitek.mind.support.team.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.common.support.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 团队成员管理 Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/7
 * 描述：团队成员管理相关接口
 */
@RestController
@RequestMapping("/mind/api/support/user/team")
@Tag(name = "团队管理")
@RequiredArgsConstructor
public class TeamController {

    /**
     * 临时
     * @return
     * @throws JsonProcessingException
     */
    @GetMapping("/list")
    @Operation(summary = "获取团队成员列表")
    public ApiResponse<?> getTeamMemberList()
        throws JsonProcessingException {
        ObjectMapper objectMapper = new ObjectMapper();
        HashMap map = objectMapper.readValue(json, HashMap.class);
        return ApiResponse.success(new Map[]{map});
    }

    String json = "{\n" +
        "    \"userId\": \"685e0b4574813aa1df06dbd0\",\n" +
        "    \"teamId\": \"685e0b4574813aa1df06dbd7\",\n" +
        "    \"teamName\": \"123\",\n" +
        "    \"memberName\": \"wechat-o5Ljw6pxleIcC5mNpo4zAIb9HcpE\",\n" +
        "    \"avatar\": \"\",\n" +
        "    \"tmbId\": \"685e0b4574813aa1df06dbde\",\n" +
        "    \"role\": \"owner\",\n" +
        "    \"status\": \"active\",\n" +
        "    \"permission\": {\n" +
        "        \"isOwner\": true,\n" +
        "        \"hasManagePer\": true,\n" +
        "        \"hasWritePer\": true,\n" +
        "        \"hasReadPer\": true,\n" +
        "        \"value\": 4294967295,\n" +
        "        \"_permissionList\": {\n" +
        "            \"read\": {\n" +
        "                \"name\": \"common:permission.read\",\n" +
        "                \"description\": \"\",\n" +
        "                \"value\": 4,\n" +
        "                \"checkBoxType\": \"single\"\n" +
        "            },\n" +
        "            \"write\": {\n" +
        "                \"name\": \"common:permission.write\",\n" +
        "                \"description\": \"\",\n" +
        "                \"value\": 2,\n" +
        "                \"checkBoxType\": \"single\"\n" +
        "            },\n" +
        "            \"manage\": {\n" +
        "                \"name\": \"common:permission.manager\",\n" +
        "                \"description\": \"\",\n" +
        "                \"value\": 1,\n" +
        "                \"checkBoxType\": \"single\"\n" +
        "            },\n" +
        "            \"appCreate\": {\n" +
        "                \"checkBoxType\": \"multiple\",\n" +
        "                \"description\": \"\",\n" +
        "                \"name\": \"account_team:permission_appCreate\",\n" +
        "                \"value\": 8\n" +
        "            },\n" +
        "            \"datasetCreate\": {\n" +
        "                \"checkBoxType\": \"multiple\",\n" +
        "                \"description\": \"\",\n" +
        "                \"name\": \"account_team:permission_datasetCreate\",\n" +
        "                \"value\": 16\n" +
        "            },\n" +
        "            \"apikeyCreate\": {\n" +
        "                \"checkBoxType\": \"multiple\",\n" +
        "                \"description\": \"\",\n" +
        "                \"name\": \"account_team:permission_apikeyCreate\",\n" +
        "                \"value\": 32\n" +
        "            }\n" +
        "        },\n" +
        "        \"hasAppCreatePer\": true,\n" +
        "        \"hasDatasetCreatePer\": true,\n" +
        "        \"hasApikeyCreatePer\": true\n" +
        "    }\n" +
        "}";

}
