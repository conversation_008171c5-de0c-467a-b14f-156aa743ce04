package com.sinitek.mind.support.file.enumerate;

import lombok.Getter;

/**
 * 工作线程名称枚举
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
@Getter
public enum WorkerNameEnum {
    READ_FILE("readFile"),
    HTML_STR_2_MD("htmlStr2Md"),
    COUNT_GPT_MESSAGES_TOKENS("countGptMessagesTokens"),
    SYSTEM_PLUGIN_RUN("systemPluginRun"),
    TEXT_2_CHUNKS("text2Chunks");

    private final String value;

    WorkerNameEnum(String value) {
        this.value = value;
    }
}