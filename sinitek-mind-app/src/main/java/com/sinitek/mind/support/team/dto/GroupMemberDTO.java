package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 群组成员DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupMemberDTO {
    
    @Schema(description = "团队成员ID")
    private String tmbId;
    
    @Schema(description = "成员名称")
    private String name;
    
    @Schema(description = "成员头像")
    private String avatar;

    @Schema(description = "成员在群组中的角色")
    private String role;
} 