package com.sinitek.mind.support.team.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.support.team.dto.*;

/**
 * 团队成员服务接口
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public interface ITeamMemberService {
    
    /**
     * 获取团队成员列表
     *
     * @param request 请求参数
     * @return 成员列表响应
     */
    PageResult<TeamMemberDTO> getTeamMemberList(TeamMemberListRequest request);
    
    /**
     * 获取团队成员数量
     *
     * @return 成员数量响应
     */
    TeamMemberCountResponse getTeamMemberCount();
    
    /**
     * 管理员更新成员名称
     *
     * @param request 更新请求
     */
    void updateMemberNameByManager(UpdateMemberNameByManagerRequest request);
    
    /**
     * 成员自己更新名称
     *
     * @param request 更新请求
     */
    void updateMemberName(UpdateMemberNameRequest request);
    
    /**
     * 移除团队成员
     *
     * @param tmbId 成员id
     */
    void deleteMember(String tmbId);
    
    /**
     * 更新邀请结果
     *
     * @param request 更新邀请状态请求
     */
    void updateInviteStatus(UpdateInviteStatusRequest request);
    
    /**
     * 恢复团队成员
     *
     * @param request 恢复请求
     */
    void restoreMember(RestoreMemberRequest request);
    
    /**
     * 离开团队
     */
    void leaveTeam();

    /**
     * 根据tmbId获取团队成员
     */
    TeamMemberDTO getById(String tmbId);
} 