package com.sinitek.mind.support.team.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.support.team.dto.*;
import com.sinitek.mind.support.team.service.IDeptService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 部门/组织管理 Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/7
 * 描述：部门/组织管理相关接口
 */
@RestController
@RequestMapping("/mind/api/support/user/team/org")
@Tag(name = "部门/组织管理")
@RequiredArgsConstructor
public class DeptController {
    
    private final IDeptService orgService;
    
    @PostMapping("/list")
    @Operation(summary = "获取组织列表")
    public ApiResponse<List<DeptDTO>> getOrgList(
            @RequestBody DeptListRequest request) {
        List<DeptDTO> response = orgService.getOrgList(request);
        return ApiResponse.success(response);
    }
    
    @PostMapping("/create")
    @Operation(summary = "创建组织")
    public ApiResponse<Void> createOrg(
            @RequestBody CreateDeptRequest request) {
        orgService.createOrg(request);
        return ApiResponse.success();
    }
    
    @DeleteMapping("/delete")
    @Operation(summary = "删除组织")
    public ApiResponse<Void> deleteOrg(
            @RequestBody DeleteDeptRequest request) {
        orgService.deleteOrg(request);
        return ApiResponse.success();
    }
    
    @PostMapping("/move")
    @Operation(summary = "移动组织")
    public ApiResponse<Void> moveOrg(
            @RequestBody MoveDeptRequest request) {
        orgService.moveOrg(request);
        return ApiResponse.success();
    }
    
    @PostMapping("/update")
    @Operation(summary = "更新组织信息")
    public ApiResponse<Void> updateOrg(
            @RequestBody UpdateDeptRequest request) {
        orgService.updateOrg(request);
        return ApiResponse.success();
    }
    
    @PostMapping("/updateMembers")
    @Operation(summary = "更新组织成员")
    public ApiResponse<Void> updateOrgMembers(
            @RequestBody DeptOrgMembersRequest request) {
        orgService.updateOrgMembers(request);
        return ApiResponse.success();
    }
    
    @GetMapping("/members")
    @Operation(summary = "获取组织成员")
    public ApiResponse<DeptMembersResponse> getOrgMembers(
            @Parameter(description = "页码，默认1") @RequestParam(required = false) Integer page,
            @Parameter(description = "每页数量，默认10") @RequestParam(required = false) Integer pageSize,
            @Parameter(description = "组织路径") @RequestParam(required = false) String orgPath) {
        GetDeptMembersRequest request = GetDeptMembersRequest.builder()
                .page(page)
                .pageSize(pageSize)
                .orgPath(orgPath)
                .build();
        DeptMembersResponse response = orgService.getOrgMembers(request);
        return ApiResponse.success(response);
    }
    
    @DeleteMapping("/deleteMember")
    @Operation(summary = "删除组织成员")
    public ApiResponse<Void> deleteOrgMember(
            @RequestBody DeleteDeptMemberRequest request) {
        orgService.deleteOrgMember(request);
        return ApiResponse.success();
    }
} 
 