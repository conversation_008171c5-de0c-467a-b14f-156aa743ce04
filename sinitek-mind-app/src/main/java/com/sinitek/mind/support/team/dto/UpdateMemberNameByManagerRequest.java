package com.sinitek.mind.support.team.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 管理员更新成员名称请求DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UpdateMemberNameByManagerRequest {
    
    @Schema(description = "团队成员ID")
    private String tmbId;
    
    @Schema(description = "新名称")
    private String name;
} 