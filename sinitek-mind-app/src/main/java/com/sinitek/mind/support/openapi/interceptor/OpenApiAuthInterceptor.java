package com.sinitek.mind.support.openapi.interceptor;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.support.openapi.context.OpenApiContext;
import com.sinitek.mind.support.openapi.dto.OpenApiAuthResultDTO;
import com.sinitek.mind.support.openapi.service.IOpenApiAuthService;
import com.sinitek.mind.support.openapi.util.ApiKeyUtil;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.mind.support.outlink.repository.OutLinkRepository;
import com.sinitek.sirm.common.um.RequestUser;
import com.sinitek.sirm.common.web.RequestContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.util.ContentCachingRequestWrapper;

import java.io.BufferedReader;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * OpenAPI认证拦截器
 * 负责从请求头提取API密钥并进行认证验证
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Component
@Slf4j
public class OpenApiAuthInterceptor implements HandlerInterceptor {

    private static final ObjectMapper MAPPER = new ObjectMapper();

    @Autowired
    private IOpenApiAuthService authService;

    @Autowired
    private OutLinkRepository outLinkRepository;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 确保后续可以正确读取到输入流
        if (!(request instanceof ContentCachingRequestWrapper)) {
            request = new ContentCachingRequestWrapper(request);
        }

        // 尝试从请求参数、请求体中获取shareId，并设置上下文
        String shareId = resolveShareId(request);
        if (StringUtils.isNotBlank(shareId)) {
            // 通过shareId设置上下文
            Optional<OutLink> outLinkOpt = outLinkRepository.findByShareId(shareId);
            if (outLinkOpt.isPresent()) {
                OutLink outLink = outLinkOpt.get();
                OpenApiAuthResultDTO openApiAuthResultDTO = new OpenApiAuthResultDTO();
                openApiAuthResultDTO.setTmbId(outLink.getTmbId());
                openApiAuthResultDTO.setTeamId(outLink.getTeamId());
                openApiAuthResultDTO.setSourceName(outLink.getName());
                openApiAuthResultDTO.setAppId(outLink.getAppId());
                OpenApiContext.setAuth(openApiAuthResultDTO);
                return true;
            }
        }

        String apiKey = ApiKeyUtil.extractApiKey(request);
        if (StringUtils.isNotBlank(apiKey)) {
            // 认证API密钥
            OpenApiAuthResultDTO authResult = authService.authenticateApiKey(apiKey);

            // 将认证信息存储到ThreadLocal上下文中
            OpenApiContext.setAuth(authResult);

            RequestUser requestUser = authService.generateUserInfo(apiKey);
            RequestContext.setCurrentUser(requestUser);
            return true;
        }

        // 没有API密钥，返回401
        response.setStatus(HttpStatus.UNAUTHORIZED.value());
        response.setContentType("application/json");
        response.getWriter().write("{\"error\":\"Missing ApiKey Authorization header\"}");
        return false;
    }


    @Override
    public void afterCompletion(HttpServletRequest request,
                                HttpServletResponse response,
                                Object handler,
                                Exception ex) throws Exception {
        // 清理ThreadLocal，避免内存泄漏
        OpenApiContext.clear();
        RequestContext.end();
    }

    /**
     * 解析shareId
     * @param request
     * @return
     * @throws Exception
     */
    private String resolveShareId(HttpServletRequest request) throws Exception {
        // 参数优先
        String shareId = request.getParameter("shareId");
        if (StringUtils.isNotBlank(shareId)) {
            return shareId;
        }

        // 请求体
        ContentCachingRequestWrapper cached =
                (ContentCachingRequestWrapper) request;
        BufferedReader reader = cached.getReader();
        String body = reader.lines().collect(Collectors.joining());

        // 支持 JSON
        if (body.trim().startsWith("{")) {
            JsonNode node = MAPPER.readTree(body);
            JsonNode idNode = node.get("shareId");
            if (idNode != null && idNode.isTextual()) {
                shareId = idNode.asText();
            }
        }
        // 支持表单
        else {
            for (String pair : body.split("&")) {
                String[] kv = pair.split("=", 2);
                if (kv.length == 2 && "shareId".equals(kv[0])) {
                    shareId = kv[1];
                    break;
                }
            }
        }

        return shareId;
    }
} 