package com.sinitek.mind.support.team.dto;

import com.sinitek.mind.support.permission.dto.PermissionDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 团队成员DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamMemberDTO {
    
    @Schema(description = "用户ID")
    private String userId;
    
    @Schema(description = "团队成员ID")
    private String tmbId;
    
    @Schema(description = "团队ID")
    private String teamId;
    
    @Schema(description = "成员名称")
    private String memberName;
    
    @Schema(description = "成员头像")
    private String avatar;
    
    @Schema(description = "成员角色")
    private String role;
    
    @Schema(description = "成员状态")
    private String status;
    
    @Schema(description = "联系方式")
    private String contact;
    
    @Schema(description = "创建时间")
    private Date createTime;
    
    @Schema(description = "更新时间")
    private Date updateTime;
    
    @Schema(description = "权限对象")
    private PermissionDTO permission;
    
    @Schema(description = "组织信息")
    private List<String> orgs;
} 