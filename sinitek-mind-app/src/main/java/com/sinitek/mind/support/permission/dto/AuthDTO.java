package com.sinitek.mind.support.permission.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 基础权限DTO
 * <p>含有userId、teamId、tmbId从token或openApi中获取，以及isRoot字段，表示是否为团队管理员</p>
 */
@Data
public class AuthDTO {

    @Schema(description = "用户id")
    private String userId;

    @Schema(description = "团队id，对应多租户id")
    private String teamId;

    @Schema(description = "成员id，对应组织表中的orgId")
    private String tmbId;

    @Schema(description = "是否为团队拥有者，根据员工角色进行判断，如果员工的角色有管理员则认为是团队拥有者")
    private Boolean isRoot;

    @Schema(description = "权限信息")
    private PermissionDTO permission;

}
