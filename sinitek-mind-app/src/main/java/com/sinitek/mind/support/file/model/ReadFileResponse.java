package com.sinitek.mind.support.file.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 读取文件响应
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReadFileResponse {
    
    private String rawText;
    
    private String formatText;
    
    private List<ImageType> imageList;
}