package com.sinitek.mind.support.outlink.service.impl;

import com.sinitek.mind.core.app.entity.App;
import com.sinitek.mind.core.app.repository.AppRepository;
import com.sinitek.mind.support.outlink.converter.OutLinkConverter;
import com.sinitek.mind.support.outlink.dto.*;
import com.sinitek.mind.support.outlink.entity.OutLink;
import com.sinitek.mind.support.outlink.repository.OutLinkRepository;
import com.sinitek.mind.support.outlink.service.IAuthOutLinkService;
import com.sinitek.mind.support.outlink.service.IOutLinkService;
import com.sinitek.sirm.common.user.factory.CurrentUserFactory;
import com.sinitek.sirm.framework.exception.BussinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 免登录链接服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OutLinkServiceImpl implements IOutLinkService {

    private final OutLinkRepository outLinkRepository;
    private final AppRepository appRepository;
    private final IAuthOutLinkService authOutLinkService;

    @Override
    public String createOutLink(CreateOutLinkRequest request) {
        try {
            log.info("开始创建免登录链接，应用ID: {}, 类型: {}", request.getAppId(), request.getType());
            
            // 1. 验证应用是否存在
            Optional<App> appOpt = appRepository.findById(request.getAppId());
            if (appOpt.isEmpty()) {
                throw new BussinessException("应用不存在");
            }
            
            // 2. 生成24位随机shareId
            String shareId = generateShareId();
            
            // 3. 创建OutLink对象
            OutLink outLink = new OutLink();
            outLink.setShareId(shareId);
            outLink.setTeamId(CurrentUserFactory.getTenantId());
            outLink.setTmbId(CurrentUserFactory.getOrgId());
            outLink.setAppId(request.getAppId());
            outLink.setType(request.getType());
            outLink.setName(request.getName());
            outLink.setResponseDetail(request.getResponseDetail() != null ? request.getResponseDetail() : false);
            outLink.setShowNodeStatus(request.getShowNodeStatus() != null ? request.getShowNodeStatus() : true);
            outLink.setShowRawSource(request.getShowRawSource());
            outLink.setImmediateResponse(request.getImmediateResponse());
            outLink.setDefaultResponse(request.getDefaultResponse());
            outLink.setLimit(request.getLimit());
            outLink.setApp(request.getApp());
            outLink.setUsagePoints(0);
            outLink.setLastTime(new Date());
            
            // 4. 保存到数据库
            outLinkRepository.save(outLink);
            
            log.info("免登录链接创建成功，shareId: {}", shareId);
            return shareId;
            
        } catch (Exception e) {
            log.error("创建免登录链接失败", e);
            throw new BussinessException("创建免登录链接失败: " + e.getMessage());
        }
    }

    @Override
    public List<OutLinkResponse> getOutLinkList(String appId, String type) {
        try {
            log.info("查询免登录链接列表，应用ID: {}, 类型: {}", appId, type);
            
            // 1. 验证应用是否存在
            Optional<App> appOpt = appRepository.findById(appId);
            if (appOpt.isEmpty()) {
                throw new BussinessException("应用不存在");
            }
            
            // 2. 查询免登录链接列表
            List<OutLink> outLinks = outLinkRepository.findByAppIdAndType(appId, type);
            
            // 3. 转换为DTO
            List<OutLinkResponse> responseList = OutLinkConverter.toResponseList(outLinks);
            
            log.info("查询到 {} 个免登录链接", outLinks.size());
            return responseList;
            
        } catch (Exception e) {
            log.error("查询免登录链接列表失败", e);
            throw new BussinessException("查询免登录链接列表失败: " + e.getMessage());
        }
    }

    @Override
    public void updateOutLink(UpdateOutLinkRequest request) {
        try {
            log.info("更新免登录链接，ID: {}", request.get_id());
            
            // 1. 查找要更新的链接
            Optional<OutLink> outLinkOpt = outLinkRepository.findById(request.get_id());
            if (outLinkOpt.isEmpty()) {
                throw new BussinessException("免登录链接不存在");
            }
            OutLink outLink = outLinkOpt.get();

            // 2. 更新字段
            outLink.setName(request.getName());
            if (request.getResponseDetail() != null) {
                outLink.setResponseDetail(request.getResponseDetail());
            }
            if (request.getShowNodeStatus() != null) {
                outLink.setShowNodeStatus(request.getShowNodeStatus());
            }
            if (request.getShowRawSource() != null) {
                outLink.setShowRawSource(request.getShowRawSource());
            }
            if (request.getImmediateResponse() != null) {
                outLink.setImmediateResponse(request.getImmediateResponse());
            }
            if (request.getDefaultResponse() != null) {
                outLink.setDefaultResponse(request.getDefaultResponse());
            }
            if (request.getLimit() != null) {
                outLink.setLimit(request.getLimit());
            }
            if (request.getApp() != null) {
                outLink.setApp(request.getApp());
            }
            
            // 更新最后修改时间
            outLink.setLastTime(new Date());
            
            // 3. 保存更新
            outLinkRepository.save(outLink);
            
            log.info("免登录链接更新成功");
            
        } catch (Exception e) {
            log.error("更新免登录链接失败", e);
            throw new BussinessException("更新免登录链接失败: " + e.getMessage());
        }
    }

    @Override
    public void deleteOutLink(String id) {
        try {
            log.info("删除免登录链接，ID: {}", id);
            
            // 1. 查找要删除的链接
            Optional<OutLink> outLinkOpt = outLinkRepository.findById(id);
            if (outLinkOpt.isEmpty()) {
                throw new BussinessException("免登录链接不存在");
            }
            
            // 2. 删除链接
            outLinkRepository.deleteById(id);
            
            log.info("免登录链接删除成功");
            
        } catch (Exception e) {
            log.error("删除免登录链接失败", e);
            throw new BussinessException("删除免登录链接失败: " + e.getMessage());
        }
    }

    @Override
    public ChatInitResponse initChat(String shareId, String outLinkUid, String chatId) {
        try {
            log.info("初始化免登录聊天，shareId: {}, outLinkUid: {}, chatId: {}", shareId, outLinkUid, chatId);
            
            // 1. 根据shareId查找免登录链接
            Optional<OutLink> outLinkOpt = outLinkRepository.findByShareId(shareId);
            if (outLinkOpt.isEmpty()) {
                throw new BussinessException("分享链接不存在或已过期");
            }
            OutLink outLink = outLinkOpt.get();

            // TODO 先进行身份验证，如果该外链配置了hookUrl
            outLinkUid = authOutLinkService.authOutLinkChatInit(shareId, outLinkUid);

            // 2. 检查链接是否过期
            if (outLink.getLimit() != null && outLink.getLimit().getExpiredTime() != null) {
                if (new Date().after(outLink.getLimit().getExpiredTime())) {
                    throw new BussinessException("分享链接已过期");
                }
            }
            
            // 3. 检查使用次数限制
            if (outLink.getLimit() != null && outLink.getLimit().getMaxUsagePoints() > 0) {
                if (outLink.getUsagePoints() >= outLink.getLimit().getMaxUsagePoints()) {
                    throw new BussinessException("分享链接使用次数已达上限");
                }
            }
            
            // 4. 获取关联的应用信息
            Optional<App> appOpt = appRepository.findById(outLink.getAppId());
            if (appOpt.isEmpty()) {
                throw new BussinessException("关联的应用不存在");
            }
            App app = appOpt.get();
            
            // 5. 生成或使用已有的chatId
            String finalChatId = chatId;
            if (!StringUtils.hasText(chatId)) {
                finalChatId = generateChatId();
            }
            
            // 6. 构建响应
            ChatInitResponse response = new ChatInitResponse();
            response.setChatId(finalChatId);
            response.setAppId(app.getId());
            response.setTitle(app.getName());
            response.setUserAvatar(null); // 外链用户没有头像
            response.setVariables(null); // 可以根据需要设置变量
            response.setApp(app);
            response.setResponseDetail(outLink.getResponseDetail());
            response.setShowNodeStatus(outLink.getShowNodeStatus());
            response.setShowRawSource(outLink.getShowRawSource());
            
            log.info("免登录聊天初始化成功，chatId: {}", finalChatId);
            return response;
            
        } catch (Exception e) {
            log.error("初始化免登录聊天失败", e);
            throw new BussinessException("初始化免登录聊天失败: " + e.getMessage());
        }
    }
    
    /**
     * 生成24位随机shareId
     */
    private String generateShareId() {
        return UUID.randomUUID().toString().replace("-", "").substring(0, 24);
    }
    
    @Override
    public AppInfoDTO getAppByShareId(String shareId) {
        try {
            log.info("根据shareId获取应用信息，shareId: {}", shareId);
            
            // 1. 根据shareId查找免登录链接
            Optional<OutLink> outLinkOpt = outLinkRepository.findByShareId(shareId);
            if (outLinkOpt.isEmpty()) {
                throw new BussinessException("分享链接不存在或已过期");
            }
            OutLink outLink = outLinkOpt.get();
            
            // 2. 检查链接是否过期
            if (outLink.getLimit() != null && outLink.getLimit().getExpiredTime() != null) {
                if (new Date().after(outLink.getLimit().getExpiredTime())) {
                    throw new BussinessException("分享链接已过期");
                }
            }
            
            // 3. 获取关联的应用信息
            Optional<App> appOpt = appRepository.findById(outLink.getAppId());
            if (appOpt.isEmpty()) {
                throw new BussinessException("关联的应用不存在");
            }
            
            App app = appOpt.get();
            
            // 4. 构建AppInfoDTO
            AppInfoDTO appInfoDTO = new AppInfoDTO();
            appInfoDTO.setAppId(app.getId());
            appInfoDTO.setShowRawSource(outLink.getShowRawSource() != null ? outLink.getShowRawSource() : false);
            appInfoDTO.setResponseDetail(outLink.getResponseDetail());
            appInfoDTO.setShowNodeStatus(outLink.getShowNodeStatus());
            
            // 5. 构建关联应用信息
            AppInfoDTO.AssociatedAppDTO associatedApp = new AppInfoDTO.AssociatedAppDTO();
            associatedApp.setName(app.getName());
            associatedApp.setAvatar(app.getAvatar());
            associatedApp.setIntro(app.getIntro());
            appInfoDTO.setAssociatedApp(associatedApp);
            
            log.info("根据shareId获取应用信息成功，应用ID: {}, 应用名称: {}", app.getId(), app.getName());
            return appInfoDTO;
            
        } catch (Exception e) {
            log.error("根据shareId获取应用信息失败", e);
            throw new BussinessException("获取应用信息失败: " + e.getMessage());
        }
    }

    @Override
    public void addOutLinkUsage(String shareId, int totalPoints) {
        // 根据 shareId 查找对应的 OutLink 实体
        Optional<OutLink> outLinkOpt = outLinkRepository.findByShareId(shareId);
        if (outLinkOpt.isPresent()) {
            OutLink outLink = outLinkOpt.get();
            // 获取当前的 usagePoints
            int currentUsagePoints = outLink.getUsagePoints();
            // 计算新的 usagePoints
            int newUsagePoints = currentUsagePoints + totalPoints;
            // 更新 usagePoints 字段
            outLink.setUsagePoints(newUsagePoints);
            // 保存更新后的实体到数据库
            outLinkRepository.save(outLink);
        }
    }

    /**
     * 生成聊天ID
     */
    private String generateChatId() {
        return "chat_" + System.currentTimeMillis() + "_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
} 