package com.sinitek.mind.support.team.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.support.team.dto.*;
import com.sinitek.mind.support.team.service.ITeamMemberService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 团队成员管理 Controller 层
 *
 * <AUTHOR>
 * @date 2025/7/7
 * 描述：团队成员管理相关接口
 */
@RestController
@RequestMapping("/mind/api/support/user/team/member")
@Tag(name = "团队成员管理")
@RequiredArgsConstructor
public class TeamMemberController {
    
    private final ITeamMemberService teamMemberService;
    
    @PostMapping("/list")
    @Operation(summary = "获取团队成员列表")
    public ApiResponse<PageResult<TeamMemberDTO>> getTeamMemberList(
            @RequestBody TeamMemberListRequest request) {
        PageResult<TeamMemberDTO> response = teamMemberService.getTeamMemberList(request);
        return ApiResponse.success(response);
    }
    
    @GetMapping("/count")
    @Operation(summary = "获取团队成员数量")
    public ApiResponse<TeamMemberCountResponse> getTeamMemberCount() {
        TeamMemberCountResponse response = teamMemberService.getTeamMemberCount();
        return ApiResponse.success(response);
    }
    
    @PostMapping("/updateNameByManager")
    @Operation(summary = "管理员更新成员名称")
    public ApiResponse<Void> updateMemberNameByManager(
            @RequestBody UpdateMemberNameByManagerRequest request) {
        teamMemberService.updateMemberNameByManager(request);
        return ApiResponse.success();
    }
    
    @PostMapping("/updateName")
    @Operation(summary = "成员自己更新名称")
    public ApiResponse<Void> updateMemberName(
            @RequestBody UpdateMemberNameRequest request) {
        teamMemberService.updateMemberName(request);
        return ApiResponse.success();
    }
    
    @DeleteMapping("/delete")
    @Operation(summary = "移除团队成员")
    public ApiResponse<Void> deleteMember(
            @RequestParam String tmbId) {
        teamMemberService.deleteMember(tmbId);
        return ApiResponse.success();
    }
    
    @PostMapping("/updateInvite")
    @Operation(summary = "更新邀请结果")
    public ApiResponse<Void> updateInviteStatus(
            @RequestBody UpdateInviteStatusRequest request) {
        teamMemberService.updateInviteStatus(request);
        return ApiResponse.success();
    }
    
    @PostMapping("/restore")
    @Operation(summary = "恢复团队成员")
    public ApiResponse<Void> restoreMember(
            @RequestBody RestoreMemberRequest request) {
        teamMemberService.restoreMember(request);
        return ApiResponse.success();
    }
    
    @DeleteMapping("/leave")
    @Operation(summary = "离开团队")
    public ApiResponse<Void> leaveTeam() {
        teamMemberService.leaveTeam();
        return ApiResponse.success();
    }
} 