package com.sinitek.mind.support.operationlog.dto;

import com.sinitek.mind.common.support.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * 审计日志DTO
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GetAuditListRequest extends PageParam {

    @Schema(description = "操作人员")
    private List<String> tmbIds;

    @Schema(description = "事件")
    private List<String> events;
}