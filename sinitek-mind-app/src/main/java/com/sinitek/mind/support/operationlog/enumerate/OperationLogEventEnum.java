package com.sinitek.mind.support.operationlog.enumerate;

import lombok.Getter;

@Getter
public enum OperationLogEventEnum {
    // Team
    LOGIN("LOGIN", 1001),
    CREATE_INVITATION_LINK("CREATE_INVITATION_LINK", 1002),
    JOIN_TEAM("JOIN_TEAM", 1003),
    CHANGE_MEMBER_NAME("CHANGE_MEMBER_NAME", 1004),
    KICK_OUT_TEAM("KICK_OUT_TEAM", 1005),
    RECOVER_TEAM_MEMBER("RECOVER_TEAM_MEMBER", 1006),
    CREATE_DEPARTMENT("CREATE_DEPARTMENT", 1007),
    <PERSON><PERSON><PERSON>_DEPARTMENT("CHANGE_DEPARTMENT", 1008),
    DELETE_DEPARTMENT("DELETE_DEPARTMENT", 1009),
    RELOCATE_DEPARTMENT("RELOCATE_DEPARTMENT", 1010),
    CREATE_GROUP("CREATE_GROUP", 1011),
    DELETE_GROUP("DELETE_GROUP", 1012),
    ASSIGN_PERMISSION("ASSIGN_PERMISSION", 1013),
    // APP
    CREATE_APP("CREATE_APP", 1014),
    UPDATE_APP_INFO("UPDATE_APP_INFO", 1015),
    MOVE_APP("MOVE_APP", 1016),
    DELETE_APP("DELETE_APP", 1017),
    UPDATE_APP_COLLABORATOR("UPDATE_APP_COLLABORATOR", 1018),
    DELETE_APP_COLLABORATOR("DELETE_APP_COLLABORATOR", 1019),
    TRANSFER_APP_OWNERSHIP("TRANSFER_APP_OWNERSHIP", 1020),
    CREATE_APP_COPY("CREATE_APP_COPY", 1021),
    CREATE_APP_FOLDER("CREATE_APP_FOLDER", 1022),
    UPDATE_PUBLISH_APP("UPDATE_PUBLISH_APP", 1023),
    CREATE_APP_PUBLISH_CHANNEL("CREATE_APP_PUBLISH_CHANNEL", 1024),
    UPDATE_APP_PUBLISH_CHANNEL("UPDATE_APP_PUBLISH_CHANNEL", 1025),
    DELETE_APP_PUBLISH_CHANNEL("DELETE_APP_PUBLISH_CHANNEL", 1026),
    EXPORT_APP_CHAT_LOG("EXPORT_APP_CHAT_LOG", 1027),
    // Dataset
    CREATE_DATASET("CREATE_DATASET", 1028),
    UPDATE_DATASET("UPDATE_DATASET", 1029),
    DELETE_DATASET("DELETE_DATASET", 1030),
    MOVE_DATASET("MOVE_DATASET", 1031),
    UPDATE_DATASET_COLLABORATOR("UPDATE_DATASET_COLLABORATOR", 1032),
    DELETE_DATASET_COLLABORATOR("DELETE_DATASET_COLLABORATOR", 1033),
    TRANSFER_DATASET_OWNERSHIP("TRANSFER_DATASET_OWNERSHIP", 1034),
    EXPORT_DATASET("EXPORT_DATASET", 1035),
    CREATE_DATASET_FOLDER("CREATE_DATASET_FOLDER", 1036),
    // Collection
    CREATE_COLLECTION("CREATE_COLLECTION", 1037),
    UPDATE_COLLECTION("UPDATE_COLLECTION", 1038),
    DELETE_COLLECTION("DELETE_COLLECTION", 1039),
    RETRAIN_COLLECTION("RETRAIN_COLLECTION", 1040),
    // Data
    CREATE_DATA("CREATE_DATA", 1041),
    UPDATE_DATA("UPDATE_DATA", 1042),
    DELETE_DATA("DELETE_DATA", 1043),
    // SearchTest
    SEARCH_TEST("SEARCH_TEST", 1044),
    // Account
    CHANGE_PASSWORD("CHANGE_PASSWORD", 1045),
    CHANGE_NOTIFICATION_SETTINGS("CHANGE_NOTIFICATION_SETTINGS", 1046),
    CHANGE_MEMBER_NAME_ACCOUNT("CHANGE_MEMBER_NAME_ACCOUNT", 1047),
    PURCHASE_PLAN("PURCHASE_PLAN", 1048),
    EXPORT_BILL_RECORDS("EXPORT_BILL_RECORDS", 1049),
    CREATE_INVOICE("CREATE_INVOICE", 1050),
    SET_INVOICE_HEADER("SET_INVOICE_HEADER", 1051),
    CREATE_API_KEY("CREATE_API_KEY", 1052),
    UPDATE_API_KEY("UPDATE_API_KEY", 1053),
    DELETE_API_KEY("DELETE_API_KEY", 1054);

    private final String name;
    private final int value;

    OperationLogEventEnum(String name, Integer value) {
        this.name = name;
        this.value = value;
    }

}