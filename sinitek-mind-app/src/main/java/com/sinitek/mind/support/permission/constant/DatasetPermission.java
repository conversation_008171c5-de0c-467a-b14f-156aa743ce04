package com.sinitek.mind.support.permission.constant;

import com.sinitek.mind.support.permission.model.PermissionConstructorProps;

/**
 * 数据集权限类
 * 对应TypeScript中的DatasetPermission类
 * 继承基础权限类，使用数据集特定的权限配置
 */
public class DatasetPermission extends Permission {

    /**
     * 默认构造函数
     */
    public DatasetPermission() {
        this(null);
    }

    /**
     * 构造函数
     * @param props 权限构造参数
     */
    public DatasetPermission(PermissionConstructorProps props) {
        // 调用父类构造函数，必须是第一行
        super(buildDatasetProps(props));
    }

    /**
     * 构建数据集权限属性
     * 对应TypeScript中的构造函数逻辑
     * @param props 原始权限构造参数
     * @return 处理后的权限构造参数
     */
    private static PermissionConstructorProps buildDatasetProps(PermissionConstructorProps props) {
        if (props == null) {
            // 如果props为null，创建默认配置
            return PermissionConstructorProps.builder()
                    .permission(PermissionConstant.NULL_PERMISSION)
                    .build();
        } else if (props.getPermission() == null) {
            // 如果props存在但permission为null，设置默认权限
            return PermissionConstructorProps.builder()
                    .permission(PermissionConstant.NULL_PERMISSION)
                    .isOwner(props.getIsOwner())
                    .permissionList(props.getPermissionList())
                    .build();
        }
        
        // 如果props和permission都存在，直接返回
        return props;
    }
}