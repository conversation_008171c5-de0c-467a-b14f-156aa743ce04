package com.sinitek.mind.support.openapi.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * API密钥信息响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-02
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiKeyInfoDTO {
    
    /**
     * API密钥ID
     */
    private String id;
    
    /**
     * 密钥名称
     */
    private String name;
    
    /**
     * API密钥（脱敏显示）
     */
    private String apiKey;
    
    /**
     * 关联的应用ID
     */
    private String appId;
    
    /**
     * 创建时间
     */
    private Date createTime;
    
    /**
     * 最后使用时间
     */
    private Date lastUsedTime;
    
    /**
     * 累计使用点数
     */
    private Long usagePoints;
    
    /**
     * 过期时间
     */
    private Date expiredTime;
    
    /**
     * 最大使用点数
     */
    private Long maxUsagePoints;
    
    /**
     * 脱敏显示API密钥
     */
    public static String maskApiKey(String apiKey) {
        if (apiKey == null || apiKey.length() < 8) {
            return "******";
        }
        return "******" + apiKey.substring(apiKey.length() - 4);
    }
} 