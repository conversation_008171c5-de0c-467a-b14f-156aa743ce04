package com.sinitek.mind.support.outlink.dto;

import com.sinitek.mind.core.app.model.Limit;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * OutLink Response DTO
 */
@Data
@Schema(description = "OutLink Response")
public class OutLinkResponse {

    @Schema(description = "Link Database ID")
    private String _id;

    @Schema(description = "24-character randomly generated shareId")
    private String shareId;

    @Schema(description = "Team ID")
    private String teamId;

    @Schema(description = "TMB ID")
    private String tmbId;

    @Schema(description = "Application ID")
    private String appId;

    @Schema(description = "Publish Channel Type")
    private String type;

    @Schema(description = "Link Name")
    private String name;

    @Schema(description = "Usage Count")
    private int usagePoints;

    @Schema(description = "Last Used Time")
    private Date lastTime;

    @Schema(description = "Show Response Detail")
    private boolean responseDetail;

    @Schema(description = "Show Node Status")
    private boolean showNodeStatus;

    @Schema(description = "Show Raw Source")
    private Boolean showRawSource;

    @Schema(description = "Usage Limit Configuration")
    private Limit limit;

    @Schema(description = "App Configuration")
    private Object app;

    @Schema(description = "Immediate Response Content")
    private String immediateResponse;

    @Schema(description = "Default Response Content")
    private String defaultResponse;
} 