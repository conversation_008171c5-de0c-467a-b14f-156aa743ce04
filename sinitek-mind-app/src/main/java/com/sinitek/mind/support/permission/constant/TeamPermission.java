package com.sinitek.mind.support.permission.constant;

import com.sinitek.mind.support.permission.model.PermissionConstructorProps;
import lombok.Getter;

/**
 * 团队权限类
 * 对应TypeScript中的TeamPermission类
 * 继承基础权限类，添加团队特有的权限检查
 */
@Getter
public class TeamPermission extends Permission {
    // Getters
    private boolean hasAppCreatePer = false;
    private boolean hasDatasetCreatePer = false;
    private boolean hasApikeyCreatePer = false;

    /**
     * 默认构造函数
     */
    public TeamPermission() {
        this(null);
    }

    /**
     * 构造函数
     * @param props 构造参数
     */
    public TeamPermission(PermissionConstructorProps props) {
        // 调用父类构造函数，必须是第一行
        super(buildTeamProps(props));

        // 设置团队权限更新回调
        setUpdatePermissionCallback(this::updateTeamPermissions);
    }

    /**
     * 构建团队权限参数
     * @param props 原始参数
     * @return 构建后的参数
     */
    private static PermissionConstructorProps buildTeamProps(PermissionConstructorProps props) {
        // 设置默认值和权限列表
        PermissionConstructorProps teamProps = props;
        if (teamProps == null) {
            teamProps = PermissionConstructorProps.builder()
                    .permission(PermissionConstant.TEAM_DEFAULT_PERMISSION_VAL)
                    .permissionList(PermissionConstant.TEAM_PERMISSION_LIST)
                    .build();
        } else {
            if (teamProps.getPermission() == null) {
                teamProps.setPermission(PermissionConstant.TEAM_DEFAULT_PERMISSION_VAL);
            }
            // 确保使用团队权限列表
            teamProps.setPermissionList(PermissionConstant.TEAM_PERMISSION_LIST);
        }
        return teamProps;
    }

    /**
     * 更新团队特有权限状态
     */
    private void updateTeamPermissions() {
        this.hasAppCreatePer = checkPer(PermissionConstant.APP_CREATE_PER);
        this.hasDatasetCreatePer = checkPer(PermissionConstant.DATASET_CREATE_PER);
        this.hasApikeyCreatePer = checkPer(PermissionConstant.APIKEY_CREATE_PER);
    }

    @Override
    public String toString() {
        return "TeamPermission{" +
                "value=" + getValue() +
                ", isOwner=" + isOwner() +
                ", hasManagePer=" + hasManagePer() +
                ", hasWritePer=" + hasWritePer() +
                ", hasReadPer=" + hasReadPer() +
                ", hasAppCreatePer=" + hasAppCreatePer +
                ", hasDatasetCreatePer=" + hasDatasetCreatePer +
                ", hasApikeyCreatePer=" + hasApikeyCreatePer +
                ", binary='" + toBinary() + '\'' +
                '}';
    }
}