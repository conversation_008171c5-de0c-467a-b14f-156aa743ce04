package com.sinitek.mind.support.wallet.enumerate;

public enum UsageSourceEnum {
    fastgpt("fastgpt"),
    api("api"),
    shareLink("shareLink"),
    training("training"),
    cronJob("cronJob"),
    share("share"),
    wecom("wecom"),
    feishu("feishu"),
    dingtalk("dingtalk"),
    official_account("official_account"),
    pdfParse("pdfParse"),
    mcp("mcp");

    private final String value;

    UsageSourceEnum(String value) {
        this.value = value;
    }

    /**
     * 获取枚举对应的字符串值
     * @return 枚举对应的字符串值
     */
    public String getValue() {
        return value;
    }
}