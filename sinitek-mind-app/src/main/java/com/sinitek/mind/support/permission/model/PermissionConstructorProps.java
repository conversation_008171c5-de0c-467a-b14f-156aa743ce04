package com.sinitek.mind.support.permission.model;

import com.sinitek.mind.support.permission.dto.PermissionItemDTO;
import lombok.Data;

import java.util.Map;

/**
 * 权限构造参数类
 * 对应TypeScript中的PerConstructPros
 */
@Data
public class PermissionConstructorProps {
    // Getters and Setters
    private Long permission;
    private Boolean isOwner;
    private Map<String, PermissionItemDTO> permissionList;
    private Runnable childUpdatePermissionCallback;

    public PermissionConstructorProps() {}

    public PermissionConstructorProps(Long permission, Boolean isOwner,
                                    Map<String, PermissionItemDTO> permissionList,
                                    Runnable childUpdatePermissionCallback) {
        this.permission = permission;
        this.isOwner = isOwner;
        this.permissionList = permissionList;
        this.childUpdatePermissionCallback = childUpdatePermissionCallback;
    }

    // Builder pattern for easier construction
    public static class Builder {
        private Long permission;
        private Boolean isOwner;
        private Map<String, PermissionItemDTO> permissionList;
        private Runnable childUpdatePermissionCallback;

        public Builder permission(Long permission) {
            this.permission = permission;
            return this;
        }

        public Builder isOwner(Boolean isOwner) {
            this.isOwner = isOwner;
            return this;
        }

        public Builder permissionList(Map<String, PermissionItemDTO> permissionList) {
            this.permissionList = permissionList;
            return this;
        }

        public Builder childUpdatePermissionCallback(Runnable callback) {
            this.childUpdatePermissionCallback = callback;
            return this;
        }

        public PermissionConstructorProps build() {
            return new PermissionConstructorProps(permission, isOwner, permissionList, childUpdatePermissionCallback);
        }
    }

    public static Builder builder() {
        return new Builder();
    }

}