package com.sinitek.mind.support.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.File;

/**
 * 完整文件信息DTO
 *
 * <AUTHOR>
 * date 2025-08-01
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class FullFileDTO extends BaseFileDTO {

    @Schema(description = "文件本体")
    private transient File sourceFile;


}