package com.sinitek.mind.support.permission.service;

import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.dto.AuthMemberDTO;

/**
 * 权限服务类
 * 提供不同资源的鉴权接口
 */
public interface IAuthService {

    /**
     * 基础校验，只是获取基础的字段，权限字段需要在业务上进行设置
     * @return
     */
    AuthDTO authCert();

    /**
     * 校验用户是否拥有指定权限
     * @return
     */
    AuthMemberDTO authUserPer(long per);
}
