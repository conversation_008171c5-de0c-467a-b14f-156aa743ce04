package com.sinitek.mind.support.common.service;

import com.sinitek.mind.support.common.dto.FullFileDTO;
import com.sinitek.mind.support.common.dto.BaseFileDTO;
import com.sinitek.mind.support.common.dto.UploadFileResponse;
import com.sinitek.mind.support.common.dto.UploadImageRequest;
import org.springframework.core.io.Resource;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.util.Map;

/**
 * 文件服务接口
 *
 * <AUTHOR>
 * @date 2025/1/27
 */
public interface IFileService {

    /**
     * 上传图片
     *
     * @param request 上传请求
     * @return 上传结果
     */
    String uploadImage(UploadImageRequest request);
    
    /**
     * 下载图片
     *
     * @param objId 文件ID
     * @return 文件资源
     */
    Resource downloadImage(Long objId);

    /**
     * 通用文件上传 - 传递MultipartFile
     *
     * @param file 文件
     * @param bucketName 桶名称
     * @param metaData 额外数据
     * @return 上传响应
     */
    UploadFileResponse uploadFile(MultipartFile file, String bucketName, Map<String, String> metaData);


    /**
     * 通用文件上传 - 传递File
     *
     * @param file 文件
     * @param fileName 文件名称
     * @param bucketName 桶名称
     * @param metadata 额外数据
     * @return 上传响应
     */
    UploadFileResponse uploadFile(File file, String fileName, String bucketName, Map<String, String> metadata);

    /**
     * 根据桶和ID获取完整文件信息（包含文件本体）
     *
     * @param bucketName 桶名称
     * @param fileId 文件ID
     * @return 完整文件信息
     */
    FullFileDTO getFileById(String bucketName, String fileId);

    /**
     * 根据桶和ID获取基础文件信息（不包含文件本体）
     *
     * @param bucketName 桶名称
     * @param fileId 文件ID
     * @return 基础文件信息
     */
    BaseFileDTO getBaseFileById(String bucketName, String fileId);
}
