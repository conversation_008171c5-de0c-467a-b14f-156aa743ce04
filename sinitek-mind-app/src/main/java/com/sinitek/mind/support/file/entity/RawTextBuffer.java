package com.sinitek.mind.support.file.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.Date;

/**
 * 原始文本缓冲实体
 * 
 * <AUTHOR> Assistant
 * @date 2025-01-23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "buffer_rawtext.files")
public class RawTextBuffer {

    @Id
    private String id;

    @Field("metadata")
    private Metadata metadata;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Metadata {
        private String sourceId;
        private String sourceName;
        private Date expiredTime;
    }
}