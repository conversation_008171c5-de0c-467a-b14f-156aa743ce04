package com.sinitek.mind.support.permission.dto;

import com.sinitek.mind.support.permission.constant.PermissionConstant;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/8
 */
@Getter
@EqualsAndHashCode(callSuper = true)
@Schema(description = "团队权限结果dto")
public class TeamPermissionDTO extends PermissionDTO {

    @Schema(description = "是否有ApiKey创建权限")
    private Boolean hasApikeyCreatePer = false;

    @Schema(description = "是否有App创建权限")
    private Boolean hasAppCreatePer = false;

    @Schema(description = "是否有知识库创建权限")
    private Boolean hasDatasetCreatePer = false;

    @Schema(description = "具体的权限信息")
    private final Map<String, PermissionItemDTO> _permissionList = PermissionConstant.TEAM_PERMISSION_LIST;

    /**
     * 通过设置权限值，自动设置其他字段
     * @param per 权限值
     */
    @Override
    public void setValue(long per) {
        this.value = per;
        if (per == PermissionConstant.OWNER_PERMISSION_VAL) {
            this.isOwner = true;
            this.hasReadPer = true;
            this.hasWritePer = true;
            this.hasManagePer = true;
            this.hasApikeyCreatePer = true;
            this.hasAppCreatePer = true;
            this.hasDatasetCreatePer = true;
        } else {
            this.isOwner = false;
            this.hasReadPer = (per & PermissionConstant.TEAM_READ_PER) == PermissionConstant.TEAM_READ_PER;
            this.hasWritePer = (per & PermissionConstant.TEAM_WRITE_PER) == PermissionConstant.TEAM_WRITE_PER;
            this.hasManagePer = (per & PermissionConstant.TEAM_MANAGE_PER) == PermissionConstant.TEAM_MANAGE_PER;
            this.hasApikeyCreatePer = (per & PermissionConstant.APIKEY_CREATE_PER) == PermissionConstant.APIKEY_CREATE_PER;
            this.hasAppCreatePer = (per & PermissionConstant.APP_CREATE_PER) == PermissionConstant.APP_CREATE_PER;
            this.hasDatasetCreatePer = (per & PermissionConstant.DATASET_CREATE_PER) == PermissionConstant.DATASET_CREATE_PER;
        }

    }

}
