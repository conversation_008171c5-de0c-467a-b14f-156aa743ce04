package com.sinitek.mind.support.account.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户信息更新请求DTO
 *
 * <AUTHOR> Assistant
 * @date 2025/7/7
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserUpdateRequest {
    
    @Schema(description = "用户头像URL")
    private String avatar;
    
    @Schema(description = "时区设置")
    private String timezone;
} 