package com.sinitek.mind.support.openapi.util;

import com.sinitek.mind.support.openapi.dto.ApiKeyInfoDTO;
import com.sinitek.mind.support.openapi.dto.ApiKeySearchResultDTO;
import com.sinitek.mind.support.openapi.entity.OpenApi;

/**
 * API密钥转换工具类
 *
 * <AUTHOR>
 * @date 2025/7/7
 */
public class ApiKeyConverter {
    

    
    /**
     * 转换为API密钥信息DTO
     */
    public static ApiKeyInfoDTO convertToApiKeyInfo(OpenApi openApi) {
        ApiKeyInfoDTO.ApiKeyInfoDTOBuilder builder = ApiKeyInfoDTO.builder()
            .id(openApi.getId())
            .name(openApi.getName())
            .apiKey(ApiKeyInfoDTO.maskApiKey(openApi.getApiKey()))
            .appId(openApi.getAppId())
            .createTime(openApi.getCreateTime())
            .lastUsedTime(openApi.getLastUsedTime())
            .usagePoints(openApi.getUsagePoints());
        
        if (openApi.getLimit() != null) {
            builder.expiredTime(openApi.getLimit().getExpiredTime())
                   .maxUsagePoints(openApi.getLimit().getMaxUsagePoints());
        }
        
        return builder.build();
    }
    
    /**
     * 转换为API密钥搜索结果DTO（FastGPT格式）
     */
    public static ApiKeySearchResultDTO convertToApiKeySearchResult(OpenApi openApi) {
        // 构建限制信息
        ApiKeySearchResultDTO.LimitInfo limitInfo = ApiKeySearchResultDTO.LimitInfo.builder()
            .maxUsagePoints(openApi.getLimit() != null ? 
                openApi.getLimit().getMaxUsagePoints() : -1L)
            .build();
        
        return ApiKeySearchResultDTO.builder()
            .apiKey(ApiKeyInfoDTO.maskApiKey(openApi.getApiKey()))
            .createTime(openApi.getCreateTime())
            .id(openApi.getId())
            ._id(openApi.getId())
            .limit(limitInfo)
            .name(openApi.getName())
            .teamId(openApi.getTeamId())
            .tmbId(openApi.getTmbId())
            .usagePoints(openApi.getUsagePoints() != null ? openApi.getUsagePoints() : 0L)
            .build();
    }
} 