package com.sinitek.mind.support.account.dto;

import com.sinitek.mind.support.permission.dto.TeamPermissionDTO;
import com.sinitek.mind.support.team.dto.TeamMemberDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserDTO {

    @Schema(description = "用户userid")
    private String id;

    @Schema(description = "用户登录名")
    private String username;

    @Schema(description = "时区")
    private String timezone;

    @Schema(description = "联系方式")
    private String contact;

//    promotionRate: UserModelSchema['promotionRate'];

    // 以下是tmb相关内容
    @Schema(description = "FastGpt用户信息")
    private TeamMemberDTO team;

    @Schema(description = "base64编码头像")
    private String avatar;

    private String notificationAccount;

    private TeamPermissionDTO permission;


}
