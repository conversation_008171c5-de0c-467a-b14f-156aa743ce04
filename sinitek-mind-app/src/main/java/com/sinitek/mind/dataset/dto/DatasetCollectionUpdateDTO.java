package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 数据集集合更新DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 * 描述：用于仅更新数据集集合名称的DTO
 */
@Data
@Schema(description = "数据集集合更新DTO")
public class DatasetCollectionUpdateDTO {

    @NotBlank(message = "ID不能为空")
    @Schema(description = "主键ID")
    private String id;

    @NotBlank(message = "名称不能为空")
    @Schema(description = "名称")
    private String name;
}