package com.sinitek.mind.dataset.service;

import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.*;

/**
 * dataset_datas 服务接口
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表Service接口
 */
public interface IDatasetDataService {

    /**
     * 创建数据
     * @param dto 数据DTO
     * @return 主键ID
     */
    String create(DatasetDataDTO dto);

    /**
     * 更新数据
     * @param dto 数据DTO
     */
    void update(DatasetDataDTO dto);

    /**
     * 删除数据
     * @param id 主键ID
     */
    void delete(String id);

    /**
     * 查询详情
     * @param id 主键ID
     * @return 数据DTO
     */
    DatasetDataDTO detail(String id);

    /**
     * 分页查询知识库数据
     * @param param 分页参数
     * @return 分页结果
     */
    PageResult<DatasetDataPageItemDTO> pageList(DatasetDataPageParamDTO param);

    /**
     * 插入单个数据集数据
     * @param param 请求参数
     * @param teamId 团队ID
     * @param tmbId 成员ID
     * @return 插入的数据ID
     */
    String insertData(InsertOneDatasetDataDTO param, String teamId, String tmbId);

    /**
     * 更新数据集数据
     * @param request 更新请求
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 成员ID
     * @throws Exception 异常
     */
    void updateDatasetData(DatasetDataUpdateRequest request, String userId, String teamId, String tmbId) throws Exception;
}