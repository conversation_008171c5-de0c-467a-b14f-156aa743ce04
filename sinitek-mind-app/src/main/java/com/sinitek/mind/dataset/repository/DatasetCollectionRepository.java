package com.sinitek.mind.dataset.repository;

import com.sinitek.mind.dataset.entity.DatasetCollection;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * dataset_collections Repository接口
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合（文件夹/文件）表Repository
 */
@Repository
public interface DatasetCollectionRepository extends MongoRepository<DatasetCollection, String> {
    /**
     * 根据知识库ID查询所有集合
     */
    List<DatasetCollection> findAllByDatasetId(String datasetId);

    /**
     * 根据知识库ID删除所有集合
     */
    void deleteAllByDatasetId(String datasetId);

    /**
     * 根据名称查询集合
     */
    DatasetCollection findFirstByName(String name);
}