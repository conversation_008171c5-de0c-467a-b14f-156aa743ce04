package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.support.permission.dto.CollaboratorUpdateDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * dataset协作者信息更新DTO
 *
 * <AUTHOR>
 * @date 2025/7/9
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "dataset协作者更新对象")
public class DatasetClbUpdateDTO extends CollaboratorUpdateDTO {

    @Schema(description = "datasetId")
    private String datasetId;
}