package com.sinitek.mind.dataset.convert;

import com.sinitek.mind.dataset.dto.DatasetTrainingDTO;
import com.sinitek.mind.dataset.entity.DatasetTraining;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

/**
 * 知识库训练-转换层
 *
 * <AUTHOR>
 * date 2025-07-26
 */
@Component
public class DatasetTrainingConvert {

    /**
     * Entity TO DTO
     * @param entity
     * @return
     */
    public DatasetTrainingDTO convertToDTO(DatasetTraining entity) {
        DatasetTrainingDTO dto = new DatasetTrainingDTO();
        BeanUtils.copyProperties(entity, dto);
        // 将MongoDB的_id映射到DTO的_id字段
        dto.set_id(entity.getId());
        return dto;
    }
}
