package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "数据集训练队列状态DTO")
public class DatasetTrainingQueueDTO {

    @Schema(description = "重建中数量")
    private int rebuildingCount;

    @Schema(description = "训练中数量")
    private int trainingCount;

    public DatasetTrainingQueueDTO(int rebuildingCount, int trainingCount) {
        this.rebuildingCount = rebuildingCount;
        this.trainingCount = trainingCount;
    }
}