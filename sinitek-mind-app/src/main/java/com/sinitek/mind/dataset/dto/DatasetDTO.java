package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.dataset.enumerate.DatasetStatusEnum;
import com.sinitek.mind.model.dto.SystemModelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;
import java.util.Map;

/**
 * 知识库DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Data
public class DatasetDTO {

    @Schema(description = "知识库ID-返回前端")
    private String _id;

    @Schema(description = "父级知识库ID")
    private String parentId;
    
    @Schema(description = "团队ID")
    private String teamId;
    
    @Schema(description = "团队成员ID")
    private String tmbId;
    
    @Schema(description = "知识库类型")
    private String type;
    
    @Schema(description = "知识库头像")
    private String avatar;
    
    @Schema(description = "知识库名称")
    private String name;
    
    @Schema(description = "更新时间")
    private Date updateTime;
    
    @Schema(description = "知识库状态")
    private DatasetStatusEnum status;
    
    @Schema(description = "错误信息")
    private String errorMsg;
    
    @Schema(description = "权限")
    private DatasetPermissionDTO permission;
    
    @Schema(description = "向量模型")
    private SystemModelDTO vectorModel;
    
    @Schema(description = "代理模型")
    private SystemModelDTO agentModel;
    
    @Schema(description = "视觉语言模型")
    private SystemModelDTO vlmModel;
    
    @Schema(description = "知识库简介")
    private String intro;
    
    @Schema(description = "网站配置")
    private DatasetWebsiteConfigDTO websiteConfig;
    
    @Schema(description = "分块设置")
    private DatasetChunkSettingsDTO chunkSettings;
    
    @Schema(description = "是否继承权限")
    private Boolean inheritPermission;
    
    @Schema(description = "API知识库服务配置")
    private Map<String, Object> apiDatasetServer;

    @Schema(description = "所属成员")
    private DatasetSourceMemberDTO sourceMember;
} 