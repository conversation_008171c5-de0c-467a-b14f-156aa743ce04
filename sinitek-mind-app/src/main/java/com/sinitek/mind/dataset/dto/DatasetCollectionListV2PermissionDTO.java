package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.support.permission.dto.PermissionItemDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Map;

/**
 * listV2返回-权限DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合listV2权限结构
 */
@Data
@Schema(description = "知识库集合listV2权限结构")
public class DatasetCollectionListV2PermissionDTO {

    @Schema(description = "是否拥有者")
    private Boolean isOwner;

    @Schema(description = "是否有管理权限")
    private Boolean hasManagePer;

    @Schema(description = "是否有写权限")
    private Boolean hasWritePer;

    @Schema(description = "是否有读权限")
    private Boolean hasReadPer;

    @Schema(description = "权限值")
    private Long value;

    @Schema(description = "权限项列表")
    private Map<String, PermissionItemDTO> _permissionList;
} 