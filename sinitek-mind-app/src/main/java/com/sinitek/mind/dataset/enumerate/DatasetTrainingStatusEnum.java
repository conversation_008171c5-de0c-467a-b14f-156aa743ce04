package com.sinitek.mind.dataset.enumerate;

/**
 * 知识库训练数据状态枚举
 *
 * <AUTHOR>
 * date 2025-07-24
 * 描述：知识库训练数据表status字段枚举
 */
public enum DatasetTrainingStatusEnum {

    /**
     * 待处理
     */
    PENDING("pending"),

    /**
     * 已处理
     */
    PROCESSED("processed"),

    /**
     * 处理失败
     */
    FAILED("failed");

    private final String value;

    DatasetTrainingStatusEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static DatasetTrainingStatusEnum fromValue(String value) {
        for (DatasetTrainingStatusEnum status : DatasetTrainingStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
} 