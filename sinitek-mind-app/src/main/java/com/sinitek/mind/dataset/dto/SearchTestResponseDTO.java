package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.ArrayList;
import java.util.List;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "搜索测试响应DTO")
public class SearchTestResponseDTO {

    @Schema(description = "搜索结果列表")
    private List<SearchResultDTO> list = new ArrayList<>();

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "搜索结果项")
    public static class SearchResultDTO {

        @Schema(description = "文档ID")
        private String id;

        @Schema(description = "更新时间")
        private String updateTime;

        @Schema(description = "问题文本")
        private String q;

        @Schema(description = "回答文本")
        private String a;

        @Schema(description = "分块索引")
        private Integer chunkIndex;

        @Schema(description = "数据集ID")
        private String datasetId;

        @Schema(description = "集合ID")
        private String collectionId;

        @Schema(description = "源文件ID")
        private String sourceId;

        @Schema(description = "源文件名")
        private String sourceName;

        @Schema(description = "评分列表")
        private List<ScoreDTO> score = new ArrayList<>();

        @Schema(description = "token数量")
        private Integer tokens;
    }

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "评分项")
    public static class ScoreDTO {

        @Schema(description = "评分类型")
        private String type;

        @Schema(description = "评分值")
        private Double value;

        @Schema(description = "索引")
        private Integer index;
    }
}