package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.common.support.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 知识库数据分页查询参数DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据分页查询参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "知识库数据分页查询参数")
public class DatasetDataPageParamDTO extends PageParam {

    @Schema(description = "集合ID")
    private String collectionId;

    @Schema(description = "搜索关键词")
    private String searchText;
} 