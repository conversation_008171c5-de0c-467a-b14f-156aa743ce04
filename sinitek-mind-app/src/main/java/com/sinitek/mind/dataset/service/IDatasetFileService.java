package com.sinitek.mind.dataset.service;

import com.mongodb.client.gridfs.model.GridFSFile;

/**
 * 知识库文件Service
 *
 * <AUTHOR>
 * date 2025-07-17
 */
public interface IDatasetFileService {

    /**
     * 根据文件Id获取到文件基本信息 GridFSFile
     * @param fileId
     * @return
     */
    GridFSFile getGridFSFileByFileId(String fileId);

    /**
     * 根据GridFSFile实现下载文件
     * @param gridFSFile
     */
    void downloadGridFSFile(GridFSFile gridFSFile);
} 