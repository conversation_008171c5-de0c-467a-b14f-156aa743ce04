package com.sinitek.mind.dataset.service.impl;

import com.mongodb.client.gridfs.model.GridFSFile;
import com.sinitek.mind.dataset.service.IDatasetFileService;
import com.sinitek.sirm.common.utils.HttpUtils;
import com.sinitek.sirm.common.utils.SpringMvcUtil;
import jakarta.servlet.http.HttpServletResponse;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.gridfs.GridFsTemplate;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;

/**
 * 知识库文件ServiceImpl
 *
 * <AUTHOR>
 * date 2025-07-17
 */
@Service
public class DatasetFileServiceImpl implements IDatasetFileService {

    @Autowired
    private GridFsTemplate datasetGridFsTemplate;

    @Override
    public GridFSFile getGridFSFileByFileId(String fileId) {
        Query query = Query.query(Criteria.where("_id").is(new ObjectId(fileId)));
        return datasetGridFsTemplate.findOne(query);
    }

    @Override
    public void downloadGridFSFile(GridFSFile gridFSFile) {
        String fileName = gridFSFile.getFilename();
        HttpServletResponse response = SpringMvcUtil.getHttpServletResponse();

        try (InputStream inputStream = datasetGridFsTemplate.getResource(gridFSFile).getInputStream()){
            HttpUtils.download(response, fileName, inputStream);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }
}
