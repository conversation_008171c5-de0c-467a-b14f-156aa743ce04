package com.sinitek.mind.dataset.enumerate;

/**
 * 知识库类型枚举
 *
 * <AUTHOR>
 * date 2025-07-02
 */
public enum DatasetTypeEnum {
    
    /**
     * 文件夹类型
     */
    FOLDER("folder"),
    
    /**
     * 普通知识库
     */
    DATASET("dataset"),
    
    /**
     * 网站知识库
     */
    WEBSITE_DATASET("websiteDataset"),
    
    /**
     * 外部文件
     */
    EXTERNAL_FILE("externalFile"),
    
    /**
     * API知识库
     */
    API_DATASET("apiDataset"),
    
    /**
     * 飞书知识库
     */
    FEISHU("feishu"),
    
    /**
     * 语雀知识库
     */
    YUQUE("yuque");
    
    private final String value;
    
    DatasetTypeEnum(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static DatasetTypeEnum fromValue(String value) {
        for (DatasetTypeEnum type : DatasetTypeEnum.values()) {
            if (type.getValue().equals(value)) {
                return type;
            }
        }
        return null;
    }
} 