package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.util.List;

/**
 * 文件预览分块响应DTO
 *
 * <AUTHOR>
 * date 2025/07/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = false)
@Schema(description = "文件预览分块响应DTO")
public class PreviewChunksResponseDTO {
    @Schema(description = "分块列表")
    private List<ChunkDTO> chunks;

    @Schema(description = "总数量")
    private Integer total;

    /**
     * 分块数据DTO
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(callSuper = false)
    @Schema(description = "分块数据DTO")
    public static class ChunkDTO {
        @Schema(description = "问题内容")
        private String q;

        @Schema(description = "回答内容")
        private String a;

        @Schema(description = "索引列表")
        private List<Object> indexes;
    }
}