package com.sinitek.mind.dataset.enumerate;

/**
 * 知识库类型枚举
 *
 * <AUTHOR>
 * date 2025-07-17
 */

public enum DatasetCollectionTypeEnum {

    VIRTUAL("virtual", "空白数据集"),

    FILE("file", "文本数据集"),

    FOLDER("folder", "文件夹");


    private String code;

    private String description;


    DatasetCollectionTypeEnum(String code, String description) {
        this.code = code;
        this.description = description;
    }


    public String getCode() {
        return code;
    }


    public String getDescription() {
        return description;
    }
} 