package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.support.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * 知识库Tag控制器
 *
 * <AUTHOR>
 * date 2025-07-30
 */
@RestController
@RequestMapping("/mind/api/core/dataset/tag")
@Tag(name = "知识库标签管理")
public class DatasetTagController {

    @Operation(description = "标签列表")
    @GetMapping("/getAllTags")
    public ApiResponse<List<String>> getAllTags(@RequestParam String datasetId) {
        return ApiResponse.success(new ArrayList<>());
    }
}
