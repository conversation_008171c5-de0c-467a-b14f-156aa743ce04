package com.sinitek.mind.dataset.core.reader.impl;

import com.sinitek.mind.dataset.core.reader.IVectorFileReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.pdf.PagePdfDocumentReader;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * PDF 文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Slf4j
@Service
public class PdfVectorFileReader implements IVectorFileReader {

    @Override
    public Set<String> supportFileType() {
        Set<String> fileTypeSet = new HashSet<>();
        fileTypeSet.add("pdf");
        return fileTypeSet;
    }

    @Override
    public List<Document> readFile(File sourceFile) {
        Resource resource = new FileSystemResource(sourceFile);
        PagePdfDocumentReader pdfReader = new PagePdfDocumentReader(resource);
        List<Document> documents = pdfReader.get();
        log.info("PDF文件解析结果: {}", documents);
        return documents;
    }
}