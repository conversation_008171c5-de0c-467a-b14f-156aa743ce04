package com.sinitek.mind.dataset.core.vector;

import com.sinitek.mind.dataset.service.IDatasetService;
import com.sinitek.mind.model.core.embedding.EmbeddingModelFactory;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 知识库向量存储
 *
 * <AUTHOR>
 * date 2025-07-22
 */
@Component
public class DatasetVectorStoreFactoryImpl implements IDatasetVectorStoreFactory {

    @Autowired
    private IDatasetService datasetService;

    @Autowired
    private EmbeddingModelFactory embeddingModelFactory;

    @Autowired
    private IDatasetVectorStore datasetVectorStoreImpl; // 实际的向量存储实现

    // vectorModel -> VectorStore 缓存
    private final Map<String, VectorStore> vectorStoreCache = new ConcurrentHashMap<>();
    // databaseId -> vectorModel 缓存
    private final Map<String, String> vectorModelCache = new ConcurrentHashMap<>();

    /**
     * 通过databaseId获取VectorStore
     */
    public VectorStore getVectorStoreByDatabaseId(String databaseId) {
        String vectorModel = getVectorModelByDatabaseId(databaseId);
        return getVectorStoreByVectorModel(vectorModel);
    }

    /**
     * 通过vectorModel获取VectorStore
     */
    public VectorStore getVectorStoreByVectorModel(String vectorModel) {
        return vectorStoreCache.computeIfAbsent(vectorModel, vm -> {
            EmbeddingModel embeddingModel = embeddingModelFactory.embeddingModel(vm);
            return datasetVectorStoreImpl.buildVectorStore(embeddingModel);
        });
    }

    /**
     * 通过databaseId获取vectorModel
     */
    public String getVectorModelByDatabaseId(String databaseId) {
        return vectorModelCache.computeIfAbsent(databaseId, id -> datasetService.getVectorModelByDatasetId(id));
    }
}
