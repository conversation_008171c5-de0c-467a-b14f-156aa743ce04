package com.sinitek.mind.dataset.controller;

import com.sinitek.mind.common.support.ApiResponse;
import com.sinitek.mind.common.support.PageResult;
import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.service.IDatasetCollectionService;
import com.sinitek.mind.dataset.service.IDatasetDataService;
import com.sinitek.mind.support.permission.dto.AuthDTO;
import com.sinitek.mind.support.permission.service.IAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * dataset_datas 控制器
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表Controller
 */
@RestController
@RequestMapping("/mind/api/core/dataset/data")
@Tag(name = "知识库数据管理")
public class DatasetDataController {

    @Autowired
    private IDatasetDataService service;

    @Autowired
    private IAuthService authService;

    @Autowired
    private IDatasetCollectionService collectionService;

    @PostMapping("/create")
    @Operation(summary = "创建数据")
    public ApiResponse<String> create(@RequestBody DatasetDataDTO dto) {
        String id = service.create(dto);
        return ApiResponse.success(id);
    }

    @PostMapping("/update")
    @Operation(summary = "更新数据")
    public ApiResponse<Void> update(@RequestBody DatasetDataUpdateRequest request) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String userId = authInfo.getUserId();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();
        service.updateDatasetData(request, userId, teamId, tmbId);
        return ApiResponse.success();
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除数据")
    public ApiResponse<Void> delete(@Parameter(description = "主键ID") @RequestParam String id) {
        service.delete(id);
        return ApiResponse.success();
    }

    @GetMapping("/detail")
    @Operation(summary = "数据详情")
    public ApiResponse<DatasetDataDTO> detail(@Parameter(description = "主键ID") @RequestParam String id) {
        DatasetDataDTO dto = service.detail(id);
        if (dto != null) {
            AuthDTO authInfo = authService.authCert();
            dto.setIsOwner(dto.getTmbId().equals(authInfo.getTmbId()));
            String sourceName = collectionService.getNameById(dto.getCollectionId());
            if (StringUtils.isNotBlank(sourceName)) {
                dto.setSourceName(sourceName);
            }
        }
        return ApiResponse.success(dto);
    }

    @PostMapping("/v2/list")
    @Operation(summary = "分页获取集合数据列表")
    public ApiResponse<PageResult<DatasetDataPageItemDTO>> pageList(@RequestBody DatasetDataPageParamDTO param) {
        return ApiResponse.success(service.pageList(param));
    }

    @PostMapping("/insertData")
    @Operation(summary = "插入单个数据集数据")
    public ApiResponse<String> insertData(@RequestBody InsertOneDatasetDataDTO param) throws Exception {
        AuthDTO authInfo = authService.authCert();
        String teamId = authInfo.getTeamId();
        String tmbId = authInfo.getTmbId();
        String insertId = service.insertData(param, teamId, tmbId);
        return ApiResponse.success(insertId);
    }
} 