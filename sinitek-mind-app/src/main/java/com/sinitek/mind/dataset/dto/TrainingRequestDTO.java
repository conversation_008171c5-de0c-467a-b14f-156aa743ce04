package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Data
@Schema(description = "训练数据请求基础参数DTO")
public class TrainingRequestDTO {

    @NotBlank(message = "datasetId不能为空")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "collectionId不能为空")
    @Schema(description = "集合ID")
    private String collectionId;

    @NotBlank(message = "dataId不能为空")
    @Schema(description = "数据ID")
    private String dataId;

    @Schema(description = "问题内容")
    private String q;
}