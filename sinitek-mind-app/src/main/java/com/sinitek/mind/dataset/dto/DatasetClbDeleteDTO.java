package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.support.permission.dto.CollaboratorDeleteDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * dataset协作者信息更新DTO
 *
 * <AUTHOR>
 * @date 2025/7/10
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Schema(description = "dataset协作者删除对象")
public class DatasetClbDeleteDTO extends CollaboratorDeleteDTO {

    @Schema(description = "datasetId")
    private String datasetId;
}