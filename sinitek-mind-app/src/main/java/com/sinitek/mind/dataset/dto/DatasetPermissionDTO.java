package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库权限DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DatasetPermissionDTO {

    @Schema(description = "是否有读取权限")
    private Boolean hasReadPer;
    
    @Schema(description = "是否有写入权限")
    private Boolean hasWritePer;
    
    @Schema(description = "是否有删除权限")
    private Boolean hasDeletePer;
    
    @Schema(description = "是否有管理权限")
    private Boolean hasManagePer;
    
    @Schema(description = "是否为所有者")
    private Boolean isOwner;
    
    /**
     * 从权限值初始化
     * 
     * @param per 权限值
     * @param isOwner 是否为所有者
     */
    public DatasetPermissionDTO(Integer per, Boolean isOwner) {
        this.isOwner = isOwner;
        
        if (per != null) {
            // 读取权限 - 最低位
            this.hasReadPer = (per & 1) > 0;
            // 写入权限 - 第二位
            this.hasWritePer = (per & 2) > 0;
            // 删除权限 - 第三位
            this.hasDeletePer = (per & 4) > 0;
            // 管理权限 - 第四位
            this.hasManagePer = (per & 8) > 0;
        } else {
            this.hasReadPer = false;
            this.hasWritePer = false;
            this.hasDeletePer = false;
            this.hasManagePer = false;
        }
        
        // 所有者拥有所有权限
        if (Boolean.TRUE.equals(isOwner)) {
            this.hasReadPer = true;
            this.hasWritePer = true;
            this.hasDeletePer = true;
            this.hasManagePer = true;
        }
    }
    
    /**
     * 转换为权限值
     * 
     * @return 权限值
     */
    public int toValue() {
        int value = 0;
        
        if (Boolean.TRUE.equals(hasReadPer)) {
            value |= 1;
        }
        if (Boolean.TRUE.equals(hasWritePer)) {
            value |= 2;
        }
        if (Boolean.TRUE.equals(hasDeletePer)) {
            value |= 4;
        }
        if (Boolean.TRUE.equals(hasManagePer)) {
            value |= 8;
        }
        
        return value;
    }
} 