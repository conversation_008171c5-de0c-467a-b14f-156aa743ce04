package com.sinitek.mind.dataset.entity;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.data.mongodb.core.mapping.FieldType;

import java.util.Date;
import java.util.List;

/**
 * dataset_datas 实体类
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表实体
 */
@Data
@Document(collection = "dataset_datas")
public class DatasetData {

    @Id
    @Schema(description = "主键ID")
    private String id;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "团队成员ID")
    private String tmbId;

    @Field(targetType = FieldType.OBJECT_ID)
    @Schema(description = "知识库ID")
    private String datasetId;

    @Field(targetType = FieldType.OBJECT_ID)
    @Schema(description = "集合ID")
    private String collectionId;

    @Schema(description = "问题内容")
    private String q;

    @Schema(description = "答案内容")
    private String a;

    @Schema(description = "索引列表")
    private List<Index> indexes;

    @Schema(description = "分块索引")
    private Integer chunkIndex;

    @Schema(description = "历史记录")
    private List<Object> history;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Data
    public static class Index {
        @Schema(description = "索引类型")
        private String type;

        @Schema(description = "数据ID")
        private String dataId;

        @Schema(description = "文本内容")
        private String text;

        @Schema(description = "索引主键ID")
        private String id;
    }
} 