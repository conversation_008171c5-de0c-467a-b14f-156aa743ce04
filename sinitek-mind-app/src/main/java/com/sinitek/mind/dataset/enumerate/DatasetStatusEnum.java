package com.sinitek.mind.dataset.enumerate;

/**
 * 知识库状态枚举
 *
 * <AUTHOR>
 * date 2025-07-02
 */
public enum DatasetStatusEnum {
    
    /**
     * 活跃状态
     */
    ACTIVE("active"),
    
    /**
     * 同步中
     */
    SYNCING("syncing"),
    
    /**
     * 等待中
     */
    WAITING("waiting"),
    
    /**
     * 错误状态
     */
    ERROR("error");
    
    private final String value;
    
    DatasetStatusEnum(String value) {
        this.value = value;
    }
    
    public String getValue() {
        return value;
    }
    
    public static DatasetStatusEnum fromValue(String value) {
        for (DatasetStatusEnum status : DatasetStatusEnum.values()) {
            if (status.getValue().equals(value)) {
                return status;
            }
        }
        return null;
    }
} 