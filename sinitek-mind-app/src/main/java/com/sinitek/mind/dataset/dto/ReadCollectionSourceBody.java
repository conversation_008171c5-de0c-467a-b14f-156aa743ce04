/**
 * 读取集合源请求体 DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 */

package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "读取集合源请求体")
public class ReadCollectionSourceBody {

    @Schema(description = "集合 ID", required = true)
    private String collectionId;

    @Schema(description = "应用 ID")
    private String appId;

    @Schema(description = "聊天 ID")
    private String chatId;

    @Schema(description = "聊天项数据 ID")
    private String chatItemDataId;

    @Schema(description = "共享 ID")
    private String shareId;

    @Schema(description = "外部链接 UID")
    private String outLinkUid;

    @Schema(description = "团队 ID")
    private String teamId;

    @Schema(description = "团队令牌")
    private String teamToken;
} 