/**
 * 文件ID创建数据集集合参数DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 */

package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * FileID模式创建知识库集合DTO
 *
 * <AUTHOR>
 * date 2025/07/23
 */
@Data
@Schema(description = "FileID模式创建知识库集合DTO")
@EqualsAndHashCode(callSuper = true)
public class FileIdCreateDatasetCollectionParamsDTO extends BaseDatasetCollectionParamsDTO {

    @Schema(description = "自定义PDF解析")
    private Boolean customPdfParse;

    @Schema(description = "文件ID")
    private String fileId;
} 