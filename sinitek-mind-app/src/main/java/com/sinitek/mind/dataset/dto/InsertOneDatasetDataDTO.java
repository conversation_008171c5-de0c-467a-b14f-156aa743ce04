/**
 * 插入单个数据集数据请求DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 */

package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class InsertOneDatasetDataDTO {

    @Schema(description = "集合ID")
    private String collectionId;

    @Schema(description = "问题内容")
    private String q;

    @Schema(description = "答案内容")
    private String a;

    @Schema(description = "索引列表")
    private List<IndexDTO> indexes;

    @Data
    public static class IndexDTO {

        @Schema(description = "文本内容")
        private String text;

        @Schema(description = "默认值")
        private String defaultValue;

        @Schema(description = "默认索引")
        private Boolean defaultIndex;

        @Schema(description = "类型")
        private String type;

        @Schema(description = "索引名称")
        private String indexName;

    }

} 