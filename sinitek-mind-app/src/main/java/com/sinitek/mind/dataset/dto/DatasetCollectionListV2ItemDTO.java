package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * listV2返回-单项DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合listV2单项结构
 */
@Data
@Schema(description = "知识库集合listV2单项结构")
public class DatasetCollectionListV2ItemDTO {

    @Schema(description = "主键ID")
    private String _id;

    @Schema(description = "父级ID")
    private String parentId;

    @Schema(description = "团队成员ID")
    private String tmbId;

    @Schema(description = "类型（folder/file）")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "文件ID")
    private String fileId;

    @Schema(description = "训练类型")
    private String trainingType;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "是否禁用")
    private Boolean forbid;

    @Schema(description = "训练数量")
    private Integer trainingAmount;

    @Schema(description = "数据数量")
    private Integer dataAmount;

    @Schema(description = "权限")
    private DatasetCollectionListV2PermissionDTO permission;
} 