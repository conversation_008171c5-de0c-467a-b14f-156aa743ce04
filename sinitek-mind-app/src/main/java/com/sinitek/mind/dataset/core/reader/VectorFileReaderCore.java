package com.sinitek.mind.dataset.core.reader;

import org.apache.commons.compress.utils.FileNameUtils;
import org.springframework.ai.document.Document;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.List;
import java.util.Map;

/**
 * 向量文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Component
public class VectorFileReaderCore {

    @Autowired
    private List<IVectorFileReader> vectorFileReaderList;

    /**
     * 拆分出最终的List<Document>
     * @param sourceFile
     * @return
     */
    public List<Document> process(File sourceFile) {
        List<Document> documents = this.readFile(sourceFile);
        TokenTextSplitter tokenTextSplitter = tokenTextSplitter();
        List<Document> finalDocumentList = tokenTextSplitter.apply(documents);

        return this.buildMetadata(finalDocumentList);
    }

    /**
     * 创建TokenTextSplitter
     * @return
     */
    public TokenTextSplitter tokenTextSplitter() {
        return new TokenTextSplitter();
    }

    /**
     * 添加Document中的元数据
     * @param documentList
     * @return
     */
    public List<Document> buildMetadata(List<Document> documentList) {
        for (Document document : documentList) {
            Map<String, Object> metadata = document.getMetadata();

            metadata.put("team_id", "685e0b4574813aa1df06dbd7");
            metadata.put("dataset_id", "6874bdcff892b2654b7c0cfd");
            metadata.put("collection_id", "68788531f892b2654b7cce0e");
        }
        return documentList;
    }

    /**
     * 向量文件解析，根据不同的文件类型找到不同的解析逻辑
     * @param sourceFile
     * @return
     */
    public List<Document> readFile(File sourceFile) {
        String name = sourceFile.getName();
        String extension = FileNameUtils.getExtension(name);

        for (IVectorFileReader vectorFileReader : vectorFileReaderList) {
            if (vectorFileReader.supportFileType().contains(extension)) {
                return vectorFileReader.readFile(sourceFile);
            }
        }
        return null;
    }
}
