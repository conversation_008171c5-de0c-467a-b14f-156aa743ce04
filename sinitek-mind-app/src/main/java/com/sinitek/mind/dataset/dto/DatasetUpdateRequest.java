package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库更新请求DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Data
public class DatasetUpdateRequest {
    
    @Schema(description = "知识库ID", required = true)
    private String id;
    
    @Schema(description = "知识库名称")
    private String name;
    
    @Schema(description = "知识库简介")
    private String intro;
    
    @Schema(description = "知识库头像")
    private String avatar;
    
} 