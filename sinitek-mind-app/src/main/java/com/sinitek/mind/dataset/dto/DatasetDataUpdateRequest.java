package com.sinitek.mind.dataset.dto;

/**
 * 数据集数据更新请求DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 */
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
public class DatasetDataUpdateRequest {

    @Schema(description = "数据ID")
    private String dataId;

    @Schema(description = "问题")
    private String q;

    @Schema(description = "答案")
    private String a;

    @Schema(description = "索引列表")
    private List<DatasetDataIndexDTO> indexes;

} 