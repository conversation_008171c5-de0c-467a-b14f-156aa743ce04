package com.sinitek.mind.dataset.core.reader.impl;

import com.sinitek.mind.dataset.core.reader.IVectorFileReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.TextReader;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;


/**
 * TXT 文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Slf4j
@Service
public class TxtVectorFileReader implements IVectorFileReader {

    @Override
    public Set<String> supportFileType() {
        Set<String> fileTypeSet = new HashSet<>();
        fileTypeSet.add("txt");
        return fileTypeSet;
    }

    @Override
    public List<Document> readFile(File sourceFile) {
        Resource resource = new FileSystemResource(sourceFile);
        TextReader textReader = new TextReader(resource);
        List<Document> documents = textReader.get();
        log.info("TXT文件解析结果: {}", documents);
        return documents;
    }
}