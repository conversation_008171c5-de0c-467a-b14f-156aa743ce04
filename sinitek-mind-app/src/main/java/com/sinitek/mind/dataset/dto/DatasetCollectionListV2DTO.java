package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * listV2返回DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合listV2返回结构
 */
@Data
@Schema(description = "知识库集合listV2返回结构")
public class DatasetCollectionListV2DTO {

    @Schema(description = "列表")
    private List<DatasetCollectionListV2ItemDTO> list;

    @Schema(description = "总数")
    private Integer total;
} 