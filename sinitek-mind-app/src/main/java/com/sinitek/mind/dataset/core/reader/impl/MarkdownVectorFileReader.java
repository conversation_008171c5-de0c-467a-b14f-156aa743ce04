package com.sinitek.mind.dataset.core.reader.impl;

import com.sinitek.mind.dataset.core.reader.IVectorFileReader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.markdown.MarkdownDocumentReader;
import org.springframework.ai.reader.markdown.config.MarkdownDocumentReaderConfig;
import org.springframework.core.io.FileSystemResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Markdown 文件解析
 *
 * <AUTHOR>
 * date 2025/07/22
 */
@Slf4j
@Service
public class MarkdownVectorFileReader implements IVectorFileReader {

    @Override
    public Set<String> supportFileType() {
        Set<String> fileTypeSet = new HashSet<>();
        fileTypeSet.add("md");
        return fileTypeSet;
    }

    @Override
    public List<Document> readFile(File sourceFile) {
        Resource resource = new FileSystemResource(sourceFile);

        MarkdownDocumentReaderConfig markdownDocumentReaderConfig = MarkdownDocumentReaderConfig.builder()
                .withHorizontalRuleCreateDocument(true)
                .withIncludeCodeBlock(true)
                .withIncludeBlockquote(true)
                .build();

        MarkdownDocumentReader markdownDocumentReader = new MarkdownDocumentReader(resource, markdownDocumentReaderConfig);
        List<Document> documents = markdownDocumentReader.get();
        log.info("我是MarkdownVectorFileReader的结果: {}", documents);
        return documents;
    }
}
