package com.sinitek.mind.dataset.dto;

/**
 * 数据集数据索引DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 */

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class DatasetDataIndexDTO {

    @Schema(description = "类型")
    private String type;

    @Schema(description = "数据ID")
    private String dataId;

    @Schema(description = "文本")
    private String text;

    @Schema(description = "ID")
    private String _id;

    @Schema(description = "是否折叠")
    private boolean fold;

} 