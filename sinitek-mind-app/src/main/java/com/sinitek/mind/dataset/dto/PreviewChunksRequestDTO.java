package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 文件预览分块请求DTO
 *
 * <AUTHOR>
 * date 2025/07/23
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Schema(description = "文件预览分块请求DTO")
public class PreviewChunksRequestDTO extends BaseDatasetCollectionParamsDTO {

    @NotBlank(message = "数据集ID不能为空")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "文件类型不能为空")
    @Schema(description = "文件类型")
    private String type;

    @NotBlank(message = "文件源ID不能为空")
    @Schema(description = "文件源ID")
    private String sourceId;

    @Schema(description = "是否自定义PDF解析")
    private boolean customPdfParse = false;

    @Schema(description = "网页选择器")
    private String webSelector;

    @Schema(description = "选择器")
    private String selector;

    @NotNull(message = "重叠比例不能为空")
    @Schema(description = "重叠比例")
    private BigDecimal overlapRatio;
}