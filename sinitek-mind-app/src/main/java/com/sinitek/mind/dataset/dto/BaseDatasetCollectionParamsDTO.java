package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库集合基础参数DTO
 *
 * <AUTHOR>
 * date 2025/07/23
 */
@Data
@Schema(description = "知识库集合基础参数DTO")
public class BaseDatasetCollectionParamsDTO {

    @Schema(description = "训练类型")
    private String trainingType;

    @Schema(description = "分块触发类型")
    private String chunkTriggerType;

    @Schema(description = "分块触发最小大小")
    private Integer chunkTriggerMinSize;

    @Schema(description = "数据增强集合名称")
    private Boolean dataEnhanceCollectionName;

    @Schema(description = "图像索引")
    private Boolean imageIndex;

    @Schema(description = "自动索引")
    private Boolean autoIndexes;

    @Schema(description = "分块设置模式")
    private String chunkSettingMode;

    @Schema(description = "分块拆分模式")
    private String chunkSplitMode;

    @Schema(description = "段落分块AI模式")
    private String paragraphChunkAIMode;

    @Schema(description = "段落分块深度")
    private Integer paragraphChunkDeep;

    @Schema(description = "段落分块最小大小")
    private Integer paragraphChunkMinSize;

    @Schema(description = "分块大小")
    private Integer chunkSize;

    @Schema(description = "分块分割符")
    private String chunkSplitter;

    @Schema(description = "索引大小")
    private Integer indexSize;

    @Schema(description = "QA提示词")
    private String qaPrompt;

    @Schema(description = "数据集ID")
    private String datasetId;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "父级ID")
    private String parentId;
}
