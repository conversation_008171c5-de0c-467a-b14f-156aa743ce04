package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 知识库集合分页查询参数DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库集合分页查询参数
 */
@Data
@Schema(description = "知识库集合分页查询参数")
public class DatasetCollectionListV2PageParamDTO {

    @Schema(description = "页码，从1开始")
    private Integer pageNum = 1;

    @Schema(description = "每页数量")
    private Integer pageSize = 20;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "父级ID")
    private String parentId;

    @Schema(description = "搜索关键词")
    private String searchText;

    @Schema(description = "标签过滤")
    private List<String> filterTags;
} 