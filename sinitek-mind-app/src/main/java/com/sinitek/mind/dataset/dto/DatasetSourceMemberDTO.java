package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库所属成员成员DTO
 *
 * <AUTHOR>
 * date 2025-07-15
 */
@Data
@Schema(description = "知识库所属成员成员DTO")
public class DatasetSourceMemberDTO {

    @Schema(description = "名称")
    private String name;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "状态")
    private String status;
}
