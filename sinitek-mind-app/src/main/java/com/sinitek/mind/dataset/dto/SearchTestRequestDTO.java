package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "搜索测试请求DTO")
public class SearchTestRequestDTO {

    @NotBlank(message = "数据集ID不能为空")
    @Schema(description = "数据集ID")
    private String datasetId;

    @NotBlank(message = "搜索文本不能为空")
    @Schema(description = "搜索文本")
    private String text;

    @Schema(description = "搜索模式")
    private String searchMode = "embedding";

    @Schema(description = "嵌入权重")
    private Double embeddingWeight = 0.5;

    @Schema(description = "是否使用重排序")
    private Boolean usingReRank = true;

    @Schema(description = "重排序权重")
    private Double rerankWeight = 0.5;

    @NotNull(message = "限制数量不能为空")
    @Schema(description = "限制数量")
    private Integer limit = 5000;

    @Schema(description = "相似度阈值")
    private Double similarity = 0.0;

    @Schema(description = "是否使用扩展查询")
    private Boolean datasetSearchUsingExtensionQuery = false;

    @Schema(description = "扩展查询模型")
    private String datasetSearchExtensionModel = "Qwen3-32B-AWQ";

    @Schema(description = "扩展查询背景")
    private String datasetSearchExtensionBg = "";
}