package com.sinitek.mind.dataset.repository;

import com.sinitek.mind.dataset.entity.DatasetTraining;
import org.springframework.data.mongodb.repository.MongoRepository;

import java.util.List;

public interface DatasetTrainingRepository extends MongoRepository<DatasetTraining, String> {
    
    /**
     * 根据状态查询训练数据并按创建时间升序排序
     */
    List<DatasetTraining> findByStatusOrderByCreateTimeAsc(String status);
    
    /**
     * 根据状态查询训练数据并按collectionId和创建时间升序排序
     */
    List<DatasetTraining> findByStatusOrderByCollectionIdAscCreateTimeAsc(String status);
    
    /**
     * 根据集合ID删除训练数据
     */
    void deleteByCollectionId(String collectionId);
    
    /**
     * 根据数据集ID和状态查询训练数据
     */
    List<DatasetTraining> findByDatasetIdAndStatus(String datasetId, String status);
}
