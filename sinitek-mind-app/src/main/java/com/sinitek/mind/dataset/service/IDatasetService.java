package com.sinitek.mind.dataset.service;

import com.sinitek.mind.dataset.dto.*;
import com.sinitek.mind.dataset.entity.Dataset;

import java.util.List;

/**
 * 知识库服务接口
 *
 * <AUTHOR>
 * date 2025-07-02
 */
public interface IDatasetService {
    
    /**
     * 创建知识库
     *
     * @param request 创建请求
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 知识库ID
     */
    String createDataset(DatasetCreateRequest request, String userId, String teamId, String tmbId) throws Exception;
    
    /**
     * 更新知识库
     *
     * @param request 更新请求
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     */
    void updateDataset(DatasetUpdateRequest request, String userId, String teamId, String tmbId) throws Exception;
    
    /**
     * 删除知识库
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     */
    void deleteDataset(String datasetId, String userId, String teamId, String tmbId) throws Exception;
    
    /**
     * 获取知识库详情
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 知识库详情
     */
    DatasetDTO getDatasetDetail(String datasetId, String userId, String teamId, String tmbId);
    
    /**
     * 获取知识库列表
     *
     * @param request 请求参数
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 知识库列表
     */
    List<Dataset> getDatasetList(DatasetListRequest request, String userId, String teamId, String tmbId) throws Exception;
    
    /**
     * 获取知识库路径
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     * @return 知识库路径列表
     */
    List<DatasetPathDTO> getDatasetPaths(String datasetId, String userId, String teamId, String tmbId) throws Exception;

    /**
     * 检查用户是否有该知识库权限，基于当前用户
     * @param datasetId
     * @return
     */
    DatasetDTO checkDatasetAuthByCurrentUser(String datasetId);

    /**
     * 通过知识库名称查询知识库ID
     *
     * @param name 知识库名称
     * @return 知识库ID
     * <AUTHOR>
     * date 2025-07-23
     */
    String getDatasetIdByName(String name);

    /**
     * 根据datasetId获取vectorModel
     * @param datasetId 知识库ID
     * @return vectorModel
     */
    String getVectorModelByDatasetId(String datasetId);

    /**
     * 检查知识库权限
     *
     * @param datasetId 知识库ID
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param tmbId 团队成员ID
     */
    void checkDatasetPermission(String datasetId, String userId, String teamId, String tmbId);
}