package com.sinitek.mind.dataset.repository;

import com.sinitek.mind.dataset.entity.DatasetData;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * dataset_datas Repository接口
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据表Repository
 */
@Repository
public interface DatasetDataRepository extends MongoRepository<DatasetData, String> {
    /**
     * 根据集合ID批量删除数据
     */
    void deleteAllByCollectionIdIn(List<String> collectionIds);
} 