package com.sinitek.mind.dataset.dto;

import com.sinitek.mind.support.common.dto.BaseFileDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库集合详情DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 */
@Data
@Schema(description = "知识库集合详情DTO")
public class DatasetCollectionDetailDTO {

    @Schema(description = "ID")
    private String _id;

    @Schema(description = "父ID")
    private String parentId;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "TMB ID")
    private String tmbId;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "类型")
    private String type;

    @Schema(description = "名称")
    private String name;

    @Schema(description = "创建时间")
    private String createTime;

    @Schema(description = "更新时间")
    private String updateTime;

    @Schema(description = "版本")
    private int __v;

    @Schema(description = "知识库详情")
    private DatasetDTO dataset;

    @Schema(description = "索引数量")
    private int indexAmount;

    @Schema(description = "来源名称")
    private String sourceName;

    @Schema(description = "权限")
    private DatasetCollectionListV2PermissionDTO permission;

    @Schema(description = "错误数量")
    private int errorCount;

    @Schema(description = "文件ID（type=file时有值）")
    private String fileId;

    @Schema(description = "集合原文件信息")
    private BaseFileDTO file;

} 