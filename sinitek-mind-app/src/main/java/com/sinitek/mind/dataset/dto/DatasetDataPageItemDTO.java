package com.sinitek.mind.dataset.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 知识库数据分页返回项DTO
 *
 * <AUTHOR>
 * date 2025-07-16
 * 描述：知识库数据分页返回项
 */
@Data
@Schema(description = "知识库数据分页返回项")
public class DatasetDataPageItemDTO {

    @Schema(description = "主键ID")
    private String _id;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "知识库ID")
    private String datasetId;

    @Schema(description = "集合ID")
    private String collectionId;

    @Schema(description = "问题内容")
    private String q;

    @Schema(description = "答案内容")
    private String a;

    @Schema(description = "分块索引")
    private Integer chunkIndex;
} 