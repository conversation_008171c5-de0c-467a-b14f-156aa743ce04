package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 系统环境配置DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Data
@Schema(description = "系统环境配置")
public class SystemEnvDTO {

    @Schema(description = "OpenAPI前缀")
    private String openapiPrefix;

    @Schema(description = "向量处理线程数量")
    private Integer vectorMaxProcess;

    @Schema(description = "问答拆分线程数量")
    private Integer qaMaxProcess;

    @Schema(description = "图片理解模型最大处理进程")
    private Integer vlmMaxProcess;

    @Schema(description = "Token计算线程保持数")
    private Integer tokenWorkers;

    @Schema(description = "向量搜索参数")
    private Integer hnswEfSearch;

    @Schema(description = "向量搜索最大扫描数据量")
    private Integer hnswMaxScanTuples;

    @Schema(description = "OneAPI URL")
    private String oneapiUrl;

    @Schema(description = "聊天API密钥")
    private String chatApiKey;

    @Schema(description = "自定义PDF解析配置")
    private CustomPdfParseDTO customPdfParse;
} 