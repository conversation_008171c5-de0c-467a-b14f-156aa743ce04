package com.sinitek.mind.system.dto;

import com.sinitek.mind.model.dto.SystemModelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 系统初始化数据响应 DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Schema(description = "系统初始化数据响应")
@Data
public class InitDataResponseDTO {

    @Schema(description = "缓冲区ID")
    private String bufferId;

    @Schema(description = "前端配置")
    private FeConfigsDTO feConfigs;

    @Schema(description = "订阅计划")
    private Object subPlans;

    @Schema(description = "系统版本")
    private String systemVersion;

    @Schema(description = "激活模型列表")
    private List<SystemModelDTO> activeModelList;

    @Schema(description = "默认模型")
    private DefaultModelsDTO defaultModels;

    public InitDataResponseDTO() {
    }

    public static InitDataResponseDTO onlyBufferId(String bufferId, String systemVersion) {
        InitDataResponseDTO resp = new InitDataResponseDTO();
        resp.setBufferId(bufferId);
        resp.setSystemVersion(systemVersion);
        return resp;
    }

    public static InitDataResponseDTO full(String bufferId, FeConfigsDTO feConfigs, Object subPlans, String systemVersion, List<SystemModelDTO> activeModelList, DefaultModelsDTO defaultModels) {
        InitDataResponseDTO resp = new InitDataResponseDTO();
        resp.setBufferId(bufferId);
        resp.setFeConfigs(feConfigs);
        resp.setSubPlans(subPlans);
        resp.setSystemVersion(systemVersion);
        resp.setActiveModelList(activeModelList);
        resp.setDefaultModels(defaultModels);
        return resp;
    }

    public static InitDataResponseDTO pricePage(FeConfigsDTO feConfigs, Object subPlans, List<SystemModelDTO> activeModelList) {
        InitDataResponseDTO resp = new InitDataResponseDTO();
        resp.setFeConfigs(feConfigs);
        resp.setSubPlans(subPlans);
        resp.setActiveModelList(activeModelList);
        return resp;
    }

    public static InitDataResponseDTO unAuth(String bufferId, FeConfigsDTO feConfigs) {
        InitDataResponseDTO resp = new InitDataResponseDTO();
        resp.setBufferId(bufferId);
        resp.setFeConfigs(feConfigs);
        return resp;
    }
} 