package com.sinitek.mind.system.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.sinitek.mind.system.config.SystemConfigRepository;
import com.sinitek.mind.system.constants.SystemConfigType;
import com.sinitek.mind.system.dto.FastGPTConfigDTO;
import com.sinitek.mind.system.dto.LicenseDataDTO;
import com.sinitek.mind.system.entity.SystemConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * 系统配置服务
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Service
public class SystemConfigService {

    @Autowired
    private SystemConfigRepository systemConfigRepository;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 从数据库获取FastGPT配置
     *
     * @return FastGPT配置和许可证数据
     */
    public Map<String, Object> getFastGPTConfigFromDB() {
        Map<String, Object> result = new HashMap<>();

        // 获取FastGPT配置
        Optional<SystemConfig> fastgptConfigOpt = systemConfigRepository.findLatestByType(SystemConfigType.FASTGPT.getValue());
        FastGPTConfigDTO fastgptConfig = new FastGPTConfigDTO();
        if (fastgptConfigOpt.isPresent()) {
            try {
                fastgptConfig = objectMapper.convertValue(fastgptConfigOpt.get().getValue(), FastGPTConfigDTO.class);
            } catch (Exception e) {
                // 转换失败，使用默认配置
            }
        }
        result.put("fastgptConfig", fastgptConfig);

        // 获取许可证数据
        Optional<SystemConfig> licenseConfigOpt = systemConfigRepository.findLatestByType(SystemConfigType.LICENSE.getValue());
        if (licenseConfigOpt.isPresent()) {
            try {
                Map<String, Object> licenseValue = objectMapper.convertValue(licenseConfigOpt.get().getValue(), Map.class);
                LicenseDataDTO licenseData = objectMapper.convertValue(licenseValue.get("config"), LicenseDataDTO.class);
                result.put("licenseData", licenseData);

                // 设置systemInitBufferId
                String fastgptConfigTime = fastgptConfigOpt.isPresent() ? 
                    String.valueOf(fastgptConfigOpt.get().getCreateTime().getTime()) : "";
                String licenseConfigTime = String.valueOf(licenseConfigOpt.get().getCreateTime().getTime());
                if (!fastgptConfigTime.isEmpty()) {
                    result.put("systemInitBufferId", fastgptConfigTime + "-" + licenseConfigTime);
                }
            } catch (Exception e) {
                // 转换失败，不设置许可证数据
            }
        }

        return result;
    }

    /**
     * 更新FastGPT配置缓存
     */
    public void updateFastGPTConfigBuffer() {
        Optional<SystemConfig> configOpt = systemConfigRepository.findLatestByType(SystemConfigType.FASTGPT.getValue());
        if (configOpt.isPresent()) {
            SystemConfig config = configOpt.get();
            config.setCreateTime(new java.util.Date());
            systemConfigRepository.save(config);
        }
    }

    /**
     * 重新加载FastGPT配置缓存
     * 
     * @return 缓存ID
     */
    public String reloadFastGPTConfigBuffer() {
        Optional<SystemConfig> configOpt = systemConfigRepository.findLatestByType(SystemConfigType.FASTGPT.getValue());
        if (configOpt.isPresent()) {
            return String.valueOf(configOpt.get().getCreateTime().getTime());
        }
        return null;
    }
} 