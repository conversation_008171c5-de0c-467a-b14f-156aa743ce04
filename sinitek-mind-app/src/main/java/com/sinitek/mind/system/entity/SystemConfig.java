package com.sinitek.mind.system.entity;

import lombok.Data;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * 系统配置实体类
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Data
@Document(collection = "systemConfigs")
public class SystemConfig {

    @Id
    private String id;

    /**
     * 配置类型
     */
    private String type;

    /**
     * 配置值
     */
    private Object value;

    /**
     * 创建时间
     */
    private Date createTime = new Date();
} 