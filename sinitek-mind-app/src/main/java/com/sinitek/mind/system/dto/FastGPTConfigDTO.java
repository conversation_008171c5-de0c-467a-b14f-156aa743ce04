package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * FastGPT配置DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Data
@Schema(description = "FastGPT配置")
public class FastGPTConfigDTO {

    @Schema(description = "前端配置")
    private FeConfigsDTO feConfigs;

    @Schema(description = "系统环境配置")
    private SystemEnvDTO systemEnv;

    @Schema(description = "订阅计划")
    private Object subPlans;
} 