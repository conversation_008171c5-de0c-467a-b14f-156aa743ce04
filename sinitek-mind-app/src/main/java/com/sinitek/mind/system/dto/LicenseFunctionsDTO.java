package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 许可证功能列表DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
@Data
@Schema(description = "功能列表")
public class LicenseFunctionsDTO {

    @Schema(description = "单点登录")
    private Boolean sso;

    @Schema(description = "支付功能")
    private Boolean pay;

    @Schema(description = "自定义模板")
    private Boolean customTemplates;

    @Schema(description = "数据集增强")
    private Boolean datasetEnhance;

    @Schema(description = "批量评估")
    private Boolean batchEval;
} 