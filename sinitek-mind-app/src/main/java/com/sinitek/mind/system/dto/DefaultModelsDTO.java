/**
 * 默认模型 DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
package com.sinitek.mind.system.dto;

import com.sinitek.mind.model.dto.SystemModelDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "默认模型")
public class DefaultModelsDTO {

    @Schema(description = "embedding模型")
    private SystemModelDTO embedding;

    @Schema(description = "llm模型")
    private SystemModelDTO llm;

    @Schema(description = "数据集文本LLM")
    private SystemModelDTO datasetTextLLM;

    @Schema(description = "数据集图片LLM")
    private SystemModelDTO datasetImageLLM;

    @Schema(description = "reRank模型")
    private SystemModelDTO reRank;

} 