/**
 * 前端配置限制 DTO
 *
 * <AUTHOR>
 * date 2025-07-02
 */
package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "限制配置")
public class FeConfigsLimitDTO {

    @Schema(description = "导出数据集限制(分钟)")
    private Integer exportDatasetLimitMinutes;

    @Schema(description = "网站同步限制(分钟)")
    private Integer websiteSyncLimitMinuted;

} 