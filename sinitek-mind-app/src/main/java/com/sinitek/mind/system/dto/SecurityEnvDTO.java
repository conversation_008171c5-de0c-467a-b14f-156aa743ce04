package com.sinitek.mind.system.dto;

import lombok.Data;

/**
 * 安全配置
 *
 * <AUTHOR>
 * @date 2025/7/17
 */
@Data
public class SecurityEnvDTO {
    /**
     * 对话文件过期天数
     */
    private Integer chatFileExpireTime = 7;

    /**
     * 是否启动 IP 限流，部分接口增加了 ip 限流策略，防止非正常请求操作
     */
    private Boolean useIpLimit = false;

    /**
     * 工作流最大运行次数，避免极端的死循环情况
     */
    private Integer workflowMaxRunTimes = 500;

    /**
     * 循环最大运行次数，避免极端的死循环情况
     */
    private Integer workflowMaxLoopTimes = 50;

    /**
     * 是否启用内网 IP 检查
     */
    private Boolean checkInternalIp = false;

    /**
     * 密码错误锁时长，单位：秒
     */
    private Integer passwordLoginLockSeconds;

    /**
     * 密码过期月份，不设置则不会过期
     */
    private Integer passwordExpiredMonth;

    /**
     * 最大登录客户端数量，默认为 10
     */
    private Integer maxLoginSession = 10;

}
