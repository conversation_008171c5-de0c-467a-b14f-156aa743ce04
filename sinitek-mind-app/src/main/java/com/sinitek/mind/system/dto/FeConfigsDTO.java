package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 前端配置 DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 * 描述：前端配置的数据传输对象
 */
@Data
@Schema(description = "前端配置")
public class FeConfigsDTO {

    @Schema(description = "laf环境")
    private String lafEnv;

    @Schema(description = "mcpServer代理地址")
    private String mcpServerProxyEndpoint;

    @Schema(description = "是否显示空聊天")
    private Boolean show_emptyChat;

    @Schema(description = "是否显示git")
    private Boolean show_git;

    @Schema(description = "文档地址")
    private String docUrl;

    @Schema(description = "OpenAPI文档地址")
    private String openAPIDocUrl;

    @Schema(description = "系统插件课程地址")
    private String systemPluginCourseUrl;

    @Schema(description = "应用模板课程地址")
    private String appTemplateCourse;

    @Schema(description = "系统标题")
    private String systemTitle;

    @Schema(description = "联系方式Markdown")
    private String concatMd;

    @Schema(description = "限制配置")
    private FeConfigsLimitDTO limit;

    @Schema(description = "脚本列表")
    private List<Object> scripts;

    @Schema(description = "favicon路径")
    private String favicon;

    @Schema(description = "上传文件最大大小(MB)")
    private Integer uploadFileMaxSize;

    @Schema(description = "是否Plus版本")
    private Boolean isPlus = true;

    @Schema(description = "是否显示aiproxy")
    private Boolean show_aiproxy;

    @Schema(description = "是否显示优惠券")
    private Boolean show_coupon;

    @Schema(description = "是否显示自定义PDF解析")
    private Boolean showCustomPdfParse;

    @Schema(description = "自定义PDF解析价格")
    private Integer customPdfParsePrice;

    @Schema(description = "发布渠道是否显示-飞书")
    private Boolean show_publish_feishu = false;

    @Schema(description = "发布渠道是否显示-钉钉")
    private Boolean show_publish_dingtalk = false;

    @Schema(description = "发布渠道是否显示-微信公众号")
    private Boolean show_publish_offiaccount = false;


}