package com.sinitek.mind.system.support;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.StreamUtils;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;

/**
 * 配置文件读取工具
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Component
public class ConfigFileReader {
    
    private static final Logger logger = LoggerFactory.getLogger(ConfigFileReader.class);

    @Value("${config.json.path:config}")
    private String configJsonPath;

    private final ObjectMapper objectMapper;

    public ConfigFileReader(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 读取配置文件内容
     *
     * @param name 文件名
     * @return 文件内容
     * @throws IOException 读取异常
     */
    public String readConfigData(String name) throws IOException {
        String[] splitName = name.split("\\.");
        String devName = splitName[0] + ".local." + splitName[1];

        boolean isProduction = "prod".equals(System.getProperty("spring.profiles.active"));
        String resourcePath;

        if (!isProduction) {
            // 检查本地文件是否存在
            Resource localResource = new ClassPathResource(configJsonPath + "/" + devName);
            if (localResource.exists()) {
                resourcePath = configJsonPath + "/" + devName;
                logger.info("使用开发环境本地配置文件: {}", resourcePath);
            } else {
                resourcePath = configJsonPath + "/" + name;
                logger.info("使用开发环境标准配置文件: {}", resourcePath);
            }
        } else {
            // 使用生产环境路径
            String envPath = StringUtils.isNotBlank(System.getenv("CONFIG_JSON_PATH")) ? 
                System.getenv("CONFIG_JSON_PATH") : configJsonPath;
            resourcePath = envPath + "/" + name;
            logger.info("使用生产环境配置文件: {}", resourcePath);
        }

        // 尝试从classpath加载资源
        try {
            Resource resource = new ClassPathResource(resourcePath);
            if (!resource.exists()) {
                logger.warn("配置文件不存在: {}, 尝试从其他位置加载", resourcePath);
                // 尝试从根目录加载
                resourcePath = name;
                resource = new ClassPathResource(resourcePath);
            }
            
            if (resource.exists()) {
                try (InputStream inputStream = resource.getInputStream()) {
                    // 使用StreamUtils一次性读取整个流内容，避免流关闭问题
                    return StreamUtils.copyToString(inputStream, StandardCharsets.UTF_8);
                }
            } else {
                throw new IOException("配置文件不存在: " + resourcePath);
            }
        } catch (IOException e) {
            logger.error("读取配置文件失败: {}", resourcePath, e);
            throw new IOException("无法读取配置文件: " + resourcePath, e);
        }
    }

    /**
     * 读取配置文件并解析为指定类型
     *
     * @param name 文件名
     * @param clazz 目标类型
     * @param <T> 类型参数
     * @return 解析后的对象
     * @throws IOException 读取或解析异常
     */
    public <T> T readConfigDataAsObject(String name, Class<T> clazz) throws IOException {
        String content = readConfigData(name);
        return objectMapper.readValue(content, clazz);
    }
} 