package com.sinitek.mind.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 许可证数据DTO
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Data
@Schema(description = "许可证数据")
public class LicenseDataDTO {

    @Schema(description = "开始时间")
    private String startTime;

    @Schema(description = "过期时间")
    private String expiredTime;

    @Schema(description = "公司名称")
    private String company;

    @Schema(description = "描述")
    private String description;

    @Schema(description = "管理端有效域名")
    private List<String> hosts;

    @Schema(description = "最大用户数，不填默认不上限")
    private Integer maxUsers;

    @Schema(description = "最大应用数，不填默认不上限")
    private Integer maxApps;

    @Schema(description = "最大数据集数，不填默认不上限")
    private Integer maxDatasets;

    @Schema(description = "功能列表")
    private LicenseFunctionsDTO functions;
} 