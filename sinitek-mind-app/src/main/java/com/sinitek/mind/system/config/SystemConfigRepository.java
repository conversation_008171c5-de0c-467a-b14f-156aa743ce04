package com.sinitek.mind.system.config;

import com.sinitek.mind.system.entity.SystemConfig;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 系统配置数据库访问接口
 *
 * <AUTHOR>
 * date 2025-07-01
 */
@Repository
public interface SystemConfigRepository extends MongoRepository<SystemConfig, String> {

    /**
     * 根据类型查询最新的系统配置
     *
     * @param type 配置类型
     * @return 系统配置
     */
    @Query(value = "{'type': ?0}", sort = "{'createTime': -1}")
    Optional<SystemConfig> findLatestByType(String type);
} 