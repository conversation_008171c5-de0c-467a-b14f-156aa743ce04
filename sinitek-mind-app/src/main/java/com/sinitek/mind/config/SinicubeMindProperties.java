package com.sinitek.mind.config;

import com.sinitek.mind.system.dto.FeConfigsDTO;
import com.sinitek.mind.system.dto.SecurityEnvDTO;
import com.sinitek.mind.system.dto.SystemEnvDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * mind相关配置
 *
 * <AUTHOR>
 * @date 2025/7/16
 */
@Data
@Component
@ConfigurationProperties(prefix = "sinicube.mind")
public class SinicubeMindProperties {

    private FeConfigsDTO feConfigs;

    private SystemEnvDTO systemEnv;

    private SecurityEnvDTO securityEnv;

    /**
     * 沙盒url
     */
    private String sandboxUrl;
}
