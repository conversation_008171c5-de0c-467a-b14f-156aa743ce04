package com.sinitek.mind.common.util;

import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;

/**
 * MongoDB工具类
 *
 * <AUTHOR>
 * date 2025-07-02
 */
public class MongoDBUtil {
    
    /**
     * 将字符串转换为ObjectId
     * 
     * @param id 字符串ID
     * @return ObjectId或原字符串
     */
    public static ObjectId toObjectId(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        if (ObjectId.isValid(id)) {
            return new ObjectId(id);
        }
        return null;
    }
} 