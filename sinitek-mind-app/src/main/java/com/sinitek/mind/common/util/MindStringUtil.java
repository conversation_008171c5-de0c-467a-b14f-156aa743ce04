/**
 * 字符串工具类
 *
 * <AUTHOR>
 * date 2025-07-17
 */

package com.sinitek.mind.common.util;

import org.apache.commons.lang3.StringUtils;

import java.util.regex.Pattern;

/**
 * mind相关字符串工具类
 *
 * <AUTHOR>
 * date 2025-07-17
 */
public class MindStringUtil {

    /**
     * 简化文本：去除中文间的空格、多余换行和空格
     * @param text 输入文本
     * @return 简化后的文本
     */
    public static String simpleText(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }

        text = text.trim();

        // 去除中文间的空格
        text = Pattern.compile("([\u4e00-\u9fa5])[\s&&[^\n]]+([\u4e00-\u9fa5])").matcher(text).replaceAll("$1$2");

        // 替换\r\n或\r为\n
        text = text.replaceAll("\\r\\n|\\r", "\\n");

        // 替换三个以上连续换行为两个
        text = text.replaceAll("\\n{3,}", "\\n\\n");

        // 替换两个以上连续空格（非换行）为一个空格
        text = text.replaceAll("[\\s&&[^\\n]]{2,}", " ");

        // 替换控制字符为空格
        text = text.replaceAll("[\\x00-\\x08]", " ");

        return text;
    }

} 