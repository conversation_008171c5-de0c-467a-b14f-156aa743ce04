package com.sinitek.mind.common.util;

import com.sinitek.sirm.common.spring.SpringFactory;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Locale;

/**
 * 国际化资源工具类
 */
public class I18nUtil {

    public static String t(String code) {
        MessageSource messageSource = SpringFactory.getBean(MessageSource.class);
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, null, code, locale);
    }

    public static String t(String code, Object... args) {
        MessageSource messageSource = SpringFactory.getBean(MessageSource.class);
        Locale locale = LocaleContextHolder.getLocale();
        return messageSource.getMessage(code, args, code, locale);
    }

}
