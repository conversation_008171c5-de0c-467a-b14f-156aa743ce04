/**
 * TikToken工具类，用于估算prompt的token数量
 *
 * <AUTHOR>
 * date 2025-07-17
 */

package com.sinitek.mind.common.util;

import com.knuddels.jtokkit.Encodings;
import com.knuddels.jtokkit.api.Encoding;
import com.knuddels.jtokkit.api.EncodingRegistry;
import com.knuddels.jtokkit.api.EncodingType;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageParam;
import com.sinitek.mind.core.ai.model.ChatCompletionMessageToolCall;
import org.apache.commons.lang3.StringUtils;
import org.springframework.ai.openai.api.OpenAiApi;

import java.util.List;


/**
 * 实现本地计算tokens
 *
 * <AUTHOR>
 * date 2025-07-17
 */
public class TikTokenUtil {

    /**
     * 估算prompt的token数量
     * @param prompt 输入prompt
     * @param role 角色（可选）
     * @return 估算的token数量
     */
    public static int countPromptTokens(String prompt, String role) {
        if (StringUtils.isBlank(prompt)) {
            return 0;
        }

        EncodingRegistry registry = Encodings.newDefaultEncodingRegistry();
        Encoding encoding = registry.getEncoding(EncodingType.CL100K_BASE); // 使用与tiktoken兼容的编码

        int tokens = encoding.countTokens(prompt);

        // 加上角色开销（如果有）
        if (StringUtils.isNotBlank(role)) {
            tokens += 3; // 简单加固定开销，需根据实际调整
        }

        return tokens;
    }

    /**
     * 计算GPT消息的token数量
     * @param messages 消息列表
     * @param tools 工具列表（可选）
     * @param functionCall 函数调用列表（可选）
     * @return token总数
     */
    public static Integer countGptMessagesTokens(List<ChatCompletionMessageParam> messages,
                                             List<ChatCompletionMessageToolCall> tools,
                                             List<OpenAiApi.ChatCompletionMessage.ChatCompletionFunction> functionCall
                                             ) {
        // TODO 需要JAVA实现
        return 0;
    }
}