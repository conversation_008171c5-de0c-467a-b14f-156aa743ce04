package com.sinitek.mind.common.util;

import com.sinitek.mind.common.dto.FileTokenQuery;
import com.sinitek.mind.common.enumerate.CommonErrEnum;
import com.sinitek.mind.common.exception.BusinessException;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.security.Key;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件令牌工具类
 * 用于生成和验证文件访问令牌
 *
 * <AUTHOR>
 * date 2025-07-17
 */
@Slf4j
@Component
public class MindFileTokenUtil {

    @Value("${app.file.token-key:SiniCubeMindProjectDefaultFileTokenKey}")
    private String fileTokenKey;

    private static final Map<String, Integer> BUCKET_PREVIEW_MINUTES = Map.of(
        "chat", 30,
        "dataset", 1440
    );

    /**
     * 创建文件令牌
     *
     * @param data 文件令牌查询数据
     * @return 生成的JWT令牌
     */
    public String createFileToken(FileTokenQuery data) {
        if (StringUtils.isBlank(fileTokenKey)) {
            throw new BusinessException(CommonErrEnum.SYSTEM_ERROR.getCode(), "系统未设置FILE_TOKEN_KEY");
        }

        Integer expireMinutes = data.getCustomExpireMinutes() != null ?
            data.getCustomExpireMinutes() : BUCKET_PREVIEW_MINUTES.getOrDefault(data.getBucketName(), 30);

        Instant expiredInstant = Instant.now().plus(Duration.ofMinutes(expireMinutes));
        Date expiredDate = Date.from(expiredInstant);

        Key key = Keys.hmacShaKeyFor(fileTokenKey.getBytes());

        Map<String, Object> claims = new HashMap<>();
        claims.put("bucketName", data.getBucketName());
        claims.put("teamId", data.getTeamId());
        claims.put("uid", data.getUid());
        claims.put("fileId", data.getFileId());

        return Jwts.builder()
            .setClaims(claims)
            .setExpiration(expiredDate)
            .signWith(key, SignatureAlgorithm.HS256)
            .compact();
    }

    /**
     * 验证并解析文件令牌
     *
     * @param token JWT令牌
     * @return 解析后的文件令牌查询数据
     * @throws BusinessException 如果令牌无效或过期
     */
    public FileTokenQuery authFileToken(String token) {
        if (StringUtils.isBlank(token)) {
            throw new BusinessException(CommonErrEnum.UNAUTHORIZED.getCode(), "未提供文件令牌");
        }

        if (StringUtils.isBlank(fileTokenKey)) {
            throw new BusinessException(CommonErrEnum.SYSTEM_ERROR.getCode(), "系统未设置FILE_TOKEN_KEY");
        }

        try {
            Key key = Keys.hmacShaKeyFor(fileTokenKey.getBytes());

            Claims claims = Jwts.parser()
                .setSigningKey(key)
                .build()
                .parseClaimsJws(token)
                .getBody();

            String bucketName = (String) claims.get("bucketName");
            String teamId = (String) claims.get("teamId");
            String uid = (String) claims.get("uid");
            String fileId = (String) claims.get("fileId");

            if (StringUtils.isAnyBlank(bucketName, fileId)) {
                throw new BusinessException(CommonErrEnum.UNAUTHORIZED.getCode(), "无效的文件令牌");
            }

            return new FileTokenQuery(bucketName, teamId, uid, fileId);
        } catch (Exception e) {
            log.error("文件令牌验证失败: {}", e.getMessage());
            throw new BusinessException(CommonErrEnum.UNAUTHORIZED.getCode(), "无效或过期的文件令牌");
        }
    }

} 