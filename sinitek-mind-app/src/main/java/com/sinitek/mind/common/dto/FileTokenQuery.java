package com.sinitek.mind.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

/**
 * 文件令牌查询DTO
 *
 * <AUTHOR>
 * date 2025-07-17
 */
@Data
@Schema(description = "文件令牌查询DTO")
public class FileTokenQuery implements Serializable {

    @Schema(description = "桶名称")
    private String bucketName;

    @Schema(description = "团队ID")
    private String teamId;

    @Schema(description = "用户ID")
    private String uid;

    @Schema(description = "文件ID")
    private String fileId;

    @Schema(description = "自定义过期分钟数")
    private Integer customExpireMinutes;

    public FileTokenQuery() {}

    public FileTokenQuery(String bucketName, String teamId, String uid, String fileId) {
        this.bucketName = bucketName;
        this.teamId = teamId;
        this.uid = uid;
        this.fileId = fileId;
    }

} 