package com.sinitek.mind.common.enumerate;

import lombok.Getter;

/**
 * 常见错误枚举
 *
 * <AUTHOR>
 * date 2025-07-17
 */
@Getter
public enum CommonErrEnum {

    SYSTEM_ERROR(500, "系统错误"),
    UNAUTHORIZED(401, "未授权"),
    MISSING_PARAMS(400, "缺少参数");

    private final int code;
    private final String message;

    CommonErrEnum(int code, String message) {
        this.code = code;
        this.message = message;
    }

} 