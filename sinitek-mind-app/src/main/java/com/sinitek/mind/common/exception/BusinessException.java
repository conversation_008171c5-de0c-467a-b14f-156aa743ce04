package com.sinitek.mind.common.exception;

import lombok.Getter;

/**
 * 业务异常类
 *
 * <AUTHOR>
 * date 2025-07-17
 */
@Getter
public class BusinessException extends RuntimeException {

    private final int code;

    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }

    public BusinessException(int code, String message, Throwable cause) {
        super(message, cause);
        this.code = code;
    }

} 