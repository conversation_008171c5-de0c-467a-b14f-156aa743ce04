package com.sinitek.mind.common.util;

import com.sinitek.mind.common.support.PageParam;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/7
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class PageUtil {

    public static Tuple2<Integer, Integer> formatPageOffset(PageParam param) {
        if (param == null) {
            param = new PageParam();
        }
        return formatPageOffset(param.getOffset(), param.getPageSize());
    }

    public static Tuple2<Integer, Integer> formatPageOffset(int offset, int pageSize) {
        if (pageSize <= 0) {
            // 防止后续计算报错
            pageSize = 20;
        }
        if (offset <= 0) {
            offset = 0;
        }
        // 例：offset=0,pageSize=20,则pageIndex为1
        int pageIndex = (offset / pageSize) + 1;
        return Tuples.of(pageIndex, pageSize);
    }

    public static <T> List<T> subList(List<T> list, PageParam param) {
        if (param == null) {
            param = new PageParam();
        }
        if (list == null) {
            return new LinkedList<>();
        }
        return subList(list, param.getOffset(), param.getPageSize());
    }

    public static <T> List<T> subList(List<T> list, int offset, int pageSize) {
        int size = list.size();
        int formIndex = Math.max(0, offset);
        int toIndex = Math.min(size, offset + pageSize);
        return list.subList(formIndex, toIndex);
    }
}
