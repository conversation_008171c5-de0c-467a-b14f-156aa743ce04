package com.sinitek.mind.common.support;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/4
 */
@Data
@Schema(description = "统一分页查询结果")
public class PageResult<T> {

    @Schema(description = "数据总数")
    private Integer total = 0;

    @Schema(description = "数据")
    private List<T> list;

    public static <T> PageResult<T> of(int total, List<T> list) {
        PageResult<T> result = new PageResult<>();
        result.setTotal(total);
        result.setList(list);
        return result;
    }

}
