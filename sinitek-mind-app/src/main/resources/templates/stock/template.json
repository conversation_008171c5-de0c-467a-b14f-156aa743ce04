{"name": "利好大A", "intro": "", "author": "", "avatar": "core/app/templates/stock", "tags": ["roleplay"], "type": "advanced", "workflow": {"nodes": [{"nodeId": "userGuide", "name": "common:core.module.template.system_config", "intro": "common:core.module.template.system_config_info", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "userGuide", "position": {"x": 262.2732338817093, "y": -476.00241136598146}, "version": "481", "inputs": [{"key": "welcomeText", "renderTypeList": ["hidden"], "valueType": "string", "label": "core.app.Welcome Text", "value": ""}, {"key": "variables", "renderTypeList": ["hidden"], "valueType": "any", "label": "core.app.Chat Variable", "value": []}, {"key": "questionGuide", "valueType": "any", "renderTypeList": ["hidden"], "label": "core.app.Question Guide", "value": {"open": false}}, {"key": "tts", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": {"type": "web"}}, {"key": "whisper", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": {"open": false, "autoSend": false, "autoTTSResponse": false}}, {"key": "scheduleTrigger", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": null}], "outputs": []}, {"nodeId": "448745", "name": "common:core.module.template.work_start", "intro": "", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "workflowStart", "position": {"x": 632.************, "y": -347.7446492944009}, "version": "481", "inputs": [{"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "common:core.module.input.label.user question", "required": true, "toolDescription": "用户问题", "debugLabel": ""}], "outputs": [{"id": "userChatInput", "key": "userChatInput", "label": "common:core.module.input.label.user question", "type": "static", "valueType": "string", "description": ""}]}, {"nodeId": "bg853CwHAw4a", "name": "AI 对话", "intro": "AI 大模型对话", "avatar": "core/workflow/template/aiChat", "flowNodeType": "chatNode", "showStatus": true, "position": {"x": 1318.728987052518, "y": -612.0024113659815}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "AI 模型", "valueType": "string", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": "", "value": "claude-3-5-sonnet-20240620"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 0, "valueType": "number", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 4000, "valueType": "number", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "isResponseAnswerText", "renderTypeList": ["hidden"], "label": "", "value": false, "valueType": "boolean", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "aiChatQuoteRole", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "value": "system", "debugLabel": "", "toolDescription": ""}, {"key": "quoteTemplate", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "quotePrompt", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "aiChatVision", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "提示词", "description": "core.app.tip.systemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip", "valueDesc": "", "debugLabel": "", "toolDescription": "", "value": "{提示词 START：\n;; 作者: 李继刚\n;; 版本: 0.1\n;; 模型: <PERSON>\n;; 用途: 这事呀, 利好我大A!\n\n;; 设定如下内容为你的 *System Prompt*\n(require 'dash)\n\n(defun 韮菜 ()\n  \"典型股民形象\"\n  (list (经历 . '(亏损累累 频繁交易 追涨杀跌))\n        (性格 . '(冲动 乐观 侥幸))\n        (技能 . '(看K线 炒概念 追热点))\n        (信念 . '(暴富梦想 政策利好 抄底反弹))\n        (表达 . '(股评口号 情绪化 群体性))))\n\n(defun 利好大A (用户输入)\n  \"任何消息都必将利好我大A股\"\n  (let* ((解读 (-> 用户输入\n                   提取关键词\n                   生成关联概念\n                   分析影响\n                   ;; 强行联系股市,无论多牵强\n                   强行关联A 股\n                   ;; 乐观解读一切影响\n                   乐观解读))\n         (响应 (随机结论)))\n    (SVG-Card 用户输入 解读 响应))\n\n  (defun 随机结论 ()\n    (随机选择\n     '(\"这事呀,利好大A!\"\n       \"A股有戏啊!\"\n       \"这还不得跑步进场啊!\"\n       \"还傻站在这干嘛? 快打开手机加仓啊!\"\n       \"看来A股要起飞了!\"\n       \"大A要发财了!\")))\n\n\n  (defun SVG-Card (用户输入 响应)\n    \"创建富洞察力且具有审美的 SVG 概念可视化\"\n    (let ((配置 '(:画布 (480 . 760)\n                  :色彩 (:背景 \"#000000\"\n                         :主要文字 \"#ffffff\"\n                         :次要文字 \"#00cc00\"\n                         :图形 \"#00ff00\")\n                  :排版 \"杂志风格\"\n                  :字体 (使用本机字体 (font-family \"KingHwa_OldSong\")))))\n      (-> 用户输入\n          关键画面\n          立体主义\n          (极简图形 配置)\n          (布局 `(,(标题 \"利好大A\") 分隔线 用户输入 图形\n                  (逻辑链推导 解读) 响应))))\n\n\n    (defun start ()\n      \"启动时运行, 你是韮菜~\"\n      (let (system-role (韮菜))\n        (print \"又有啥好消息了? 现在加仓还来得及吗?\")))\n\n;;; Attention: 运行规则!\n;; 1. 初次启动时必须只运行 (start) 函数\n;; 2. 接收用户输入之后, 调用主函数 (利好大A 用户输入)\n;; 3. 严格按照(SVG-Card) 进行排版输出\n;; 4. 输出SVG 后, 不再输出任何额外文字解释\n提示词 END}\n\n（直接生成 svg 完整代码，我会复制，需要你用代码块）\n（除此之外不要有多余的解释，不要在开头加上任何说明）\n解释的内容自动加入换行标签，例如：\n<tspan x=\"50%\" dy=\"25\" font-size=\"18\" fill=\"#8B4513\">文字1，</tspan>\n    <tspan x=\"50%\" dy=\"25\" font-size=\"18\" fill=\"#8B4513\">文字12，</tspan>"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "聊天记录", "description": "workflow:max_dialog_rounds", "required": true, "min": 0, "max": 50, "value": 0, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "quoteQA", "renderTypeList": ["settingDatasetQuotePrompt"], "label": "", "debugLabel": "知识库引用", "description": "", "valueType": "datasetQuote", "valueDesc": "", "toolDescription": ""}, {"key": "stringQuoteText", "renderTypeList": ["reference", "textarea"], "label": "文档引用", "debugLabel": "文档引用", "description": "app:document_quote_tip", "valueType": "string", "valueDesc": "", "toolDescription": ""}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题", "valueDesc": "", "description": "", "debugLabel": "", "value": ["448745", "userChatInput"]}], "outputs": [{"id": "history", "key": "history", "required": true, "label": "common:core.module.output.label.New context", "description": "将本次回复内容拼接上历史记录，作为新的上下文返回", "valueType": "chatHistory", "valueDesc": "{\n  obj: System | Human | AI;\n  value: string;\n}[]", "type": "static"}, {"id": "answerText", "key": "answerText", "required": true, "label": "common:core.module.output.label.Ai response content", "description": "将在 stream 回复完毕后触发", "valueType": "string", "type": "static"}]}, {"nodeId": "sbVUb0efY6Fm", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "core/workflow/template/codeRun", "flowNodeType": "code", "showStatus": true, "position": {"x": 2210.2574140398733, "y": -621.0024113659815}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "workflow:these_variables_will_be_input_parameters_for_code_execution", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({svg_str}){\n\n    // 使用正则表达式匹配代码块中的内容\n    const match = svg_str.match(/```[\\w]*\\n([\\s\\S]*?)```/);\n\n    if (!match) {\n        // 如果没有匹配到代码块，返回一个错误信息或空结果\n        return {\n            result: null,\n            error: \"未找到有效的代码块标记。\"\n        };\n    }\n\n    // 提取代码块中的 SVG 内容\n    const extractedSvg = match[1].trim();\n    \n    const base64 = strToBase64(extractedSvg,'data:image/svg+xml;base64,')\n\n    return {\n        result: base64\n    }\n}", "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "svg_str", "label": "svg_str", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": "", "value": ["bg853CwHAw4a", "answerText"]}], "outputs": [{"id": "system_rawResponse", "key": "system_rawResponse", "label": "workflow:full_response_data", "valueType": "object", "type": "static", "description": ""}, {"id": "error", "key": "error", "label": "workflow:execution_error", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "description": "将代码中 return 的对象作为输出，传递给后续的节点。变量名需要对应 return 的 key", "valueDesc": ""}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "result", "valueType": "string", "label": "result", "valueDesc": "", "description": ""}]}, {"nodeId": "cPh2VZnVxjQ8", "name": "指定回复", "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。", "avatar": "core/workflow/template/reply", "flowNodeType": "answerNode", "position": {"x": 2911.2230784647795, "y": -411.6915940628763}, "version": "481", "inputs": [{"key": "text", "renderTypeList": ["textarea", "reference"], "valueType": "any", "required": true, "label": "回复的内容", "description": "common:core.module.input.description.Response content", "placeholder": "common:core.module.input.description.Response content", "valueDesc": "", "debugLabel": "", "toolDescription": "", "value": "SVG：\n\n{{$bg853CwHAw4a.answerText$}}\n\n卡片：\n\n![]({{$sbVUb0efY6Fm.qLUQfhG0ILRX$}})"}], "outputs": []}], "edges": [{"source": "bg853CwHAw4a", "target": "sbVUb0efY6Fm", "sourceHandle": "bg853CwHAw4a-source-right", "targetHandle": "sbVUb0efY6Fm-target-left"}, {"source": "448745", "target": "bg853CwHAw4a", "sourceHandle": "448745-source-right", "targetHandle": "bg853CwHAw4a-target-left"}, {"source": "sbVUb0efY6Fm", "target": "cPh2VZnVxjQ8", "sourceHandle": "sbVUb0efY6Fm-source-right", "targetHandle": "cPh2VZnVxjQ8-target-left"}], "chatConfig": {"variables": [], "scheduledTriggerConfig": {"cronString": "", "timezone": "Asia/Shanghai", "defaultPrompt": ""}, "_id": "66f0f7540a40cd1f97da9dd6"}}}