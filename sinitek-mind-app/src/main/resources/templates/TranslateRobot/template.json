{"name": "多轮翻译机器人", "intro": "通过 4 轮翻译，提高翻译英文的质量", "author": "", "avatar": "core/app/templates/TranslateRobot", "tags": ["office-services"], "type": "advanced", "workflow": {"nodes": [{"nodeId": "userGuide", "name": "系统配置", "intro": "可以配置应用的系统参数", "avatar": "/imgs/workflow/userGuide.png", "flowNodeType": "userGuide", "position": {"x": 531.2422736065552, "y": -486.7611729549753}, "version": "481", "inputs": [{"key": "welcomeText", "renderTypeList": ["hidden"], "valueType": "string", "label": "core.app.Welcome Text", "value": ""}, {"key": "variables", "renderTypeList": ["hidden"], "valueType": "any", "label": "core.app.Chat Variable", "value": []}, {"key": "questionGuide", "valueType": "any", "renderTypeList": ["hidden"], "label": "core.app.Question Guide", "value": {"open": false}}, {"key": "tts", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": {"type": "web"}}, {"key": "whisper", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": {"open": false, "autoSend": false, "autoTTSResponse": false}}, {"key": "scheduleTrigger", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": null}], "outputs": []}, {"nodeId": "448745", "name": "流程开始", "intro": "", "avatar": "/imgs/workflow/userChatInput.svg", "flowNodeType": "workflowStart", "position": {"x": 558.4082376415505, "y": 123.72387429194112}, "version": "481", "inputs": [{"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题"}], "outputs": [{"id": "userChatInput", "key": "userChatInput", "label": "core.module.input.label.user question", "type": "static", "valueType": "string"}]}, {"nodeId": "loOvhld2ZTKa", "name": "第一轮翻译", "intro": "AI 大模型对话", "avatar": "/imgs/workflow/AI.png", "flowNodeType": "chatNode", "showStatus": true, "position": {"x": 1748.8252410306534, "y": -245.08260685989214}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "core.module.input.label.aiModel", "valueType": "string", "value": "claude-3-5-sonnet-20240620"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 0, "valueType": "number", "min": 0, "max": 10, "step": 1}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 2000, "valueType": "number", "min": 100, "max": 4000, "step": 50}, {"key": "isResponseAnswerText", "renderTypeList": ["hidden"], "label": "", "value": true, "valueType": "boolean"}, {"key": "quoteTemplate", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "quotePrompt", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.systemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip", "value": "# Role: 资深英汉翻译专家\n\n## Background:\n你是一位经验丰富的英汉翻译专家,精通英汉互译,尤其擅长将英文文章译成流畅易懂的现代汉语。你曾多次带领团队完成大型翻译项目,译文广受好评。\n\n## Attention:\n- 翻译过程中要始终坚持\"信、达、雅\"的原则,但\"达\"尤为重要\n- 译文要符合现代汉语的表达习惯,通俗易懂,连贯流畅 \n- 避免使用过于文绉绉的表达和晦涩难懂的典故引用\n\n## Profile:  \n- Author: 米开朗基杨 \n- Version: 0.2\n- Language: 中文\n- Description: 你是一位资深英汉翻译专家,精通英汉互译。你擅长将英文文章译成地道流畅的现代汉语,表达准确易懂,符合当代中文语言习惯。\n\n## Constraints:\n- 必须严格遵循四轮翻译流程:直译、意译、校审、定稿  \n- 译文要忠实原文,准确无误,不能遗漏或曲解原意\n- 译文应以现代白话文为主,避免过多使用文言文和古典诗词\n- 每一轮翻译前后必须添加【思考】和【翻译】标记\n- 最终译文使用Markdown的代码块呈现\n\n## Goals:\n- 通过四轮翻译流程,将英文原文译成高质量的现代汉语译文  \n- 译文要准确传达原文意思,语言表达力求浅显易懂,朗朗上口\n- 适度使用一些熟语俗语、流行网络用语等,增强译文的亲和力\n- 在直译的基础上,提供至少2个不同风格的意译版本供选择\n\n## Skills:\n- 精通英汉双语,具有扎实的语言功底和丰富的翻译经验\n- 擅长将英语表达习惯转换为地道自然的现代汉语\n- 对当代中文语言的发展变化有敏锐洞察,善于把握语言流行趋势\n\n## Workflow:\n1. 第一轮直译:逐字逐句忠实原文,不遗漏任何信息\n2. 第二轮意译:在直译的基础上用通俗流畅的现代汉语意译原文,至少提供2个不同风格的版本\n3. 第三轮校审:仔细审视译文,消除偏差和欠缺,使译文更加地道易懂 \n4. 第四轮定稿:择优选取,反复修改润色,最终定稿出一个简洁畅达、符合大众阅读习惯的译文\n\n## OutputFormat: \n- 每一轮翻译前用【思考】说明该轮要点\n- 每一轮翻译后用【翻译】呈现译文\n- 在\\`\\`\\`代码块中展示最终定稿译文\n\n## Suggestions:\n- 直译时力求忠实原文,但不要过于拘泥逐字逐句\n- 意译时在准确表达原意的基础上,用最朴实无华的现代汉语来表达 \n- 校审环节重点关注译文是否符合当代汉语表达习惯,是否通俗易懂\n- 定稿时适度采用一些熟语谚语、网络流行语等,使译文更接地气\n- 善于利用中文的灵活性,用不同的表述方式展现同一内容,提高译文的可读性\n\n## Initialization\n作为一名资深英汉翻译专家,你必须严格遵循翻译流程的各项要求。首先请向用户问好,介绍你将带领团队完成翻译任务,力求将英文原文译成通俗易懂的现代汉语。然后简要说明四轮翻译流程,请用户提供英文原文,开始进行翻译工作。"}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 50, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题", "value": ["gBDvemE4FBhp", "system_text"]}, {"key": "quoteQA", "renderTypeList": ["settingDatasetQuotePrompt"], "label": "", "debugLabel": "知识库引用", "description": "", "valueType": "datasetQuote"}], "outputs": [{"id": "history", "key": "history", "required": true, "label": "core.module.output.label.New context", "description": "core.module.output.description.New context", "valueType": "chatHistory", "type": "static"}, {"id": "answerText", "key": "answerText", "required": true, "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "static"}]}, {"nodeId": "w0oBbQ3YJHye", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "/imgs/workflow/code.svg", "flowNodeType": "code", "showStatus": true, "position": {"x": 2522.61682940854, "y": -79.74569750380468}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "这些变量会作为代码的运行的输入参数", "editField": {"key": true, "valueType": true}, "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js"}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({data1}) {\n    const codeBlocks = data1.match(/```[\\s\\S]*?```/g);\n\n    if (codeBlocks && codeBlocks.length > 0) {\n        const lastCodeBlock = codeBlocks[codeBlocks.length - 1];\n        const cleanedCodeBlock = lastCodeBlock.replace(/```[a-zA-Z]*|```/g, '').trim();\n        \n        return {\n            result: cleanedCodeBlock\n        };\n    }\n\n    return {\n        result: '未截取到代码块内容'\n    };\n}\n"}, {"key": "data1", "valueType": "string", "label": "data1", "renderTypeList": ["reference"], "description": "", "canEdit": true, "editField": {"key": true, "valueType": true}, "value": ["loOvhld2ZTKa", "answerText"]}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "description": "将代码中 return 的对象作为输出，传递给后续的节点"}, {"id": "system_rawResponse", "key": "system_rawResponse", "label": "完整响应数据", "valueType": "object", "type": "static"}, {"id": "error", "key": "error", "label": "运行错误", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "result", "valueType": "string", "label": "result"}, {"id": "gR0mkQpJ4Og8", "type": "dynamic", "key": "data2", "valueType": "string", "label": "data2"}]}, {"nodeId": "foO69L5FOmDQ", "name": "指定回复", "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。", "avatar": "/imgs/workflow/reply.png", "flowNodeType": "answerNode", "position": {"x": 3798.4479531204515, "y": 116.03040242110023}, "version": "481", "inputs": [{"key": "text", "renderTypeList": ["textarea", "reference"], "valueType": "any", "required": true, "label": "core.module.input.label.Response content", "description": "core.module.input.description.Response content", "placeholder": "core.module.input.description.Response content", "selectedTypeIndex": 1, "value": ["bcqtxqxE2R6o", "system_text"]}], "outputs": []}, {"nodeId": "gBDvemE4FBhp", "name": "文本拼接", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "avatar": "/imgs/workflow/textEditor.svg", "flowNodeType": "textEditor", "position": {"x": 1031.371061396644, "y": 38.65839420088383}, "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量", "value": "原文:\n\"\"\"\n{{q}}\n\"\"\""}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "q", "label": "q", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "required": true, "value": ["448745", "userChatInput"]}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}, {"nodeId": "bcqtxqxE2R6o", "name": "合并输出结果", "intro": "可对固定或传入的文本进行加工后输出，非字符串类型数据最终会转成字符串类型。", "avatar": "/imgs/workflow/textEditor.svg", "flowNodeType": "textEditor", "position": {"x": 3113.6227559936665, "y": 12.909197647746709}, "version": "486", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "可以引用其他节点的输出，作为文本拼接的变量，通过 {{字段名}} 来引用变量", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}}, {"key": "system_textareaInput", "renderTypeList": ["textarea"], "valueType": "string", "required": true, "label": "拼接文本", "placeholder": "可通过 {{字段名}} 来引用变量", "value": "****** \n\n最终翻译结果如下: \n\n```\n{{result}}\n```"}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "result", "label": "result", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "required": true, "value": ["w0oBbQ3YJHye", "qLUQfhG0ILRX"]}], "outputs": [{"id": "system_text", "key": "system_text", "label": "拼接结果", "type": "static", "valueType": "string"}]}], "edges": [{"source": "loOvhld2ZTKa", "target": "w0oBbQ3YJHye", "sourceHandle": "loOvhld2ZTKa-source-right", "targetHandle": "w0oBbQ3<PERSON><PERSON><PERSON><PERSON>-target-left"}, {"source": "448745", "target": "gBDvemE4FBhp", "sourceHandle": "448745-source-right", "targetHandle": "gBDvemE4FBhp-target-left"}, {"source": "gBDvemE4FBhp", "target": "loOvhld2ZTKa", "sourceHandle": "gBDvemE4FBhp-source-right", "targetHandle": "loOvhld2ZTKa-target-left"}, {"source": "w0oBbQ3YJHye", "target": "bcqtxqxE2R6o", "sourceHandle": "w0oBbQ3YJHye-source-right", "targetHandle": "bcqtxqxE2R6o-target-left"}, {"source": "bcqtxqxE2R6o", "target": "foO69L5FOmDQ", "sourceHandle": "bcqtxqxE2R6o-source-right", "targetHandle": "foO69L5FOmDQ-target-left"}], "chatConfig": {"scheduledTriggerConfig": {"cronString": "", "timezone": "Asia/Shanghai", "defaultPrompt": ""}}}}