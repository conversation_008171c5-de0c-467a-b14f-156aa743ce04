{"name": "谷歌搜索", "intro": "通过请求谷歌搜索，查询相关内容作为模型的参考。", "author": "", "avatar": "core/app/templates/google", "tags": ["recommendation", "web-search"], "type": "advanced", "workflow": {"nodes": [{"nodeId": "userGuide", "name": "系统配置", "intro": "可以配置应用的系统参数", "avatar": "/imgs/workflow/userGuide.png", "flowNodeType": "userGuide", "position": {"x": 262.2732338817093, "y": -476.00241136598146}, "version": "481", "inputs": [{"key": "welcomeText", "renderTypeList": ["hidden"], "valueType": "string", "label": "core.app.Welcome Text", "value": ""}, {"key": "variables", "renderTypeList": ["hidden"], "valueType": "any", "label": "core.app.Chat Variable", "value": []}, {"key": "questionGuide", "valueType": "any", "renderTypeList": ["hidden"], "label": "core.app.Question Guide", "value": {"open": false}}, {"key": "tts", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": {"type": "web"}}, {"key": "whisper", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": {"open": false, "autoSend": false, "autoTTSResponse": false}}, {"key": "scheduleTrigger", "renderTypeList": ["hidden"], "valueType": "any", "label": "", "value": null}], "outputs": []}, {"nodeId": "448745", "name": "流程开始", "intro": "", "avatar": "/imgs/workflow/userChatInput.svg", "flowNodeType": "workflowStart", "position": {"x": 295.8944548701009, "y": 110.81336038514848}, "version": "481", "inputs": [{"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题"}], "outputs": [{"id": "userChatInput", "key": "userChatInput", "label": "core.module.input.label.user question", "valueType": "string", "type": "static"}]}, {"nodeId": "NOgbnBzUwDgT", "name": "工具调用", "intro": "通过AI模型自动选择一个或多个功能块进行调用，也可以对插件进行调用。", "avatar": "/imgs/workflow/tool.svg", "flowNodeType": "tools", "showStatus": true, "position": {"x": 1028.8358722416106, "y": -500.8755882990822}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "core.module.input.label.aiModel", "valueType": "string", "llmModelType": "all", "value": "FastAI-plus"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 0, "valueType": "number", "min": 0, "max": 10, "step": 1}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 2000, "valueType": "number", "min": 100, "max": 4000, "step": 50}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.systemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip", "value": ""}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 30, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "value": ["448745", "userChatInput"]}], "outputs": [{"id": "NodeOutputKeyEnum.answerText", "key": "NodeOutputKeyEnum.answerText", "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "FlowNodeOutputTypeEnum.static"}]}, {"nodeId": "GMELVPxHfpg5", "name": "HTTP 请求", "intro": "调用谷歌搜索，查询相关内容", "avatar": "/imgs/workflow/http.png", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1005.4777753640342, "y": 319.4905539380939}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "core.module.input.description.HTTP Dynamic Input"}, {"valueType": "string", "renderTypeList": ["reference"], "key": "query", "label": "query", "toolDescription": "谷歌搜索检索词", "required": true, "canEdit": true, "editField": {"key": true, "description": true}}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "GET", "required": true}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://www.googleapis.com/customsearch/v1"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "core.module.input.description.Http Request Header", "placeholder": "core.module.input.description.Http Request Header", "required": false}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [{"key": "q", "type": "string", "value": "{{query}}"}, {"key": "cx", "type": "string", "value": "谷歌搜索cxID"}, {"key": "key", "type": "string", "value": "谷歌搜索key"}, {"key": "c2coff", "type": "string", "value": "1"}, {"key": "start", "type": "string", "value": "1"}, {"key": "end", "type": "string", "value": "20"}, {"key": "dateRestrict", "type": "string", "value": "m[1]"}], "label": "", "required": false}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "", "label": "", "required": false}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "editField": {"key": true, "valueType": true}}, {"id": "httpRawResponse", "key": "httpRawResponse", "label": "原始响应", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static", "required": true}, {"id": "M5YmxaYe8em1", "type": "dynamic", "key": "prompt", "valueType": "string", "label": "prompt"}]}, {"nodeId": "poIbrrA8aiR0", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "/imgs/workflow/code.svg", "flowNodeType": "code", "showStatus": true, "position": {"x": 1711.805344753384, "y": 650.1023414708576}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "这些变量会作为代码的运行的输入参数", "editField": {"key": true, "valueType": true}}, {"key": "data", "valueType": "object", "label": "data", "renderTypeList": ["reference"], "description": "", "canEdit": true, "editField": {"key": true, "valueType": true}, "value": ["GMELVPxHfpg5", "httpRawResponse"]}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js"}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({data}){\n    const result = data.items.map((item) => ({\n      title: item.title,\n      link: item.link,\n      snippet: item.snippet\n    }))\n    return { prompt: JSON.stringify(result) }\n}"}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "editField": {"key": true, "valueType": true}, "description": "将代码中 return 的对象作为输出，传递给后续的节点"}, {"id": "system_rawResponse", "key": "system_rawResponse", "label": "完整响应数据", "valueType": "object", "type": "static"}, {"id": "error", "key": "error", "label": "运行错误", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "prompt", "valueType": "string", "label": "prompt"}]}], "edges": [{"source": "448745", "target": "NOgbnBzUwDgT", "sourceHandle": "448745-source-right", "targetHandle": "NOgbnBzUwDgT-target-left"}, {"source": "NOgbnBzUwDgT", "target": "GMELVPxHfpg5", "sourceHandle": "selectedTools", "targetHandle": "selectedTools"}, {"source": "GMELVPxHfpg5", "target": "poIbrrA8aiR0", "sourceHandle": "GMELVPxHfpg5-source-right", "targetHandle": "poIbrrA8aiR0-target-left"}]}}