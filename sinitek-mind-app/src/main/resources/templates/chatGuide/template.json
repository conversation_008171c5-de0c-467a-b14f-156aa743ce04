{"name": "对话引导 + 变量", "intro": "可以在对话开始发送一段提示，或者让用户填写一些内容，作为本次对话的变量", "author": "", "avatar": "core/workflow/template/systemConfig", "tags": ["office-services"], "type": "simple", "weight": 1, "workflow": {"nodes": [{"nodeId": "userGuide", "name": "系统配置", "intro": "可以配置应用的系统参数", "avatar": "/imgs/workflow/userGuide.png", "flowNodeType": "userGuide", "position": {"x": 496.57560693988853, "y": -490.7611729549753}, "version": "481", "inputs": [], "outputs": []}, {"nodeId": "448745", "name": "流程开始", "intro": "", "avatar": "/imgs/workflow/userChatInput.svg", "flowNodeType": "workflowStart", "position": {"x": 558.4082376415505, "y": 123.72387429194112}, "version": "481", "inputs": [{"key": "userChatInput", "renderTypeList": ["FlowNodeInputTypeEnum.reference", "FlowNodeInputTypeEnum.textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题"}], "outputs": [{"id": "userChatInput", "key": "userChatInput", "label": "core.module.input.label.user question", "valueType": "string", "type": "FlowNodeOutputTypeEnum.static"}]}, {"nodeId": "loOvhld2ZTKa", "name": "AI 对话", "intro": "AI 大模型对话", "avatar": "/imgs/workflow/AI.png", "flowNodeType": "chatNode", "showStatus": true, "position": {"x": 1097.7317280958762, "y": -244.16014496351386}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["FlowNodeInputTypeEnum.settingLLMModel", "FlowNodeInputTypeEnum.reference"], "label": "core.module.input.label.aiModel", "valueType": "string", "value": "gpt-3.5-turbo"}, {"key": "temperature", "renderTypeList": ["FlowNodeInputTypeEnum.hidden"], "label": "", "value": 0, "valueType": "number", "min": 0, "max": 10, "step": 1}, {"key": "maxToken", "renderTypeList": ["FlowNodeInputTypeEnum.hidden"], "label": "", "value": 2000, "valueType": "number", "min": 100, "max": 4000, "step": 50}, {"key": "isResponseAnswerText", "renderTypeList": ["FlowNodeInputTypeEnum.hidden"], "label": "", "value": true, "valueType": "boolean"}, {"key": "quoteTemplate", "renderTypeList": ["FlowNodeInputTypeEnum.hidden"], "label": "", "valueType": "string"}, {"key": "quotePrompt", "renderTypeList": ["FlowNodeInputTypeEnum.hidden"], "label": "", "valueType": "string"}, {"key": "systemPrompt", "renderTypeList": ["FlowNodeInputTypeEnum.textarea", "FlowNodeInputTypeEnum.reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.systemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip", "value": "请直接将我的问题翻译成{{language}}，不需要回答问题。"}, {"key": "history", "renderTypeList": ["FlowNodeInputTypeEnum.numberInput", "FlowNodeInputTypeEnum.reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "required": true, "min": 0, "max": 30, "value": 6}, {"key": "userChatInput", "renderTypeList": ["FlowNodeInputTypeEnum.reference", "FlowNodeInputTypeEnum.textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题", "value": ["448745", "userChatInput"]}, {"key": "quoteQA", "renderTypeList": ["FlowNodeInputTypeEnum.settingDatasetQuotePrompt"], "label": "", "debugLabel": "知识库引用", "description": "", "valueType": "datasetQuote"}], "outputs": [{"id": "history", "key": "history", "label": "core.module.output.label.New context", "description": "core.module.output.description.New context", "valueType": "chatHistory", "type": "FlowNodeOutputTypeEnum.static"}, {"id": "answerText", "key": "answerText", "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "FlowNodeOutputTypeEnum.static"}]}], "edges": [{"source": "448745", "target": "loOvhld2ZTKa", "sourceHandle": "448745-source-right", "targetHandle": "loOvhld2ZTKa-target-left"}], "chatConfig": {"welcomeText": "你好，我可以为你翻译各种语言，请告诉我你需要翻译成什么语言？", "scheduledTriggerConfig": {"cronString": "", "timezone": "Asia/Shanghai", "defaultPrompt": ""}}}}