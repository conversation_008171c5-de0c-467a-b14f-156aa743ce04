{"name": "问题分类 + 知识库", "intro": "先对用户的问题进行分类，再根据不同类型问题，执行不同的操作", "author": "", "avatar": "core/workflow/template/questionClassify", "tags": ["office-services"], "type": "advanced", "workflow": {"nodes": [{"nodeId": "userGuide", "name": "系统配置", "intro": "可以配置应用的系统参数", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "userGuide", "position": {"x": 531.2422736065552, "y": -486.7611729549753}, "version": "481", "inputs": [], "outputs": []}, {"nodeId": "workflowStartNodeId", "name": "流程开始", "intro": "", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "workflowStart", "position": {"x": 558.4082376415505, "y": 123.72387429194112}, "version": "481", "inputs": [{"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题"}], "outputs": [{"id": "userChatInput", "key": "userChatInput", "label": "core.module.input.label.user question", "valueType": "string", "type": "FlowNodeOutputTypeEnum.static"}]}, {"nodeId": "7BdojPlukIQw", "name": "AI 对话", "intro": "AI 大模型对话", "avatar": "core/workflow/template/aiChat", "flowNodeType": "chatNode", "showStatus": true, "position": {"x": 2701.1267277679685, "y": -767.8956312653042}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "core.module.input.label.aiModel", "valueType": "string", "value": "gpt-4o-mini"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 3, "valueType": "number", "min": 0, "max": 10, "step": 1}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 1950, "valueType": "number", "min": 100, "max": 4000, "step": 50}, {"key": "isResponseAnswerText", "renderTypeList": ["hidden"], "label": "", "value": true, "valueType": "boolean"}, {"key": "quoteTemplate", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "quotePrompt", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.systemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip", "value": ""}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "required": true, "min": 0, "max": 30, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题", "value": ["workflowStartNodeId", "userChatInput"]}, {"key": "quoteQA", "renderTypeList": ["settingDatasetQuotePrompt"], "label": "", "debugLabel": "知识库引用", "description": "", "valueType": "datasetQuote", "value": ["MNMMMIjjWyMU", "quoteQA"]}], "outputs": [{"id": "history", "key": "history", "label": "core.module.output.label.New context", "description": "core.module.output.description.New context", "valueType": "chatHistory", "type": "FlowNodeOutputTypeEnum.static"}, {"id": "answerText", "key": "answerText", "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "FlowNodeOutputTypeEnum.static"}]}, {"nodeId": "rvbo634w3AYj", "name": "问题分类", "intro": "根据用户的历史记录和当前问题判断该次提问的类型。可以添加多组问题类型，下面是一个模板例子：\n类型1: 打招呼\n类型2: 关于商品\"使用\"问题\n类型3: 关于商品\"购买\"问题\n类型4: 其他问题", "avatar": "core/workflow/template/questionClassify", "flowNodeType": "classifyQuestion", "showStatus": true, "position": {"x": 1020.9667229609946, "y": -385.0060974413916}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["selectLLMModel", "reference"], "label": "core.module.input.label.aiModel", "required": true, "valueType": "string", "llmModelType": "classify", "value": "gpt-4o-mini"}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.module.input.label.Background", "description": "core.module.input.description.Background", "placeholder": "core.module.input.placeholder.Classify background", "value": ""}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "required": true, "min": 0, "max": 30, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "value": ["workflowStartNodeId", "userChatInput"]}, {"key": "agents", "renderTypeList": ["custom"], "valueType": "any", "label": "", "value": [{"value": "关于电影《星际穿越》的问题", "key": "wqre"}, {"value": "打招呼、问候等问题", "key": "sdfa"}, {"value": "其他问题", "key": "agex"}]}], "outputs": [{"id": "cqResult", "key": "cqResult", "label": "分类结果", "valueType": "string", "type": "FlowNodeOutputTypeEnum.static"}]}, {"nodeId": "7kwgL1dVlwG6", "name": "指定回复", "intro": "该模块可以直接回复一段指定的内容。常用于引导、提示。非字符串内容传入时，会转成字符串进行输出。", "avatar": "core/workflow/template/reply", "flowNodeType": "answerNode", "position": {"x": 1874.9167551056487, "y": 434.98431875888207}, "version": "481", "inputs": [{"key": "text", "renderTypeList": ["textarea", "reference"], "valueType": "string", "label": "core.module.input.label.Response content", "description": "core.module.input.description.Response content", "placeholder": "core.module.input.description.Response content", "selectedTypeIndex": 1, "value": ["rvbo634w3AYj", "cqResult"]}], "outputs": []}, {"nodeId": "MNMMMIjjWyMU", "name": "知识库搜索", "intro": "调用\"语义检索\"和\"全文检索\"能力，从\"知识库\"中查找可能与问题相关的参考内容", "avatar": "core/workflow/template/datasetSearch", "flowNodeType": "datasetSearchNode", "showStatus": true, "position": {"x": 1851.010152279949, "y": -613.3555232387284}, "version": "481", "inputs": [{"key": "datasets", "renderTypeList": ["selectDataset", "reference"], "label": "core.module.input.label.Select dataset", "value": [], "valueType": "selectDataset", "list": [], "required": true}, {"key": "similarity", "renderTypeList": ["selectDatasetParamsModal"], "label": "", "value": 0.4, "valueType": "number"}, {"key": "limit", "renderTypeList": ["hidden"], "label": "", "value": 5000, "valueType": "number"}, {"key": "searchMode", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "value": "embedding"}, {"key": "usingReRank", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": false}, {"key": "datasetSearchUsingExtensionQuery", "renderTypeList": ["hidden"], "label": "", "valueType": "boolean", "value": true}, {"key": "datasetSearchExtensionModel", "renderTypeList": ["hidden"], "label": "", "valueType": "string"}, {"key": "datasetSearchExtensionBg", "renderTypeList": ["hidden"], "label": "", "valueType": "string", "value": ""}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "需要检索的内容", "value": ["workflowStartNodeId", "userChatInput"]}], "outputs": [{"id": "quoteQA", "key": "quoteQA", "label": "core.module.Dataset quote.label", "description": "特殊数组格式，搜索结果为空时，返回空数组。", "type": "FlowNodeOutputTypeEnum.static", "valueType": "datasetQuote"}]}], "edges": [{"source": "workflowStartNodeId", "target": "rvbo634w3AYj", "sourceHandle": "workflowStartNodeId-source-right", "targetHandle": "rvbo634w3AYj-target-left"}, {"source": "rvbo634w3AYj", "target": "7kwgL1dVlwG6", "sourceHandle": "rvbo634w3AYj-source-agex", "targetHandle": "7kwgL1dVlwG6-target-left"}, {"source": "rvbo634w3AYj", "target": "MNMMMIjjWyMU", "sourceHandle": "rvbo634w3AYj-source-wqre", "targetHandle": "MNMMMIjjWyMU-target-left"}, {"source": "MNMMMIjjWyMU", "target": "7BdojPlukIQw", "sourceHandle": "MNMMMIjjWyMU-source-right", "targetHandle": "7BdojPlukIQw-target-left"}, {"source": "rvbo634w3AYj", "target": "7kwgL1dVlwG6", "sourceHandle": "rvbo634w3AYj-source-sdfa", "targetHandle": "7kwgL1dVlwG6-target-left"}], "chatConfig": {"scheduledTriggerConfig": {"cronString": "", "timezone": "Asia/Shanghai", "defaultPrompt": ""}, "welcomeText": "你好，我是知识库助手，请不要忘记选择知识库噢~\n[你是谁]\n[如何使用]"}}}