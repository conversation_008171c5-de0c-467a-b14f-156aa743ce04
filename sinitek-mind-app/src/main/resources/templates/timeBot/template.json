{"name": "知道时间的机器人", "intro": "通过挂载时间插件，让模型获取当前最新时间", "author": "", "avatar": "core/workflow/template/getTime", "tags": ["recommendation", "roleplay"], "type": "simple", "weight": 1, "workflow": {"nodes": [{"nodeId": "userGuide", "name": "系统配置", "intro": "可以配置应用的系统参数", "avatar": "/imgs/workflow/userGuide.png", "flowNodeType": "userGuide", "position": {"x": 531.2422736065552, "y": -486.7611729549753}, "version": "481", "inputs": [], "outputs": []}, {"nodeId": "workflowStartNodeId", "name": "流程开始", "intro": "", "avatar": "/imgs/workflow/userChatInput.svg", "flowNodeType": "workflowStart", "position": {"x": 558.4082376415505, "y": 123.72387429194112}, "version": "481", "inputs": [{"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "toolDescription": "用户问题"}], "outputs": [{"id": "userChatInput", "key": "userChatInput", "label": "core.module.input.label.user question", "valueType": "string", "type": "static"}]}, {"nodeId": "jrWPV9", "name": "工具调用", "intro": "通过AI模型自动选择一个或多个功能块进行调用，也可以对插件进行调用。", "avatar": "/imgs/workflow/tool.svg", "flowNodeType": "tools", "showStatus": true, "position": {"x": 1062.1738942532802, "y": -223.65033022650476}, "version": "481", "inputs": [{"key": "model", "renderTypeList": ["settingLLMModel", "reference"], "label": "core.module.input.label.aiModel", "valueType": "string", "llmModelType": "all", "value": "gpt-3.5-turbo"}, {"key": "temperature", "renderTypeList": ["hidden"], "label": "", "value": 0, "valueType": "number", "min": 0, "max": 10, "step": 1}, {"key": "maxToken", "renderTypeList": ["hidden"], "label": "", "value": 2000, "valueType": "number", "min": 100, "max": 4000, "step": 50}, {"key": "systemPrompt", "renderTypeList": ["textarea", "reference"], "max": 3000, "valueType": "string", "label": "core.ai.Prompt", "description": "core.app.tip.systemPromptTip", "placeholder": "core.app.tip.chatNodeSystemPromptTip", "value": ""}, {"key": "history", "renderTypeList": ["numberInput", "reference"], "valueType": "chatHistory", "label": "core.module.input.label.chat history", "description": "最多携带多少轮对话记录", "required": true, "min": 0, "max": 30, "value": 6}, {"key": "userChatInput", "renderTypeList": ["reference", "textarea"], "valueType": "string", "label": "用户问题", "required": true, "value": ["workflowStartNodeId", "userChatInput"]}], "outputs": [{"id": "answerText", "key": "answerText", "label": "core.module.output.label.Ai response content", "description": "core.module.output.description.Ai response content", "valueType": "string", "type": "static"}]}, {"nodeId": "zBxjo5", "name": "获取当前时间", "intro": "获取用户当前时区的时间。", "avatar": "/imgs/workflow/getCurrentTime.svg", "flowNodeType": "pluginModule", "showStatus": false, "position": {"x": 1000, "y": 545}, "version": "481", "inputs": [], "outputs": [{"id": "time", "type": "static", "key": "time", "valueType": "string", "label": "time", "description": ""}], "pluginId": "community-getTime"}], "edges": [{"source": "workflowStartNodeId", "target": "jrWPV9", "sourceHandle": "workflowStartNodeId-source-right", "targetHandle": "jrWPV9-target-left"}, {"source": "jrWPV9", "target": "zBxjo5", "sourceHandle": "selectedTools", "targetHandle": "selectedTools"}], "chatConfig": {"scheduledTriggerConfig": {"cronString": "", "timezone": "Asia/Shanghai", "defaultPrompt": ""}}}}