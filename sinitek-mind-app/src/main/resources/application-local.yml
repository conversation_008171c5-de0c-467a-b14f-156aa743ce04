spring:
  ai:
    openai:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
  elasticsearch:
    rest:
      uris:
        - http://192.168.1.171:9200
      username: elastic
      password: 9r65Ty
  datasource:
    username: root
    password: sinitek
    url: ****************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # 连接池的配置信息
    druid:
      # 初始化大小，最小，最大
      initialSize: 10
      minIdle: 1
      maxActive: 600
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,log4j
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: sinitek-druid
        allow:
      web-stat-filter:
        enabled: true

## 临时的拦截排除，用于快速验证fastgpt的接口改写效果
sinicube:
  interceptors:
    frontend-api:
      excludePatterns:
        - /api/**
        - /mind/**

