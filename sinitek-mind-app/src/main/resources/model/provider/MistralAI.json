{"provider": "MistralAI", "list": [{"model": "ministral-3b-latest", "name": "Ministral-3b-latest", "maxContext": 130000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1.2, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "ministral-8b-latest", "name": "Ministral-8b-latest", "maxContext": 130000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1.2, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "mistral-large-latest", "name": "Mistral-large-latest", "maxContext": 130000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1.2, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "mistral-small-latest", "name": "Mistral-small-latest", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1.2, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}]}