{"provider": "Do<PERSON><PERSON>", "list": [{"model": "Doubao-Seed-1.6", "name": "Doubao-Seed-1.6", "maxContext": 220000, "maxResponse": 16000, "quoteMaxToken": 220000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-Seed-1.6-thinking", "name": "Doubao-Seed-1.6-thinking", "maxContext": 220000, "maxResponse": 16000, "quoteMaxToken": 220000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-Seed-1.6-flash", "name": "Doubao-Seed-1.6-flash", "maxContext": 220000, "maxResponse": 16000, "quoteMaxToken": 220000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-1.5-lite-32k", "name": "Doubao-1.5-lite-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-1.5-pro-32k", "name": "Doubao-1.5-pro-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-1.5-pro-256k", "name": "Doubao-1.5-pro-256k", "maxContext": 256000, "maxResponse": 12000, "quoteMaxToken": 256000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-1.5-vision-pro-32k", "name": "Doubao-1.5-vision-pro-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-lite-4k", "name": "Doubao-lite-4k", "maxContext": 4000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-lite-32k", "name": "Doubao-lite-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "Doubao-lite-128k", "name": "Doubao-lite-128k", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Doubao-vision-lite-32k", "name": "Doubao-vision-lite-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Doubao-pro-4k", "name": "Doubao-pro-4k", "maxContext": 4000, "maxResponse": 4000, "quoteMaxToken": 4000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Doubao-pro-32k", "name": "Doubao-pro-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Doubao-pro-128k", "name": "Doubao-pro-128k", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Doubao-vision-pro-32k", "name": "Doubao-vision-pro-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Doubao-embedding-large", "name": "Doubao-embedding-large", "defaultToken": 512, "maxToken": 4096, "type": "embedding", "normalization": true}, {"model": "Doubao-embedding", "name": "Doubao-embedding", "defaultToken": 512, "maxToken": 4096, "type": "embedding", "normalization": true}]}