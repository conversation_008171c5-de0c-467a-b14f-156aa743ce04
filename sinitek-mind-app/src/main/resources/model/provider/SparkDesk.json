{"provider": "SparkDesk", "list": [{"model": "lite", "name": "SparkDesk-lite", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "generalv3", "name": "SparkDesk-Pro", "maxContext": 8000, "maxResponse": 8000, "quoteMaxToken": 8000, "maxTemperature": 1, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "pro-128k", "name": "SparkDesk-Pro-128k", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 128000, "maxTemperature": 1, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "generalv3.5", "name": "SparkDesk-max", "maxContext": 8000, "maxResponse": 8000, "quoteMaxToken": 8000, "maxTemperature": 1, "vision": false, "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInToolCall": true, "usedInQueryExtension": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "max-32k", "name": "SparkDesk-max-32k", "maxContext": 32000, "maxResponse": 8000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "4.0Ultra", "name": "SparkDesk-v4.0 Ultra", "maxContext": 8000, "maxResponse": 8000, "quoteMaxToken": 8000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}]}