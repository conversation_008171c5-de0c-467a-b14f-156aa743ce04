{"provider": "<PERSON><PERSON>", "list": [{"model": "qwen-max", "name": "<PERSON><PERSON>-max", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen-vl-max", "name": "qwen-vl-max", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "qwen-plus", "name": "<PERSON>wen-plus", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen-vl-plus", "name": "qwen-vl-plus", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "qwen-turbo", "name": "Qwen-turbo", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 1000000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-235b-a22b", "name": "qwen3-235b-a22b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-32b", "name": "qwen3-32b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-30b-a3b", "name": "qwen3-30b-a3b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-14b", "name": "qwen3-14b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-8b", "name": "qwen3-8b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-4b", "name": "qwen3-4b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-1.7b", "name": "qwen3-1.7b", "maxContext": 32000, "maxResponse": 8000, "quoteMaxToken": 30000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen3-0.6b", "name": "qwen3-0.6b", "maxContext": 32000, "maxResponse": 8000, "quoteMaxToken": 30000, "maxTemperature": 1, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwq-plus", "name": "qwq-plus", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": null, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInQueryExtension": false, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}, {"model": "qwq-32b", "name": "qwq-32b", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": null, "vision": false, "reasoning": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInQueryExtension": false, "usedInToolCall": true, "defaultConfig": {"stream": true}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}, {"model": "qwen-coder-turbo", "name": "qwen-coder-turbo", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "qwen2.5-7b-instruct", "name": "qwen2.5-7b-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen2.5-14b-instruct", "name": "qwen2.5-14b-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen2.5-32b-instruct", "name": "qwen2.5-32b-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen2.5-72b-instruct", "name": "Qwen2.5-72B-instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "qwen-long", "name": "qwen-long", "maxContext": 10000000, "maxResponse": 6000, "quoteMaxToken": 10000000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInQueryExtension": false, "usedInToolCall": false, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}, {"model": "text-embedding-v4", "name": "text-embedding-v4", "defaultToken": 512, "maxToken": 8000, "type": "embedding", "defaultConfig": {"dimensions": 1536}}, {"model": "text-embedding-v3", "name": "text-embedding-v3", "defaultToken": 512, "maxToken": 8000, "type": "embedding"}, {"model": "gte-rerank-v2", "name": "gte-rerank-v2", "type": "rerank"}]}