{"provider": "Hunyuan", "list": [{"model": "hunyuan-large", "name": "hunyuan-large", "maxContext": 28000, "maxResponse": 4000, "quoteMaxToken": 20000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-lite", "name": "hunyuan-lite", "maxContext": 250000, "maxResponse": 6000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-pro", "name": "hunyuan-pro", "maxContext": 28000, "maxResponse": 4000, "quoteMaxToken": 28000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-standard", "name": "hunyuan-standard", "maxContext": 32000, "maxResponse": 2000, "quoteMaxToken": 20000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-turbo-vision", "name": "hunyuan-turbo-vision", "maxContext": 6000, "maxResponse": 2000, "quoteMaxToken": 6000, "maxTemperature": 1, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-turbo", "name": "hunyuan-turbo", "maxContext": 28000, "maxResponse": 4000, "quoteMaxToken": 20000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-vision", "name": "hunyuan-vision", "maxContext": 6000, "maxResponse": 2000, "quoteMaxToken": 4000, "maxTemperature": 1, "vision": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "hunyuan-embedding", "name": "hunyuan-embedding", "defaultToken": 512, "maxToken": 1024, "type": "embedding"}]}