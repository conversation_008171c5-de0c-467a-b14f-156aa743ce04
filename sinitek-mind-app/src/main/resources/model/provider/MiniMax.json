{"provider": "MiniMax", "list": [{"model": "MiniMax-Text-01", "name": "MiniMax-Text-01", "maxContext": 1000000, "maxResponse": 1000000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "abab6.5s-chat", "name": "MiniMax-abab6.5s", "maxContext": 245000, "maxResponse": 10000, "quoteMaxToken": 240000, "maxTemperature": 1, "vision": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "speech-01-turbo", "name": "speech-01-turbo", "voices": [{"label": "male-qn-qingse", "value": "male-qn-qingse"}, {"label": "male-qn-jingying", "value": "male-qn-jingying"}, {"label": "male-qn-badao", "value": "male-qn-badao"}, {"label": "male-qn-dax<PERSON><PERSON>g", "value": "male-qn-dax<PERSON><PERSON>g"}, {"label": "female-shaonv", "value": "female-shaonv"}, {"label": "female-yujie", "value": "female-yujie"}, {"label": "female-chengshu", "value": "female-chengshu"}, {"label": "female-tianmei", "value": "female-tianmei"}, {"label": "presenter_male", "value": "presenter_male"}, {"label": "presenter_female", "value": "presenter_female"}, {"label": "audiobook_male_1", "value": "audiobook_male_1"}, {"label": "audiobook_male_2", "value": "audiobook_male_2"}, {"label": "audiobook_female_1", "value": "audiobook_female_1"}, {"label": "audiobook_female_2", "value": "audiobook_female_2"}, {"label": "male-qn-qingse-jingpin", "value": "male-qn-qingse-jingpin"}, {"label": "male-qn-jingying-jingpin", "value": "male-qn-jingying-jingpin"}, {"label": "male-qn-badao-jingpin", "value": "male-qn-badao-jingpin"}, {"label": "male-qn-daxuesheng-jingpin", "value": "male-qn-daxuesheng-jingpin"}, {"label": "female-shaonv-jingpin", "value": "female-shaonv-jingpin"}, {"label": "female-yujie-jingpin", "value": "female-yujie-jingpin"}, {"label": "female-chengshu-jingpin", "value": "female-chengshu-jingpin"}, {"label": "female-tianmei-jingpin", "value": "female-tianmei-jingpin"}, {"label": "clever_boy", "value": "clever_boy"}, {"label": "cute_boy", "value": "cute_boy"}, {"label": "lovely_girl", "value": "lovely_girl"}, {"label": "cartoon_pig", "value": "cartoon_pig"}, {"label": "bin<PERSON><PERSON><PERSON>_didi", "value": "bin<PERSON><PERSON><PERSON>_didi"}, {"label": "jun<PERSON>_nanyou", "value": "jun<PERSON>_nanyou"}, {"label": "chun<PERSON>_xuedi", "value": "chun<PERSON>_xuedi"}, {"label": "lengdan_xiongzhang", "value": "lengdan_xiongzhang"}, {"label": "badao_shaoye", "value": "badao_shaoye"}, {"label": "tianxin_xiaoling", "value": "tianxin_xiaoling"}, {"label": "qiaopi_mengmei", "value": "qiaopi_mengmei"}, {"label": "wumei_yujie", "value": "wumei_yujie"}, {"label": "diadia_xue<PERSON>", "value": "diadia_xue<PERSON>"}, {"label": "danya_xuejie", "value": "danya_xuejie"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON>"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "<PERSON>", "value": "<PERSON>"}, {"label": "<PERSON><PERSON>_<PERSON>", "value": "<PERSON><PERSON>_<PERSON>"}, {"label": "Cha<PERSON>_<PERSON>", "value": "Cha<PERSON>_<PERSON>"}, {"label": "Sweet_Girl", "value": "Sweet_Girl"}, {"label": "Cute_Elf", "value": "Cute_Elf"}, {"label": "Attractive_Girl", "value": "Attractive_Girl"}, {"label": "Serene_Woman", "value": "Serene_Woman"}], "type": "tts"}]}