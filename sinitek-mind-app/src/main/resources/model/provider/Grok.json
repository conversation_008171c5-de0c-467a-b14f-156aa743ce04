{"provider": "Grok", "list": [{"model": "grok-3-mini", "name": "grok-3-mini", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 128000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "grok-3-mini-fast", "name": "grok-3-mini-fast", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 128000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "grok-3", "name": "grok-3", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 128000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}, {"model": "grok-3-fast", "name": "grok-3-fast", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 128000, "maxTemperature": 1, "showTopP": true, "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm"}]}