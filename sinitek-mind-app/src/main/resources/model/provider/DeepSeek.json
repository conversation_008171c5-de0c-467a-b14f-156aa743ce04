{"provider": "DeepSeek", "list": [{"model": "deepseek-chat", "name": "Deepseek-chat", "maxContext": 64000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "showTopP": true, "responseFormatList": ["text", "json_object"], "showStopSign": true, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "type": "llm"}, {"model": "deepseek-reasoner", "name": "<PERSON><PERSON><PERSON>-reasoner", "maxContext": 64000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": null, "vision": false, "reasoning": true, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": false, "showStopSign": false}]}