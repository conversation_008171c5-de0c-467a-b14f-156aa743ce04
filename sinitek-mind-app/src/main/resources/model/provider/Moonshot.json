{"provider": "Moonshot", "list": [{"model": "moonshot-v1-8k", "name": "moonshot-v1-8k", "maxContext": 8000, "maxResponse": 4000, "quoteMaxToken": 6000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "moonshot-v1-32k", "name": "moonshot-v1-32k", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "moonshot-v1-128k", "name": "moonshot-v1-128k", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "moonshot-v1-8k-vision-preview", "name": "moonshot-v1-8k-vision-preview", "maxContext": 8000, "maxResponse": 4000, "quoteMaxToken": 6000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "moonshot-v1-32k-vision-preview", "name": "moonshot-v1-32k-vision-preview", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}, {"model": "moonshot-v1-128k-vision-preview", "name": "moonshot-v1-128k-vision-preview", "maxContext": 128000, "maxResponse": 4000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true, "responseFormatList": ["text", "json_object"]}]}