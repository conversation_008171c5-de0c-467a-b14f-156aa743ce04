{"provider": "Gemini", "list": [{"model": "gemini-2.5-pro-exp-03-25", "name": "gemini-2.5-pro-exp-03-25", "maxContext": 1000000, "maxResponse": 63000, "quoteMaxToken": 1000000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-2.5-flash-preview-04-17", "name": "gemini-2.5-flash-preview-04-17", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-2.0-flash", "name": "gemini-2.0-flash", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-2.0-pro-exp", "name": "gemini-2.0-pro-exp", "maxContext": 2000000, "maxResponse": 8000, "quoteMaxToken": 100000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-1.5-flash", "name": "gemini-1.5-flash", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-1.5-pro", "name": "gemini-1.5-pro", "maxContext": 2000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-2.0-flash-exp", "name": "gemini-2.0-flash-exp", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-2.0-flash-thinking-exp-1219", "name": "gemini-2.0-flash-thinking-exp-1219", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-2.0-flash-thinking-exp-01-21", "name": "gemini-2.0-flash-thinking-exp-01-21", "maxContext": 1000000, "maxResponse": 8000, "quoteMaxToken": 60000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "gemini-exp-1206", "name": "gemini-exp-1206", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 120000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "text-embedding-004", "name": "text-embedding-004", "defaultToken": 512, "maxToken": 2000, "type": "embedding"}]}