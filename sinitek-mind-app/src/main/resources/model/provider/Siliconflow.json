{"provider": "Siliconflow", "list": [{"model": "Qwen/Qwen2.5-72B-Instruct", "name": "Qwen/Qwen2.5-72B-Instruct", "maxContext": 128000, "maxResponse": 8000, "quoteMaxToken": 50000, "maxTemperature": 1, "vision": false, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "Qwen/Qwen2-VL-72B-Instruct", "name": "Qwen/Qwen2-VL-72B-Instruct", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "censor": false, "vision": true, "datasetProcess": false, "usedInClassify": false, "usedInExtractFields": false, "usedInToolCall": false, "toolChoice": false, "functionCall": false, "defaultSystemChatPrompt": "", "defaultConfig": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "deepseek-ai/DeepSeek-V2.5", "name": "deepseek-ai/DeepSeek-V2.5", "maxContext": 32000, "maxResponse": 4000, "quoteMaxToken": 32000, "maxTemperature": 1, "vision": true, "toolChoice": true, "functionCall": false, "defaultSystemChatPrompt": "", "datasetProcess": true, "usedInClassify": true, "usedInExtractFields": true, "usedInQueryExtension": true, "usedInToolCall": true, "defaultConfig": {}, "fieldMap": {}, "type": "llm", "showTopP": true, "showStopSign": true}, {"model": "BAAI/bge-m3", "name": "BAAI/bge-m3", "defaultToken": 512, "maxToken": 8000, "type": "embedding"}, {"model": "FunAudioLLM/CosyVoice2-0.5B", "name": "FunAudioLLM/CosyVoice2-0.5B", "voices": [{"label": "alex", "value": "FunAudioLLM/CosyVoice2-0.5B:alex"}, {"label": "anna", "value": "FunAudioLLM/CosyVoice2-0.5B:anna"}, {"label": "bella", "value": "FunAudioLLM/CosyVoice2-0.5B:bella"}, {"label": "benjamin", "value": "FunAudioLLM/CosyVoice2-0.5B:benjamin"}, {"label": "charles", "value": "FunAudioLLM/CosyVoice2-0.5B:charles"}, {"label": "claire", "value": "FunAudioLLM/CosyVoice2-0.5B:claire"}, {"label": "david", "value": "FunAudioLLM/CosyVoice2-0.5B:david"}, {"label": "diana", "value": "FunAudioLLM/CosyVoice2-0.5B:diana"}], "type": "tts"}, {"model": "RVC-Boss/GPT-SoVITS", "name": "RVC-Boss/GPT-SoVITS", "voices": [{"label": "alex", "value": "RVC-Boss/GPT-SoVITS:alex"}, {"label": "anna", "value": "RVC-Boss/GPT-SoVITS:anna"}, {"label": "bella", "value": "RVC-Boss/GPT-SoVITS:bella"}, {"label": "benjamin", "value": "RVC-Boss/GPT-SoVITS:benjamin"}, {"label": "charles", "value": "RVC-Boss/GPT-SoVITS:charles"}, {"label": "claire", "value": "RVC-Boss/GPT-SoVITS:claire"}, {"label": "david", "value": "RVC-Boss/GPT-SoVITS:david"}, {"label": "diana", "value": "RVC-Boss/GPT-SoVITS:diana"}], "type": "tts"}, {"model": "fishaudio/fish-speech-1.5", "name": "fish-speech-1.5", "voices": [{"label": "alex", "value": "fishaudio/fish-speech-1.5:alex"}, {"label": "anna", "value": "fishaudio/fish-speech-1.5:anna"}, {"label": "bella", "value": "fishaudio/fish-speech-1.5:bella"}, {"label": "benjamin", "value": "fishaudio/fish-speech-1.5:benjamin"}, {"label": "charles", "value": "fishaudio/fish-speech-1.5:charles"}, {"label": "claire", "value": "fishaudio/fish-speech-1.5:claire"}, {"label": "david", "value": "fishaudio/fish-speech-1.5:david"}, {"label": "diana", "value": "fishaudio/fish-speech-1.5:diana"}], "type": "tts"}, {"model": "FunAudioLLM/SenseVoiceSmall", "name": "FunAudioLLM/SenseVoiceSmall", "type": "stt"}, {"model": "BAAI/bge-reranker-v2-m3", "name": "BAAI/bge-reranker-v2-m3", "type": "rerank"}]}