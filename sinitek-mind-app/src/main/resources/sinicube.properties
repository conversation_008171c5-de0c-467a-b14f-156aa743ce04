#\u662F\u5426\u52A0\u5165\u5FAE\u670D\u52A1\u4E2D.
sirm.cloud.enable = false
#\u5FAE\u670D\u52A1\u8C03\u7528\uFF0C\u9700\u8981\u5B89\u5168\u9A8C\u8BC1\u7684\u62E6\u622A\u8DEF\u5F84
sirm.security.path=/frontend/api/**
#\u5FAE\u670D\u52A1\u8C03\u7528\uFF0C\u9700\u8981\u5B89\u5168\u9A8C\u8BC1\u7684\u6392\u9664\u8DEF\u5F84
sirm.security.excludepath=/frontend/api/security/public-key,/frontend/api/properties,/frontend/api/login,/frontend/api/captcha,/frontend/api/ue/exec,/frontend/api/user/current,/hystrix,/service/logon.action

appid=sirm

# \u662F\u5426\u5F00\u542F\u591A\u79DF\u6237\u6A21\u5F0F
isMultiTenant=false

# \u7528\u6237\u8EAB\u4EFD\u9A8C\u8BC1\u6A21\u5F0F(0=\u672C\u5730\uFF08\u9ED8\u8BA4\uFF091=\u8868\u5355\u9A8C\u8BC1 2=LDAP\u9A8C\u8BC1\u65B9\u5F0F 3=\u81EA\u5B9A\u4E49\u9A8C\u8BC1\u65B9\u5F0F)
usercheckmode = 0
# \u5982\u679Cusercheckmode = 3 \u90A3\u4E48usercheckclassname \u4E3A\u5FC5\u586B
usercheckclassname= com.sinitek.sirm.framework.um.SimpleUserChecker
language.default = zh_cn
#\u5F00\u542F\u56FD\u9645\u5316\u652F\u6301
language.enable = false
#\u662F\u5426\u538B\u7F29js\u548Ccss   enable=true(\u538B\u7F29)\u3001enable=false(\u4E0D\u538B\u7F29)
compress.enable=false
#\u9ED8\u8BA4\u4E3B\u9898\uFF1A\u53EF\u9009default\u548Cblue\u548Corange
defaulttheme=2014style

#\u767B\u5F55\u8D85\u65F6\u7684\u65F6\u95F4 \uFF08\u5206\u949F\uFF09
userchecktimeout=120

#\u5FC5\u586B  \u7BA1\u7406\u5458\u8D26\u53F7\u540D
#ldap.principal = cn={username},dc=example,dc=com
#\u5FC5\u586B  LDAP\u670D\u52A1\u5668\u5730\u5740
#ldap.host=localhost
#\u5FC5\u586B  LDAP\u7AEF\u53E3\uFF0C\u9ED8\u8BA4389
#ldap.port=389
#\u5FC5\u586B  LDAP\u670D\u52A1\u540D
#ldap.basedn=dc=example,dc=com
#\u5FC5\u586B  \u5E10\u53F7
#ldap.filter=(cn={username})
#\u5FC5\u586B  \u9ED8\u8BA4\u540D\u79F0\u7684\u5C5E\u6027\u540D
#ldap.displaynameattr=cn
#\u53EF\u9009
#ldap.searchbasedn=

#\u9009\u586B\uFF0C\u672C\u5730\u767B\u5F55\u7684\u7528\u6237\u540D
usercheck.loalcheckuser = admin

app.logonurl = /logon.jsp
app.firsturl = /first.jsp
app.js.contextpath = /
#app.logout.redirecturl=

#\u6570\u636E\u540C\u6B65\u670D\u52A1
#\u662F\u5426\u542F\u7528\u6570\u636E\u540C\u6B65\u670D\u52A1
datasyn.enabled=false
#\u6570\u636E\u540C\u6B65id
datasyn.id=debtsystem
#\u6570\u636E\u540C\u6B65\u76EE\u6807\uFF0Cwebservice\uFF0C\u591A\u4E2Awebserviceurl\u4F7F\u7528\u9017\u53F7\u201C,\u201D\u9694\u5F00
datasyn.targets=http://***********:8081/sirmapp/services/DataSynReceiveService
#datasyn.targets=http://*************:8080/sirmapp/services/DataSynReceiveService

sso.enable=false
sso.mainhost=false
#sso.sessionid=sirmapp


#\u662F\u5426\u542F\u7528\u8DE8\u7AD9\u70B9\u8BF7\u6C42\u9A8C\u8BC1
security.csrf.enable=false


qrtz.loadfrom=db,local

#\u767B\u5F55\u662F\u5426\u4F7F\u7528\u9A8C\u8BC1\u7801
logon.code = false
#\u767B\u5F55\u9A8C\u8BC1\u7801\u6A21\u5F0F, \u8FD9\u4E2A\u53C2\u6570\u53EA\u6709\u5728logon.code = true \u7684\u65F6\u5019\u624D\u6709\u6548\uFF0C 0\u4E3A\u666E\u901A\u65B9\u5F0F\uFF0C 1\u4E3A\u5F53\u9519\u8BEF N\u6B21\u4EE5\u540E\uFF0C\u540E\u7EED\u767B\u5F55\u9700\u8981\u8F93\u5165\u9A8C\u8BC1\u7801\u3002
logon.code.mode = 0
#\u662F\u5426\u5F00\u542F\u81EA\u52A8\u767B\u5F55
logon.rememberMe = true
#\u5B58\u50A8sessionid\u7684CookieName
sessionid.cookie.name=accesstoken
#session\u8FC7\u671F\u65F6\u95F4(\u7528\u6237\u767B\u9646\u8FC7\u671F\u65F6\u95F4)
session.timeout=1800000
#\u5B58\u50A8rememberMe\u7684CookieName
rememberMe.cookie.name=rememberMe
#rememberMe\u8FC7\u671F\u65F6\u95F4(\u81EA\u52A8\u767B\u9646\u8FC7\u671F\u65F6\u95F4)
rememberMe.timeout=31536000
#rememberMe\u503C\u7684\u52A0\u89E3\u5BC6Key
rememberMe.cipherKey=Vn3p+XWDvHbtKvXXYenifw==

#\u52A0\u5BC6\u5BC6\u7801
despassword=sirm2012
#\u52A0\u5BC6\u6709\u6548\u65F6\u95F4
destimeout=120

#\u662F\u5426\u542F\u7528cookie\u5B89\u5168\u6027\u68C0\u67E5
checker.cookie = true

#\u7528\u6237\u5BC6\u7801\u662F\u5426\u9700\u8981\u590D\u6742\u52A0\u5BC6
security.usrpwdsalt.enabled = false

#\u7528\u6237\u5BC6\u7801\u590D\u6742\u52A0\u5BC6\u7684\u79D8\u94A5\u503C(security.usrpwdsalt.enabled\u8BBE\u7F6E\u4E3Atrue\u7684\u65F6\u5019\u6709\u6548\uFF0C\u9ED8\u8BA4\u4E3A\u201Dsirm\u201D)
security.userpwdsalt.value = sirm

#\u652F\u6301\u672C\u5730\u5173\u95ED\u5B9A\u65F6\u4EFB\u52A1(\u53D6\u503C\u6709true(\u9ED8\u8BA4)|false\uFF0C\u5F53quartz.enabled=false\u65F6\uFF0C\u5B9A\u65F6\u4EFB\u52A1\u4E0D\u751F\u6548)
quartz.enabled  = true

#\u652F\u6301\u672C\u5730\u6307\u5B9A\u4E0D\u540C\u7684qrtz_\u8868\u524D\u7F00(\u76EE\u524D\u652F\u6301qrtz_(\u9ED8\u8BA4)\uFF0C\u540E\u7EED\u53EF\u80FD\u4F1A\u65B0\u589E)
quartz.prefix = qrtz_

#\u662F\u5426\u542F\u7528\u96C6\u7FA4\u767B\u9646\u6A21\u5F0F
cluster.enable=false

#\u652F\u6301\u4E0D\u540C\u7684\u6570\u636E\u5E93\uFF1Aorcale,mysql,sqlserver
db.type=mysql
#sqlserver\u7684dbuser
#db.dbuser=dbo

#\u9ED8\u8BA4\u7684aes.key
aes.key=sirmpasswordcryp

iframe.sameorigin.enable=true

#\u662F\u5426\u72EC\u7ACB\u8FD0\u884C\u5DE5\u4F5C\u6D41
sinicube.workflow.dependent.enable=true

# Sql\u62E6\u622A\u5668\u6392\u9664\u7684url
#sql.interceptor.excludes=

#table\u9ED8\u8BA4\u9650\u5236\u6700\u5927\u5BFC\u51FA\u884C\u6570
table.export.maxrow = 10000

# \u516C\u79C1\u94A5,\u5F53Redis\u6302\u6389\u65F6\uFF0C\u4E34\u65F6\u4F7F\u7528\u7684\u516C\u79C1\u94A5
rsa.privateKey=MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMJvyftn+1AKE3KaXzGYpS4yuZyKHkO8liF3IpcINJFLWXI7yiIBLWWbFLbgwnV/aJV9iJmUQ6sjS2pw9fmxAvoDP+4uHJlAaUkzVHZOyW/mHaCdmREtEoq1PXo5ZJyKnK9Xs0B7wrUPjeyTUSrFZsyGm4gMJSEdPhriauuyzzPNAgMBAAECgYEAv84bSECZL6ng6WrTgU99kwdDBuNRW6zLxROLMcZMZRAZmpDUo5rZt6O2WXl7GwGmn+GIQUh7QHW+za/FVp2BunsT1qYgIXtRneocNNvhk668Y9vHzF1p0j6ThXFHf1aHQTZC7hTbKzqc7ju98JAx+EThB1XE8A9gLBNj4Dz9sEECQQDukED32WPKXhuMReMj99sRNijwOi/GZSaiSmaseUZ7NSOFUMiBysS4MCJeQFWUupdPLXtY6IQkfwWO40NcKipFAkEA0KXhlY47gYPH0OEzKHLxvB+hdc7tCb2OXOlJp13uC2MMhEX9EMuxHsfKOHBVaEopa8HahsqEQumIy5lBnkT/6QJAXF5QOWbbg3xikDJzGvcZxBVDTphI7Tk29zl1faxLROMzSKgZql93QZWpiPLB8B86/LYwfyEwIO1pmoSWDTZqPQJAAaLrbALbkJ55+LdrcUvFfZY56/Sdg1ALR8tEmp1v+oLiXD84RuNyFi229k5bPSAnxwKUNi5sbyHpwr8G9rjP0QJBAKjZBywSFSdcamDoLXkKXLwmv9pz/uLmPveAqVR+YnVhelUQMUcBqBy4zqRCAdYxbb0wjVLSvTDIa8lSefuzO8o=
rsa.publicKey=MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDCb8n7Z/tQChNyml8xmKUuMrmcih5DvJYhdyKXCDSRS1lyO8oiAS1lmxS24MJ1f2iVfYiZlEOrI0tqcPX5sQL6Az/uLhyZQGlJM1R2Tslv5h2gnZkRLRKKtT16OWScipyvV7NAe8K1D43sk1EqxWbMhpuIDCUhHT4a4mrrss8zzQIDAQAB

# \u914D\u7F6E\u6587\u4EF6\u6307\u5B9A \u4E34\u65F6\u6587\u4EF6\u8DEF\u5F84   \u6CE8: \u6307\u5B9A\u4E34\u65F6\u6587\u4EF6\u8DEF\u5F84\u4E3AJava\u9ED8\u8BA4\u7684\u76EE\u5F55,\u4F7F\u591A\u4E2A\u64CD\u4F5C\u7CFB\u7EDF\u80FD\u517C\u5BB9\u8FD0\u884C
localsettingpriority=true
setting.tempdir=${java.io.tmpdir}