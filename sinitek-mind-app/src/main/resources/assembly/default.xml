<?xml version="1.0" encoding="UTF-8"?>
<assembly xmlns="http://maven.apache.org/ASSEMBLY/2.1.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/ASSEMBLY/2.1.0 http://maven.apache.org/xsd/assembly-2.1.0.xsd">
    <!-- 可自定义，这里指定的是项目环境 -->
    <!-- spring-boot-assembly-local-1.0.RELEASE.tar.gz  -->
    <id>${profileActive}-${project.version}</id>

    <!-- 打包的类型，如果有N个，将会打N个类型的包 -->
    <formats>
        <format>tar.gz</format>
    </formats>

    <includeBaseDirectory>true</includeBaseDirectory>
    <fileSets>
        <!-- 将项目启动jar打包到boot目录中 -->
        <fileSet>
            <directory>${build.directory}</directory>
            <outputDirectory>boot</outputDirectory>
            <fileMode>0755</fileMode>
            <includes>
                <include>${project.build.finalName}.jar</include>
            </includes>
        </fileSet>

        <!-- 将配置文件打包到config目录中 -->
        <fileSet>
            <directory>${build.outputDirectory}</directory>
            <outputDirectory>config</outputDirectory>
            <fileMode>0644</fileMode>
            <includes>
                <include>application.properties</include>
                <include>application.yml</include>
                <include>application-${profileActive}.yml</include>
                <include>application-${profileActive}.properties</include>
                <include>bootstrap.yml</include>
                <include>log4j2.xml</include>
                <include>sinicube.properties</include>
            </includes>
        </fileSet>

        <!--
            0755->即用户具有读/写/执行权限，组用户和其它用户具有读写权限；
            0644->即用户具有读写权限，组用户和其它用户具有只读权限；
        -->
        <!-- 将src/bin目录下的所有文件输出到打包后的bin目录中 -->
        <fileSet>
            <directory>${build.outputDirectory}/assembly/bin</directory>
            <outputDirectory>bin</outputDirectory>
            <includes>
                <include>*.sh</include>
            </includes>
            <fileMode>0755</fileMode>
            <lineEnding>unix</lineEnding>
        </fileSet>

        <!-- 默认创建一个空的logs目录 -->
        <fileSet>
            <directory>${build.outputDirectory}/assembly/logs</directory>
            <outputDirectory>logs</outputDirectory>
            <fileMode>0644</fileMode>
            <excludes>
                <exclude>*</exclude>
            </excludes>
        </fileSet>

        <!-- 包含根目录下的使用说明 -->
        <fileSet>
            <directory>${basedir}/src/main/resources/assembly</directory>
            <outputDirectory>./</outputDirectory>
            <includes>
                <include>使用说明(必读).txt</include>
            </includes>
        </fileSet>

        <!-- 将项目依赖打包到lib目录 -->
        <fileSet>
            <directory>${build.directory}/lib</directory>
            <outputDirectory>lib</outputDirectory>
            <fileMode>0755</fileMode>
        </fileSet>
    </fileSets>
</assembly>