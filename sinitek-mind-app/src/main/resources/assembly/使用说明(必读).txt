# 使用说明

## 目录说明

* bin命令: 系统运行命令目录
  * startup.sh: 启动命令
  * shutdown.sh: 停止命令
  * restart.sh: 重启命令
* boot目录: spring boot 可执行jar包
* config目录: 配置文件目录 
* lib目录: 依赖的所有jar包
* logs目录: 系统日志目录，默认该目录为空，但通过startup.sh启动应用后日志将会写入到此处
    * logs/startup.log: 记录启动日志
    * logs/back目录: 项目运行日志备份目录

## 如何启动与停止

解压安装包后，cd进入bin目录

**启动:**

```shell
./startup.sh
```

**停止:**

```shell
./shutdown.sh
```

## 场景

### 如何增加一个Java Option配置

**举例:** 运维需要配置系统连接redis的密码(该密码只有运维人员知道)，已明确redis密码使用spring.redis.password属性

* 一、打开bin/startup.sh，找到**JAVA_OPT=**
* 二、在以后增加一行如下，配置spring.redis.password
```shell
JAVA_OPT="${JAVA_OPT} -Dspring.redis.password=***"
```