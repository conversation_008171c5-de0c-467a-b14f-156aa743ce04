#! /bin/bash
#======================================================================
# 项目启动shell脚本
# boot目录: spring boot jar包
# config目录: 配置文件目录
# logs目录: 项目运行日志目录
# logs/startup.log: 记录启动日志
# logs/back目录: 项目运行日志备份目录
# nohup后台运行

#======================================================================

# 项目名称
APPLICATION="@project.name@"

# 项目启动jar包名称
APPLICATION_JAR="@build.finalName@.jar"

# bin目录绝对路径
BIN_PATH=$(cd `dirname $0`; pwd)
# 进入bin目录
cd `dirname $0`
# 返回到上一级项目根目录路径
cd ..
# 打印项目根目录绝对路径
# `pwd` 执行系统命令并获得结果
BASE_PATH=`pwd`

# 外部配置文件绝对目录,如果是目录需要/结尾，也可以直接指定文件
# 如果指定的是目录,spring则会读取目录中的所有配置文件
CONFIG_DIR=${BASE_PATH}"/config/"

# 项目日志输出绝对路径
LOG_DIR=${BASE_PATH}"/logs"
LOG_FILE="${APPLICATION}-stdout.log"
LOG_PATH="${LOG_DIR}/${LOG_FILE}"
# 日志备份目录
LOG_BACK_DIR="${LOG_DIR}/back/"

# 项目启动日志输出绝对路径
LOG_STARTUP_PATH="${LOG_DIR}/startup.log"

# 当前时间
NOW=`date +'%Y-%m-%d-%H-%M-%S'`
NOW_PRETTY=`date +'%Y-%m-%d %H:%M:%S'`

# 启动日志
STARTUP_LOG="================================================ ${NOW_PRETTY} ================================================\n"

if [ -z "$JAVA_HOME" ]; then
   STARTUP_LOG="$STARTUP_LOG JAVA_HOME is not set,please set environment variable!"
fi
# 如果logs文件夹不存在,则创建文件夹
if [[ ! -d "${LOG_DIR}" ]]; then
  mkdir "${LOG_DIR}"
fi

# 如果logs/back文件夹不存在,则创建文件夹
if [[ ! -d "${LOG_BACK_DIR}" ]]; then
  mkdir "${LOG_BACK_DIR}"
fi

# 如果项目运行日志存在,则重命名备份
if [[ -f "${LOG_PATH}" ]]; then
	mv ${LOG_PATH} "${LOG_BACK_DIR}/${APPLICATION}_back_${NOW}.log"
fi

# 创建新的项目运行日志
echo "" > ${LOG_PATH}

# 如果项目启动日志不存在,则创建,否则追加
#echo "${STARTUP_LOG}" >> ${LOG_STARTUP_PATH}

#==========================================================================================
# JVM Configuration
# -Xmx2048m:设置JVM最大可用内存为2048m,根据项目实际情况而定，建议最小和最大设置成一样。
# -Xms512m:设置JVM初始内存。此值可以设置与-Xmx相同,以避免每次垃圾回收完成后JVM重新分配内存
# -Xmn1024m:设置年轻代大小为1024m。整个JVM内存大小=年轻代大小 + 年老代大小 + 持久代大小。
#          持久代一般固定大小为64m,所以增大年轻代,将会减小年老代大小。此值对系统性能影响较大,Sun官方推荐配置为整个堆的3/8
# -XX:MetaspaceSize=64m:存储class的内存大小,该值越大触发Metaspace GC的时机就越晚
# -XX:MaxMetaspaceSize=320m:限制Metaspace增长的上限，防止因为某些情况导致Metaspace无限的使用本地内存，影响到其他程序
# -XX:-OmitStackTraceInFastThrow:解决重复异常不打印堆栈信息问题
#==========================================================================================
# 4G物理内存,内存出现瓶颈时，可以使用  -Xms3096m -Xmx3096m -Xmn1161m
#JAVA_OPT="-server -Xms2048m -Xmx2048m -Xmn768m -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=256m"
#当前配置为2G物理内存的配置
JAVA_OPT="-Xms4096m -Xmx4096m -Xmn384m -XX:MetaspaceSize=64m -XX:MaxMetaspaceSize=2048m"

JAVA_OPT="${JAVA_OPT} -Dloader.path=../lib"
JAVA_OPT="${JAVA_OPT} -Duser.timezone=GMT+08"
JAVA_OPT="${JAVA_OPT} -Dcatalina.home=${BASE_PATH}"
JAVA_OPT="${JAVA_OPT} -Dspring.profiles.active=@profileActive@"
JAVA_OPT="${JAVA_OPT} -Dspring.config.location=${CONFIG_DIR}"
JAVA_OPT="${JAVA_OPT} -Dlogging.config=${CONFIG_DIR}log4j2.xml"

#=======================================================
# 将命令启动相关日志追加到日志文件
#=======================================================

# 输出项目名称
STARTUP_LOG="${STARTUP_LOG}application name: ${APPLICATION}\n"
# 输出jar包名称
STARTUP_LOG="${STARTUP_LOG}application jar  name: ${APPLICATION_JAR}\n"
# 输出项目根目录
STARTUP_LOG="${STARTUP_LOG}application root path: ${BASE_PATH}\n"
# 输出项目bin路径
STARTUP_LOG="${STARTUP_LOG}application bin  path: ${BIN_PATH}\n"
# 输出项目config路径
STARTUP_LOG="${STARTUP_LOG}application config path: ${CONFIG_DIR}\n"
# 打印日志路径
STARTUP_LOG="${STARTUP_LOG}application log  path: ${LOG_PATH}\n"
# 打印JVM配置
STARTUP_LOG="${STARTUP_LOG}application JAVA_OPT : ${JAVA_OPT}\n"


# 打印启动命令
STARTUP_LOG="${STARTUP_LOG}application startup command: nohup java ${JAVA_OPT}  -jar     ${BASE_PATH}/boot/${APPLICATION_JAR}   > ${LOG_PATH} 2>&1 &\n"


#======================================================================
# 执行启动命令：后台启动项目,并将日志输出到项目根目录下的logs文件夹下
#======================================================================
nohup ${JAVA_HOME}/bin/java  ${JAVA_OPT}  -jar ${BASE_PATH}/boot/${APPLICATION_JAR} -> ${LOG_PATH} 2>&1 &


# 进程ID
PID=$(ps -ef | grep "${APPLICATION_JAR}" | grep -v grep | awk '{ print $2 }')
STARTUP_LOG="${STARTUP_LOG}application pid: ${PID}\n"
STARTUP_LOG="${STARTUP_LOG}${APPLICATION} is  starting,see log(${LOG_PATH}) for details.\n"


# 启动日志追加到启动日志文件中
echo -e ${STARTUP_LOG} >> ${LOG_STARTUP_PATH}
# 打印启动日志
echo -e ${STARTUP_LOG}

if [ "$1" != "nolog" ]; then
    # 打印项目日志
    echo -e "Press ctrl+c to stop  terminal log printing.\n"
    tail -f ${LOG_PATH}
fi

