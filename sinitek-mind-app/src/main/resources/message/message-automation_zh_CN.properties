# æµç¨ç¼ææ¶æ¯ç è§å
# 30-00-0000
# [æµç¨ç¼æåºå®30-æ¨¡å-éè¯¯ç ]

# 01 - audit
# 02 - common
# 03 - execution
# 04 - node
# 05 - workflow

30050001=æµç¨ç¼æä¸å­å¨
30050002=æµç¨ç¼æJSONåºååå¤±è´¥
30050003=åæ°ä¸è½ä¸ºç©º
30050004=æµç¨IDæ æ
30050005=æµç¨æ°æ®ä¸è½ä¸ºç©º
30050006=åæ°ç±»åè½¬æ¢å¤±è´¥
30050007=metaä¿¡æ¯ä¸è½ä¸ºç©º
30050008=positionæ°æ®ä¸è½ä¸ºç©º
30050009=èç¹ä¿¡æ¯ä¸è½ä¸ºç©º
30050010=èç¹éç½®JSONåºååå¤±è´¥
30050011=åå¸å¤±è´¥
30050012=æ´æ°å¤±è´¥
30050013=åºç¨åå²å¤±è´¥
30050014=çæå¤±è´¥
30050015=èç¹ä¸å­å¨
30050016=èç¹ç±»åä¸å­å¨
30050017=è¯¥ç±»åå·²è¢«èç¹ä½¿ç¨ï¼ä¸å¯å é¤
30050018=è¿æ¥çº¿ä¸å­å¨
30050019=æµç¨ç¼æä¿å­å¤±è´¥
30050020=èç¹IDä¸è½ä¸ºç©º