cacheManager: redisCacheManager
logon:
  code: false
sirm:
  encrypt:
    signDebug: true
    debug: true

spring:
  ai:
    ## milvus向量数据库
    vectorstore:
      milvus:
        client:
          host: "**************"
          port: 47377
    openai:
      base-url: https://api.deepseek.com
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
    mcp:
      client:
        enabled: false
        name: "sinitek-mind-mcp-client"
        version: 1.0.0
        request-timeout: 30s
        type: async
        sse:
          connections:
            sinitek-mind-server:
              url: "http://abc-url"
  cloud:
    nacos:
      discovery:
        enabled: false

  redis:
    redisson:
      config: |
        singleServerConfig:
          address: "redis://**************:6379"
          password: mypassword
          database: 6
          timeout: 10000
          connectionMinimumIdleSize: 10
          connectionPoolSize: 500
        threads: 100
        nettyThreads: 100
        transportMode: "NIO"

  data:
    mongodb:
      uri: ***************************************************************************************************
  datasource:
    username: root
    password: sinitek
    url: *************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource
    # 连接池的配置信息
    druid:
      # 初始化大小，最小，最大
      initialSize: 10
      minIdle: 1
      maxActive: 600
      # 配置获取连接等待超时的时间
      maxWait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      timeBetweenEvictionRunsMillis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      minEvictableIdleTimeMillis: 300000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      poolPreparedStatements: true
      maxPoolPreparedStatementPerConnectionSize: 20
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,log4j
      stat-view-servlet:
        enabled: true
        loginUsername: admin
        loginPassword: sinitek-druid
        allow:
      web-stat-filter:
        enabled: true

sinicube:
  cookie:
    secure: false
  business-log:
    mode: db
  # # 子系统相关参数
  # subsystem:
  #   # 子前端系统地址
  #   # {host}占位符会被解析为主前端host地址
  #   sub-frontend-address:
  #     - http://*************:32287/
  api-security:
    ## 默认值为false
    enable: false
  exception:
    ## 是否打印BussinessException的报错日志,默认为false
    business-print-log: false
  user:
    ## 忘记密码相关配置
    forget-pwd:
      enable: true

  message:
    ## 我的消息是否显示邮件发送方式, 默认为true
    myMsgShowEmail: true
    independent: false
    service-name: CLOUD-SIRMAPP
    email:
      limit:
        ## 邮件发送限流配置
        ## 间隔时间内，最大发送次数
        rate: 30
        ## 间隔时间，单位 秒
        interval: 60
      from-sys: east1000@*************
      smtp-server: *************
      smtpUser: east1033@*************
      smtpPwd: 123
    ## 发送消息队列批次数，默认50
    batchSize: 50
    retry:
      ## 消息发送失败重试配置，是否开启重试，默认开启
      enable: true
      ## 最大重试次数，默认3次
      maxAttempts: 3
  ## 缓存配置
  cache:
    prefix: "sini-mind:"
    defaultCache: maximumSize=1000,expireAfterWrite=86400s
    customizeCache:
      - usersessionCache: maximumSize=800,expireAfterWrite=1800s
      - captchaCache: maximumSize=500,expireAfterWrite=60s
      - keyPairCache: maximumSize=200
      - exportProgressCache: maximumSize=200,expireAfterWrite=1800s
      - verificationCodeCache: maximumSize=1000,expireAfterWrite=300s
      - sendMessageJobCache: maximumSize=100,expireAfterWrite=300s

  minio:
    endpoint: **************
    port: 32000
    accessKey: minioadmin
    secretKey: sinitek123
    secure: false
    bucketName: "sini-mind-test"
    region: cn-shanghai
  attachment:
    #附件存放方式 : db、file、fastDFS、minio
    store-type: minio
  multi-factor-check:
    enable: false
    types:
      - 0
      - 1
    strategy: any
    time-step: 120
  #  身份验证失效安全性配置，配置为true时，用户登录会话过期直接跳转登录页，注：前端框架如果没有轮询后端接口，此配置失效
  user-session-expire-redirection: false
usercheck:
  loalcheckuser: admin

# knife4j相关的配置
knife4j:
  # 开启增强配置
  enable: true
  basic:
    enable: true
    username: admin
    password: sinitek-swagger


# actuator暴露监控端点
management:
  endpoints:
    enabled-by-default: false #关闭监控
    web:
      exposure:
        include: '*'
