{"author": "", "name": "博查搜索", "avatar": "core/workflow/template/bocha", "intro": "使用博查AI搜索引擎进行网络搜索。", "showStatus": true, "weight": 10, "courseUrl": "", "isTool": true, "templateType": "search", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "workflow:template.plugin_start", "intro": "workflow:intro_plugin_input", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 636.3048409085379, "y": -238.61714728578016}, "version": "481", "inputs": [{"renderTypeList": ["input"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "description": "博查API密钥", "defaultValue": "", "required": true}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "query", "label": "query", "description": "搜索查询词", "defaultValue": "", "required": true, "toolDescription": "搜索查询词"}, {"renderTypeList": ["select", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "freshness", "label": "freshness", "description": "搜索指定时间范围内的网页。", "defaultValue": "noLimit", "required": false, "toolDescription": "搜索指定时间范围内的网页。", "list": [{"label": "noLimit", "value": "noLimit"}, {"label": "oneDay", "value": "oneDay"}, {"label": "oneWeek", "value": "oneWeek"}, {"label": "oneMonth", "value": "oneMonth"}, {"label": "oneYear", "value": "oneYear"}]}, {"renderTypeList": ["switch", "reference"], "selectedTypeIndex": 0, "valueType": "boolean", "canEdit": true, "key": "summary", "label": "summary", "description": "是否显示文本摘要。", "defaultValue": true, "required": false, "list": []}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "include", "label": "include", "description": "指定搜索的site范围。多个域名使用|或,分隔，最多20个。例如：qq.com|m.163.com", "defaultValue": "", "required": false, "list": []}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "exclude", "label": "exclude", "description": "排除搜索的网站范围。多个域名使用|或,分隔，最多20个。例如：qq.com|m.163.com", "defaultValue": "", "required": false, "list": []}, {"renderTypeList": ["numberInput", "reference"], "selectedTypeIndex": 0, "valueType": "number", "canEdit": true, "key": "count", "label": "count", "description": "返回结果的条数。可填范围：1-50，默认为10", "defaultValue": 10, "required": false, "min": 1, "max": 50, "list": []}], "outputs": [{"id": "<PERSON><PERSON><PERSON><PERSON>", "valueType": "string", "key": "<PERSON><PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON>", "type": "hidden"}, {"id": "query", "valueType": "string", "key": "query", "label": "query", "type": "hidden"}, {"id": "freshness", "valueType": "string", "key": "freshness", "label": "freshness", "type": "hidden"}, {"id": "summary", "valueType": "boolean", "key": "summary", "label": "summary", "type": "hidden"}, {"id": "include", "valueType": "string", "key": "include", "label": "include", "type": "hidden"}, {"id": "exclude", "valueType": "string", "key": "exclude", "label": "exclude", "type": "hidden"}, {"id": "count", "valueType": "number", "key": "count", "label": "count", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "common:core.module.template.self_output", "intro": "workflow:intro_custom_plugin_output", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2289.548741109713, "y": -113.61714728578016}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "arrayObject", "canEdit": true, "key": "result", "label": "result", "isToolOutput": true, "description": "", "value": [["cixTtgyy4gK43lPD", "hb4MQP8oGxPx1f9G"]], "required": true}, {"renderTypeList": ["reference"], "valueType": "object", "canEdit": true, "key": "error", "label": "error", "isToolOutput": true, "description": "", "required": true, "value": ["cixTtgyy4gK43lPD", "error"]}], "outputs": []}, {"nodeId": "pluginConfig", "name": "common:core.module.template.system_config", "intro": "", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "pluginConfig", "position": {"x": 184.66337662472682, "y": -216.05298493910115}, "version": "4811", "inputs": [], "outputs": []}, {"nodeId": "cixTtgyy4gK43lPD", "name": "HTTP 请求#2", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1336.2141952438083, "y": -513.6171472857802}, "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "接收前方节点的输出值作为变量，这些变量可以被 HTTP 请求参数使用。", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectDataset", "selectApp"], "showDescription": false, "showDefaultValue": true}, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "新的 HTTP 请求地址。如果出现两个“请求地址”，可以删除该模块重新加入，会拉取最新的模块配置。", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://api.bochaai.com/v1/web-search", "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [{"key": "Authorization", "type": "string", "value": "Bearer {{$pluginInput.apiKey$}}"}], "label": "", "description": "自定义请求头，请严格填入 JSON 字符串。\n1. 确保最后一个属性没有逗号\n2. 确保 key 包含双引号\n例如：{\"Authorization\":\"Bearer xxx\"}", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n    \"query\": \"{{$pluginInput.query$}}\",\n    \"freshness\": \"{{$pluginInput.freshness$}}\",\n    \"summary\": {{$pluginInput.summary$}},\n    \"include\": \"{{$pluginInput.include$}}\",\n    \"exclude\": \"{{$pluginInput.exclude$}}\",\n    \"count\": {{$pluginInput.count$}}\n}", "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false}], "outputs": [{"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "输出字段提取", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectDataset", "selectApp"], "showDescription": false, "showDefaultValue": false}, "description": "可以通过 JSONPath 语法来提取响应值中的指定字段", "valueDesc": ""}, {"id": "error", "key": "error", "label": "请求错误", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static", "valueDesc": ""}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "原始响应", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static", "valueDesc": ""}, {"id": "hb4MQP8oGxPx1f9G", "valueType": "arrayObject", "type": "dynamic", "key": "$.data.webPages.value", "label": "$.data.webPages.value"}]}], "edges": [{"source": "pluginInput", "target": "cixTtgyy4gK43lPD", "sourceHandle": "pluginInput-source-right", "targetHandle": "cixTtgyy4gK43lPD-target-left"}, {"source": "cixTtgyy4gK43lPD", "target": "pluginOutput", "sourceHandle": "cixTtgyy4gK43lPD-source-right", "targetHandle": "pluginOutput-target-left"}], "chatConfig": {"variables": [], "_id": "67bd78aeebcd3c6700e82e5e", "questionGuide": {"open": false, "model": "gpt-4o-mini", "customPrompt": ""}, "ttsConfig": {"type": "web"}, "whisperConfig": {"open": false, "autoSend": false, "autoTTSResponse": false}, "chatInputGuide": {"open": false, "textList": [], "customUrl": ""}, "instruction": "", "autoExecute": {"open": false, "defaultPrompt": ""}, "welcomeText": ""}}, "chatConfig": {}}