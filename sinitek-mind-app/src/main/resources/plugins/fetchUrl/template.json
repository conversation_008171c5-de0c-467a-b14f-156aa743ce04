{"author": "", "name": "网页内容抓取", "avatar": "core/workflow/template/fetchUrl", "intro": "可获取一个网页链接内容，并以 Markdown 格式输出，仅支持获取静态网站。", "showStatus": true, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "lmpb9v2lo2lk", "name": "插件开始", "intro": "自定义配置外部输入，使用插件时，仅暴露自定义配置的输入", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 487.2485939481787, "y": -159.13661665265613}, "version": "481", "inputs": [{"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "key": "url", "label": "url", "description": "需要读取的网页链接", "required": true, "toolDescription": "需要读取的网页链接", "list": [], "defaultValue": ""}], "outputs": [{"id": "url", "valueType": "string", "key": "url", "label": "url", "type": "hidden"}]}, {"nodeId": "i7uow4wj2wdp", "name": "插件输出", "intro": "自定义配置外部输出，使用插件时，仅暴露自定义配置的输出", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1853.935047606551, "y": -154.13661665265613}, "version": "481", "inputs": [{"key": "result", "valueType": "string", "label": "result", "renderTypeList": ["reference"], "required": false, "description": "", "canEdit": true, "editField": {"key": true, "description": true, "valueType": true}, "value": ["ebLCxU43hHuZ", "rH4tMV02robs"]}], "outputs": []}, {"nodeId": "ebLCxU43hHuZ", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1054.2940501177068, "y": -503.13661665265613}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "common:core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectDataset", "selectApp"], "showDescription": false, "showDefaultValue": true}, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "fetchUrl", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n  \"url\": \"{{url}}\"\n}", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "url", "label": "url", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectDataset", "selectApp"], "showDescription": false, "showDefaultValue": true}, "required": true, "value": ["lmpb9v2lo2lk", "url"]}], "outputs": [{"id": "error", "key": "error", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "workflow:raw_response", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}, {"id": "rH4tMV02robs", "valueType": "string", "type": "dynamic", "key": "result", "label": "result"}]}], "edges": [{"source": "lmpb9v2lo2lk", "target": "ebLCxU43hHuZ", "sourceHandle": "lmpb9v2lo2lk-source-right", "targetHandle": "ebLCxU43hHuZ-target-left"}, {"source": "ebLCxU43hHuZ", "target": "i7uow4wj2wdp", "sourceHandle": "ebLCxU43hHuZ-source-right", "targetHandle": "i7uow4wj2wdp-target-left"}], "chatConfig": {"welcomeText": "", "variables": [], "questionGuide": {"open": false, "model": "gpt-4o-mini", "customPrompt": "You are an AI assistant tasked with predicting the user's next question based on the conversation history. Your goal is to generate 3 potential questions that will guide the user to continue the conversation. When generating these questions, adhere to the following rules:\n\n1. Use the same language as the user's last question in the conversation history.\n2. Keep each question under 20 characters in length.\n\nAnalyze the conversation history provided to you and use it as context to generate relevant and engaging follow-up questions. Your predictions should be logical extensions of the current topic or related areas that the user might be interested in exploring further.\n\nRemember to maintain consistency in tone and style with the existing conversation while providing diverse options for the user to choose from. Your goal is to keep the conversation flowing naturally and help the user delve deeper into the subject matter or explore related topics."}, "ttsConfig": {"type": "web"}, "whisperConfig": {"open": false, "autoSend": false, "autoTTSResponse": false}, "chatInputGuide": {"open": false, "textList": [], "customUrl": ""}, "instruction": "", "autoExecute": {"open": false, "defaultPrompt": ""}, "_id": "677b59849d672185a5671b45"}}}