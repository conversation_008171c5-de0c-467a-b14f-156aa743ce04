{"author": "", "name": "Google搜索", "avatar": "core/workflow/template/google", "intro": "在google中搜索。", "showStatus": true, "weight": 10, "courseUrl": "https://fael3z0zfze.feishu.cn/wiki/Vqk1w4ltNiuLifkHTuoc0hSrnVg?fromScene=spaceOverview", "isTool": true, "templateType": "search", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "workflow:template.plugin_start", "intro": "workflow:intro_plugin_input", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 636.3048409085379, "y": -238.61714728578016}, "version": "481", "inputs": [{"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "cx", "label": "cx", "description": "Google搜索cxID", "defaultValue": "", "list": [{"label": "", "value": ""}], "required": true}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "key", "label": "key", "description": "Google搜索key", "defaultValue": "", "required": true, "list": []}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "query", "label": "query", "description": "查询字段值", "defaultValue": "", "list": [{"label": "", "value": ""}], "required": true, "toolDescription": "查询字段值"}], "outputs": [{"id": "cx", "valueType": "string", "key": "cx", "label": "cx", "type": "hidden"}, {"id": "key", "valueType": "string", "key": "key", "label": "key", "type": "hidden"}, {"id": "query", "valueType": "string", "key": "query", "label": "query", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "common:core.module.template.self_output", "intro": "workflow:intro_custom_plugin_output", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2764.1105686698083, "y": -30.617147285780163}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "object", "canEdit": true, "key": "result", "label": "result", "isToolOutput": true, "description": "", "value": ["pZTkvleFSZXo", "system_rawResponse"]}], "outputs": []}, {"nodeId": "pluginConfig", "name": "common:core.module.template.system_config", "intro": "", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "pluginConfig", "position": {"x": 184.66337662472682, "y": -216.05298493910115}, "version": "4811", "inputs": [], "outputs": []}, {"nodeId": "nyA6oA8mF1iW", "name": "HTTP 请求", "intro": "调用谷歌搜索，查询相关内容", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1335.0647252518884, "y": -455.9043948565971}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "common:core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "GET", "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "https://www.googleapis.com/customsearch/v1", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [{"key": "q", "type": "string", "value": "{{query}}"}, {"key": "cx", "type": "string", "value": "{{$pluginInput.cx$}}"}, {"key": "key", "type": "string", "value": "{{$pluginInput.key$}}"}, {"key": "c2coff", "type": "string", "value": "1"}, {"key": "start", "type": "string", "value": "1"}, {"key": "end", "type": "string", "value": "20"}, {"key": "dateRestrict", "type": "string", "value": "m[1]"}], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false, "debugLabel": "", "toolDescription": ""}, {"valueType": "string", "renderTypeList": ["reference"], "key": "query", "label": "query", "toolDescription": "谷歌搜索检索词", "required": true, "canEdit": true, "editField": {"key": true, "description": true}, "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "value": ["pluginInput", "query"]}], "outputs": [{"id": "error", "key": "error", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "workflow:raw_response", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "editField": {"key": true, "valueType": true}}, {"id": "M5YmxaYe8em1", "type": "dynamic", "key": "prompt", "valueType": "string", "label": "prompt"}]}, {"nodeId": "pZTkvleFSZXo", "name": "代码运行", "intro": "执行一段简单的脚本代码，通常用于进行复杂的数据处理。", "avatar": "core/workflow/template/codeRun", "flowNodeType": "code", "showStatus": true, "position": {"x": 2153.5325687235554, "y": -188.04429852303304}, "version": "482", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "workflow:these_variables_will_be_input_parameters_for_code_execution", "editField": {"key": true, "valueType": true}, "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "debugLabel": "", "toolDescription": ""}, {"key": "codeType", "renderTypeList": ["hidden"], "label": "", "value": "js", "debugLabel": "", "toolDescription": ""}, {"key": "code", "renderTypeList": ["custom"], "label": "", "value": "function main({data}){\n    const result = data.items.map((item) => ({\n      title: item.title,\n      link: item.link,\n      snippet: item.snippet\n    }))\n    return  JSON.stringify(result) \n}", "debugLabel": "", "toolDescription": ""}, {"key": "data", "valueType": "object", "label": "data", "renderTypeList": ["reference"], "description": "", "canEdit": true, "editField": {"key": true, "valueType": true}, "value": ["nyA6oA8mF1iW", "httpRawResponse"], "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}}], "outputs": [{"id": "system_rawResponse", "key": "system_rawResponse", "label": "workflow:full_response_data", "valueType": "object", "type": "static", "description": ""}, {"id": "error", "key": "error", "label": "workflow:execution_error", "description": "代码运行错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "editField": {"key": true, "valueType": true}, "description": "将代码中 return 的对象作为输出，传递给后续的节点"}, {"id": "qLUQfhG0ILRX", "type": "dynamic", "key": "prompt", "valueType": "string", "label": "prompt"}]}], "edges": [{"source": "pluginInput", "target": "nyA6oA8mF1iW", "sourceHandle": "pluginInput-source-right", "targetHandle": "nyA6oA8mF1iW-target-left"}, {"source": "nyA6oA8mF1iW", "target": "pZTkvleFSZXo", "sourceHandle": "nyA6oA8mF1iW-source-right", "targetHandle": "pZTkvleFSZXo-target-left"}, {"source": "pZTkvleFSZXo", "target": "pluginOutput", "sourceHandle": "pZTkvleFSZXo-source-right", "targetHandle": "pluginOutput-target-left"}], "chatConfig": {"welcomeText": "", "variables": [], "questionGuide": {"open": false}, "ttsConfig": {"type": "web"}, "whisperConfig": {"open": false, "autoSend": false, "autoTTSResponse": false}, "chatInputGuide": {"open": false, "textList": [], "customUrl": ""}, "instruction": "", "_id": "6709e90cd9873479ee78fe71"}}}