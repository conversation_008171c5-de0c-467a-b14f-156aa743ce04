{"author": "<PERSON><PERSON><PERSON>", "name": "基础图表", "avatar": "core/workflow/template/baseChart", "intro": "根据数据生成图表，可根据chartType生成柱状图，折线图，饼图", "showStatus": true, "weight": 10, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "common:core.module.template.self_input", "intro": "workflow:intro_plugin_input", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 613.7921798611637, "y": -123.07734867626235}, "version": "481", "inputs": [{"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "title", "label": "title", "description": "BI图表的标题", "defaultValue": "", "list": [{"label": "", "value": ""}], "required": true, "toolDescription": "BI图表的标题"}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "xAxis", "label": "xAxis", "description": "x轴数据，例如：[\"A\", \"B\", \"C\"]", "defaultValue": "", "required": true, "toolDescription": "x轴数据，例如：[\"A\", \"B\", \"C\"]", "list": [{"label": "", "value": ""}]}, {"renderTypeList": ["input", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "yAxis", "label": "yAxis", "description": "y轴数据，例如：[1,2,3]", "defaultValue": "", "list": [{"label": "", "value": ""}], "required": true, "toolDescription": "y轴数据，例如：[1,2,3]"}, {"renderTypeList": ["select", "reference"], "selectedTypeIndex": 0, "valueType": "string", "canEdit": true, "key": "chartType", "label": "chartType", "description": "图表类型：柱状图，折线图，饼图", "defaultValue": "", "required": true, "list": [{"label": "柱状图", "value": "柱状图"}, {"label": "折线图", "value": "折线图"}, {"label": "饼图", "value": "饼图"}], "toolDescription": "图表类型：柱状图，折线图，饼图"}], "outputs": [{"id": "title", "valueType": "string", "key": "title", "label": "title", "type": "hidden"}, {"id": "xAxis", "valueType": "string", "key": "xAxis", "label": "xAxis", "type": "hidden"}, {"id": "yAxis", "valueType": "string", "key": "yAxis", "label": "yAxis", "type": "hidden"}, {"id": "chartType", "valueType": "string", "key": "chartType", "label": "chartType", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "common:core.module.template.self_output", "intro": "workflow:intro_custom_plugin_output", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 2128.8138851197145, "y": -63.52186746137181}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "string", "canEdit": true, "key": "图表 url", "label": "图表 url", "description": "可用使用markdown格式展示图片，如：![图片](url)", "value": ["ws0DFKJnCPhk", "bzaYjKyQFOw2"], "isToolOutput": true, "required": true}], "outputs": []}, {"nodeId": "ws0DFKJnCPhk", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1264.2009472531117, "y": -455.0773486762623}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "common:core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "valueDesc": "", "debugLabel": "", "toolDescription": "", "value": "drawing/baseChart"}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\r\n  \"title\": \"{{$pluginInput.title$}}\",\r\n  \"xAxis\": {{$pluginInput.xAxis$}},\r\n  \"yAxis\": {{$pluginInput.yAxis$}},\r\n  \"chartType\": \"{{$pluginInput.chartType$}}\"\r\n}", "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}], "outputs": [{"id": "error", "key": "error", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "workflow:raw_response", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "valueDesc": "", "description": ""}, {"id": "bzaYjKyQFOw2", "valueType": "string", "type": "dynamic", "key": "result", "label": "result"}]}], "edges": [{"source": "pluginInput", "target": "ws0DFKJnCPhk", "sourceHandle": "pluginInput-source-right", "targetHandle": "ws0DFKJnCPhk-target-left"}, {"source": "ws0DFKJnCPhk", "target": "pluginOutput", "sourceHandle": "ws0DFKJnCPhk-source-right", "targetHandle": "pluginOutput-target-left"}], "chatConfig": {"welcomeText": "", "variables": [], "questionGuide": {"open": false}, "ttsConfig": {"type": "web"}, "whisperConfig": {"open": false, "autoSend": false, "autoTTSResponse": false}, "chatInputGuide": {"open": false, "textList": [], "customUrl": ""}, "instruction": "数据源配置，支持主流数据库配置", "_id": "670a23b31957c5b9899b4a4d"}}}