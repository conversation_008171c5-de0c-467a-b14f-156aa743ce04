{"author": "collin", "name": "流程等待", "avatar": "core/workflow/template/sleep", "intro": "让工作流等待指定时间后运行", "showStatus": true, "weight": 1, "isTool": true, "templateType": "tools", "workflow": {"nodes": [{"nodeId": "pluginInput", "name": "workflow:template.plugin_start", "intro": "workflow:intro_plugin_input", "avatar": "core/workflow/template/workflowStart", "flowNodeType": "pluginInput", "showStatus": false, "position": {"x": 627.6352390819724, "y": -165.05298493910118}, "version": "481", "inputs": [{"renderTypeList": ["numberInput", "reference"], "selectedTypeIndex": 0, "valueType": "number", "canEdit": true, "key": "延迟时长", "label": "延迟时长", "description": "需要暂停的时间，单位毫秒", "defaultValue": 1000, "list": [{"label": "", "value": ""}], "maxFiles": 5, "canSelectFile": true, "canSelectImg": true, "required": true, "toolDescription": "需要暂停的时间，单位毫秒", "max": 300000, "min": 1}], "outputs": [{"id": "ms", "valueType": "number", "key": "延迟时长", "label": "延迟时长", "type": "hidden"}]}, {"nodeId": "pluginOutput", "name": "common:core.module.template.self_output", "intro": "workflow:intro_custom_plugin_output", "avatar": "core/workflow/template/pluginOutput", "flowNodeType": "pluginOutput", "showStatus": false, "position": {"x": 1921.839722563351, "y": -160.05298493910115}, "version": "481", "inputs": [{"renderTypeList": ["reference"], "valueType": "any", "canEdit": true, "key": "result", "label": "result", "isToolOutput": true, "description": "", "required": true, "value": ["zCJC6zw7c14i", "httpRawResponse"]}], "outputs": []}, {"nodeId": "pluginConfig", "name": "common:core.module.template.system_config", "intro": "", "avatar": "core/workflow/template/systemConfig", "flowNodeType": "pluginConfig", "position": {"x": 184.66337662472682, "y": -216.05298493910115}, "version": "4811", "inputs": [], "outputs": []}, {"nodeId": "zCJC6zw7c14i", "name": "HTTP 请求", "intro": "可以发出一个 HTTP 请求，实现更为复杂的操作（联网搜索、数据库查询等）", "avatar": "core/workflow/template/httpRequest", "flowNodeType": "httpRequest468", "showStatus": true, "position": {"x": 1154.4041630064592, "y": -455.0529849391012}, "version": "481", "inputs": [{"key": "system_addInputParam", "renderTypeList": ["addInputParam"], "valueType": "dynamic", "label": "", "required": false, "description": "common:core.module.input.description.HTTP Dynamic Input", "customInputConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": true}, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpMethod", "renderTypeList": ["custom"], "valueType": "string", "label": "", "value": "POST", "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpTimeout", "renderTypeList": ["custom"], "valueType": "number", "label": "", "value": 30, "min": 5, "max": 600, "required": true, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpReqUrl", "renderTypeList": ["hidden"], "valueType": "string", "label": "", "description": "common:core.module.input.description.Http Request Url", "placeholder": "https://api.ai.com/getInventory", "required": false, "value": "delay", "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpHeader", "renderTypeList": ["custom"], "valueType": "any", "value": [], "label": "", "description": "common:core.module.input.description.Http Request Header", "placeholder": "common:core.module.input.description.Http Request Header", "required": false, "valueDesc": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpParams", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpJsonBody", "renderTypeList": ["hidden"], "valueType": "any", "value": "{\n\"ms\": {{$pluginInput.ms$}}\n}", "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpFormBody", "renderTypeList": ["hidden"], "valueType": "any", "value": [], "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}, {"key": "system_httpContentType", "renderTypeList": ["hidden"], "valueType": "string", "value": "json", "label": "", "required": false, "valueDesc": "", "description": "", "debugLabel": "", "toolDescription": ""}], "outputs": [{"id": "error", "key": "error", "label": "workflow:request_error", "description": "HTTP请求错误信息，成功时返回空", "valueType": "object", "type": "static"}, {"id": "httpRawResponse", "key": "httpRawResponse", "required": true, "label": "workflow:raw_response", "description": "HTTP请求的原始响应。只能接受字符串或JSON类型响应数据。", "valueType": "any", "type": "static"}, {"id": "system_addOutputParam", "key": "system_addOutputParam", "type": "dynamic", "valueType": "dynamic", "label": "输出字段提取", "customFieldConfig": {"selectValueTypeList": ["string", "number", "boolean", "object", "arrayString", "arrayNumber", "arrayBoolean", "arrayObject", "arrayAny", "any", "chatHistory", "datasetQuote", "dynamic", "selectApp", "selectDataset"], "showDescription": false, "showDefaultValue": false}, "description": "可以通过 JSONPath 语法来提取响应值中的指定字段", "valueDesc": ""}]}], "edges": [{"source": "pluginInput", "target": "zCJC6zw7c14i", "sourceHandle": "pluginInput-source-right", "targetHandle": "zCJC6zw7c14i-target-left"}, {"source": "zCJC6zw7c14i", "target": "pluginOutput", "sourceHandle": "zCJC6zw7c14i-source-right", "targetHandle": "pluginOutput-target-left"}], "chatConfig": {"welcomeText": ""}}}